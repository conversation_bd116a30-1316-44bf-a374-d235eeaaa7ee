import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

interface Service {
  title: string;
  description: string;
  icon: string;
  color: string;
}

interface ServiceCategory {
  name: string;
  icon: string;
  services: Service[];
}

@Component({
  selector: 'app-services',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatTabsModule, MatIconModule, MatButtonModule],
  template: `
    <div class="services-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Engineering Business Momentum Through Technology</h1>
          <p class="hero-subtitle">
            Transform your business challenges into competitive advantages with our integrated technology solutions.
            From process bottlenecks to competitive breakthroughs, we engineer solutions that deliver measurable results.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">40%</span>
              <span class="stat-label">Average Efficiency Increase</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">System Uptime Achieved</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$2M+</span>
              <span class="stat-label">Annual Savings Delivered</span>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Services Section -->
      <div class="ai-services-section">
        <div class="container">
          <h2>AI/ML Solutions: Unlock Your Data's Hidden Value</h2>
          <p class="section-subtitle">
            Transform your business processes with AI solutions that deliver measurable ROI.
            Increase operational efficiency by up to 40%, reduce manual processing time, and gain predictive insights for better decision making.
          </p>
          <div class="value-proposition">
            <div class="value-item">
              <mat-icon>trending_up</mat-icon>
              <h4>Increase Efficiency</h4>
              <p>Automate complex workflows and reduce manual processing by 40%</p>
            </div>
            <div class="value-item">
              <mat-icon>insights</mat-icon>
              <h4>Predictive Analytics</h4>
              <p>Make data-driven decisions with AI-powered forecasting and insights</p>
            </div>
            <div class="value-item">
              <mat-icon>security</mat-icon>
              <h4>Risk Reduction</h4>
              <p>Identify potential issues before they impact your business operations</p>
            </div>
          </div>

          <mat-tab-group class="service-tabs" animationDuration="300ms">
            @for (category of aiServiceCategories; track category.name) {
              <mat-tab [label]="category.name">
                <div class="tab-content">
                  <div class="services-grid">
                    @for (service of category.services; track service.title) {
                      <mat-card class="service-card" [style.border-left-color]="service.color">
                        <mat-card-header>
                          <mat-icon [style.color]="service.color" mat-card-avatar>{{ service.icon }}</mat-icon>
                          <mat-card-title>{{ service.title }}</mat-card-title>
                        </mat-card-header>
                        <mat-card-content>
                          <p>{{ service.description }}</p>
                        </mat-card-content>
                      </mat-card>
                    }
                  </div>
                </div>
              </mat-tab>
            }
          </mat-tab-group>
        </div>
      </div>

      <!-- Traditional Services Section -->
      <div class="traditional-services-section">
        <div class="container">
          <h2>Additional Services</h2>
          <div class="services-grid">
            @for (service of traditionalServices; track service.title) {
              <mat-card class="service-card" [style.border-left-color]="service.color">
                <mat-card-header>
                  <mat-icon [style.color]="service.color" mat-card-avatar>{{ service.icon }}</mat-icon>
                  <mat-card-title>{{ service.title }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ service.description }}</p>
                </mat-card-content>
                <mat-card-actions>
                  <button mat-button color="primary">Learn More</button>
                </mat-card-actions>
              </mat-card>
            }
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>Let's discuss how our services can help you achieve your goals.</p>
          <button mat-raised-button color="primary" class="cta-button">
            Get Started Today
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .services-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 5rem 0;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(77, 10, 255, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
      pointer-events: none;
    }

    .hero-section h1 {
      font-size: 3.5rem;
      margin-bottom: 1.5rem;
      font-weight: 800;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 2;
    }

    .hero-subtitle {
      font-size: 1.3rem;
      max-width: 800px;
      margin: 0 auto 3rem auto;
      opacity: 0.95;
      line-height: 1.6;
      position: relative;
      z-index: 2;
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      max-width: 800px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }

    .stat-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      color: #06B6D4;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .ai-services-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .ai-services-section h2 {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 2.8rem;
      font-weight: 800;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 3rem;
      font-size: 1.2rem;
      color: #666;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .value-proposition {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;
      max-width: 1000px;
      margin-left: auto;
      margin-right: auto;
    }

    .value-item {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .value-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .value-item mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #0607E1;
      margin-bottom: 1rem;
    }

    .value-item h4 {
      font-size: 1.3rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1rem;
    }

    .value-item p {
      color: #666;
      line-height: 1.6;
    }

    .service-tabs {
      margin-top: 2rem;
    }

    .tab-content {
      padding: 2rem 0;
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .service-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-left: 4px solid #0607E1;
      height: 100%;
    }

    .service-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .service-card mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .service-card mat-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
    }

    .service-card p {
      color: #666;
      line-height: 1.6;
    }

    .traditional-services-section {
      padding: 4rem 0;
    }

    .traditional-services-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-button {
      font-size: 1.1rem;
      padding: 12px 32px;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .services-grid {
        grid-template-columns: 1fr;
      }

      .service-tabs {
        font-size: 0.9rem;
      }
    }
  `]
})
export class ServicesComponent {
  aiServiceCategories: ServiceCategory[] = [
    {
      name: '🧠 AI Strategy & Consulting',
      icon: 'psychology',
      services: [
        {
          title: 'AI Strategy and Consulting',
          description: 'Strategic guidance for AI adoption and implementation across your organization.',
          icon: 'lightbulb',
          color: '#0607E1'
        },
        {
          title: 'AI Readiness Assessment',
          description: 'Comprehensive evaluation of your infrastructure and data maturity for AI implementation.',
          icon: 'assessment',
          color: '#0607E1'
        },
        {
          title: 'Use Case Identification',
          description: 'Business-aligned AI use case prioritization and roadmap development.',
          icon: 'search',
          color: '#0607E1'
        }
      ]
    },
    {
      name: '🤖 Custom AI Development',
      icon: 'smart_toy',
      services: [
        {
          title: 'Machine Learning Solutions',
          description: 'Predictive analytics and recommendation engines tailored to your business needs.',
          icon: 'trending_up',
          color: '#4D0AFF'
        },
        {
          title: 'Computer Vision',
          description: 'Advanced image recognition and video analysis solutions.',
          icon: 'visibility',
          color: '#06B6D4'
        },
        {
          title: 'Natural Language Processing',
          description: 'Intelligent chatbots, text analysis, and language understanding systems.',
          icon: 'chat',
          color: '#10B981'
        },
        {
          title: 'Generative AI Solutions',
          description: 'Content generation, code generation, and creative AI applications.',
          icon: 'auto_awesome',
          color: '#4D0AFF'
        }
      ]
    },
    {
      name: '🔄 AI Integration',
      icon: 'integration_instructions',
      services: [
        {
          title: 'AI Integration Services',
          description: 'Seamless integration of AI capabilities into existing business systems.',
          icon: 'link',
          color: '#F59E0B'
        },
        {
          title: 'Cloud AI Implementation',
          description: 'Scalable cloud-based AI solutions using leading platforms.',
          icon: 'cloud',
          color: '#8B5CF6'
        }
      ]
    },
    {
      name: '📊 Data & MLOps',
      icon: 'data_usage',
      services: [
        {
          title: 'Data Engineering',
          description: 'Data collection, cleaning, preprocessing, and pipeline development.',
          icon: 'storage',
          color: '#EF4444'
        },
        {
          title: 'MLOps Services',
          description: 'ML model CI/CD, monitoring, and lifecycle management.',
          icon: 'settings',
          color: '#EF4444'
        }
      ]
    },
    {
      name: '💡 Packaged Solutions',
      icon: 'inventory',
      services: [
        {
          title: 'Industry-Specific AI Solutions',
          description: 'Pre-built AI solutions tailored for specific industries and use cases.',
          icon: 'business',
          color: '#10B981'
        },
        {
          title: 'AI-Powered Automation',
          description: 'Task automation and workflow optimization using AI technologies.',
          icon: 'automation',
          color: '#F59E0B'
        }
      ]
    }
  ];

  traditionalServices: Service[] = [
    {
      title: 'Custom Software Development',
      description: 'Bespoke software solutions designed to meet your specific business requirements and objectives.',
      icon: 'code',
      color: '#F59E0B'
    },
    {
      title: 'Web & Mobile Applications',
      description: 'Modern, responsive web applications and mobile apps built with cutting-edge technologies.',
      icon: 'devices',
      color: '#06B6D4'
    },
    {
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure, migration services, and cloud-native application development.',
      icon: 'cloud',
      color: '#8B5CF6'
    },
    {
      title: 'Digital Marketing & SEO',
      description: 'Comprehensive digital marketing strategies and search engine optimization services.',
      icon: 'campaign',
      color: '#10B981'
    },
    {
      title: 'IT Consulting & Outsourcing',
      description: 'Expert IT consulting services and reliable outsourcing solutions for your technology needs.',
      icon: 'support_agent',
      color: '#EF4444'
    },
    {
      title: 'SAP Data Management',
      description: 'Comprehensive SAP data management solutions and enterprise system integration.',
      icon: 'account_tree',
      color: '#0607E1'
    }
  ];
}
