import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishDate: Date;
  readTime: number;
  tags: string[];
  imageUrl: string;
  featured: boolean;
}

@Component({
  selector: 'app-blog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule
  ],
  template: `
    <div class="blog-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Tech Insights & Innovation</h1>
          <p class="hero-subtitle">
            Stay updated with the latest trends in AI, technology, and digital transformation.
            Insights from our experts to help you navigate the digital landscape.
          </p>

          <!-- Search Bar -->
          <div class="search-section">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search articles...</mat-label>
              <input matInput [(ngModel)]="searchTerm" (input)="filterPosts()" placeholder="Enter keywords">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
      </div>

      <!-- Featured Post -->
      @if (featuredPost) {
        <div class="featured-section">
          <div class="container">
            <h2>Featured Article</h2>
            <mat-card class="featured-card">
              <div class="featured-content">
                <div class="featured-image">
                  <img [src]="featuredPost.imageUrl" [alt]="featuredPost.title" loading="lazy">
                </div>
                <div class="featured-text">
                  <div class="post-meta">
                    <span class="author">{{ featuredPost.author }}</span>
                    <span class="date">{{ featuredPost.publishDate | date:'MMM dd, yyyy' }}</span>
                    <span class="read-time">{{ featuredPost.readTime }} min read</span>
                  </div>
                  <h3>{{ featuredPost.title }}</h3>
                  <p>{{ featuredPost.excerpt }}</p>
                  <div class="post-tags">
                    @for (tag of featuredPost.tags; track tag) {
                      <mat-chip>{{ tag }}</mat-chip>
                    }
                  </div>
                  <button mat-raised-button color="primary">Read More</button>
                </div>
              </div>
            </mat-card>
          </div>
        </div>
      }

      <!-- Blog Posts Grid -->
      <div class="posts-section">
        <div class="container">
          <div class="section-header">
            <h2>Latest Articles</h2>
            <div class="filter-tags">
              <mat-chip-set>
                <mat-chip (click)="filterByTag('all')" [class.selected]="selectedTag === 'all'">
                  All
                </mat-chip>
                @for (tag of allTags; track tag) {
                  <mat-chip (click)="filterByTag(tag)" [class.selected]="selectedTag === tag">
                    {{ tag }}
                  </mat-chip>
                }
              </mat-chip-set>
            </div>
          </div>

          <div class="posts-grid">
            @for (post of filteredPosts; track post.id) {
              <mat-card class="post-card">
                <div class="post-image">
                  <img [src]="post.imageUrl" [alt]="post.title" loading="lazy">
                </div>
                <mat-card-content>
                  <div class="post-meta">
                    <span class="author">{{ post.author }}</span>
                    <span class="date">{{ post.publishDate | date:'MMM dd, yyyy' }}</span>
                    <span class="read-time">{{ post.readTime }} min read</span>
                  </div>
                  <h3>{{ post.title }}</h3>
                  <p>{{ post.excerpt }}</p>
                  <div class="post-tags">
                    @for (tag of post.tags.slice(0, 3); track tag) {
                      <mat-chip>{{ tag }}</mat-chip>
                    }
                  </div>
                </mat-card-content>
                <mat-card-actions>
                  <button mat-button color="primary">Read More</button>
                  <button mat-icon-button>
                    <mat-icon>share</mat-icon>
                  </button>
                  <button mat-icon-button>
                    <mat-icon>bookmark_border</mat-icon>
                  </button>
                </mat-card-actions>
              </mat-card>
            }
          </div>

          @if (filteredPosts.length === 0) {
            <div class="no-posts">
              <mat-icon>article</mat-icon>
              <h3>No articles found</h3>
              <p>Try adjusting your search or filter criteria.</p>
            </div>
          }
        </div>
      </div>

      <!-- Newsletter Section -->
      <div class="newsletter-section">
        <div class="container">
          <div class="newsletter-content">
            <h2>Stay Updated</h2>
            <p>Subscribe to our newsletter for the latest tech insights and company updates.</p>
            <div class="newsletter-form">
              <mat-form-field appearance="outline">
                <mat-label>Your email address</mat-label>
                <input matInput type="email" placeholder="Enter your email">
              </mat-form-field>
              <button mat-raised-button color="primary">Subscribe</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .blog-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto 2rem;
      opacity: 0.9;
    }

    .search-section {
      max-width: 500px;
      margin: 0 auto;
    }

    .search-field {
      width: 100%;
    }

    .search-field .mat-mdc-form-field-flex {
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }

    .featured-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .featured-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 2rem;
    }

    .featured-card {
      overflow: hidden;
    }

    .featured-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      align-items: center;
    }

    .featured-image img {
      width: 100%;
      height: 300px;
      object-fit: cover;
      border-radius: 8px;
    }

    .posts-section {
      padding: 4rem 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 3rem;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .section-header h2 {
      color: #333;
      font-size: 2.5rem;
      margin: 0;
    }

    .filter-tags mat-chip {
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .filter-tags mat-chip.selected {
      background-color: #0607E1;
      color: white;
    }

    .posts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }

    .post-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .post-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .post-image {
      height: 200px;
      overflow: hidden;
    }

    .post-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .post-meta {
      display: flex;
      gap: 1rem;
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 1rem;
      flex-wrap: wrap;
    }

    .post-meta span {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .post-card h3 {
      color: #333;
      font-size: 1.3rem;
      margin-bottom: 1rem;
      line-height: 1.4;
    }

    .post-card p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1rem;
      flex-grow: 1;
    }

    .post-tags {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
      margin-bottom: 1rem;
    }

    .post-tags mat-chip {
      font-size: 0.8rem;
      height: 24px;
    }

    .no-posts {
      text-align: center;
      padding: 4rem 0;
      color: #666;
    }

    .no-posts mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #ccc;
    }

    .newsletter-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
    }

    .newsletter-content {
      text-align: center;
      max-width: 600px;
      margin: 0 auto;
    }

    .newsletter-content h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .newsletter-content p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .newsletter-form {
      display: flex;
      gap: 1rem;
      max-width: 400px;
      margin: 0 auto;
    }

    .newsletter-form mat-form-field {
      flex: 1;
    }

    .newsletter-form .mat-mdc-form-field-flex {
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .featured-content {
        grid-template-columns: 1fr;
      }

      .section-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .posts-grid {
        grid-template-columns: 1fr;
      }

      .newsletter-form {
        flex-direction: column;
      }
    }
  `]
})
export class BlogComponent {
  searchTerm = '';
  selectedTag = 'all';

  blogPosts: BlogPost[] = [
    {
      id: 1,
      title: 'The Future of AI in Business: Trends and Opportunities',
      excerpt: 'Explore how artificial intelligence is reshaping industries and creating new opportunities for businesses to innovate and grow.',
      content: '',
      author: 'Dr. Sarah Johnson',
      publishDate: new Date('2024-01-15'),
      readTime: 8,
      tags: ['AI', 'Business', 'Innovation'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: true
    },
    {
      id: 2,
      title: 'Building Scalable Cloud Infrastructure: Best Practices',
      excerpt: 'Learn the essential strategies for designing and implementing cloud infrastructure that grows with your business needs.',
      content: '',
      author: 'Michael Chen',
      publishDate: new Date('2024-01-10'),
      readTime: 6,
      tags: ['Cloud', 'Infrastructure', 'DevOps'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: false
    },
    {
      id: 3,
      title: 'Machine Learning in Healthcare: Transforming Patient Care',
      excerpt: 'Discover how ML algorithms are revolutionizing healthcare delivery and improving patient outcomes across the globe.',
      content: '',
      author: 'Dr. Emily Rodriguez',
      publishDate: new Date('2024-01-05'),
      readTime: 10,
      tags: ['Machine Learning', 'Healthcare', 'Innovation'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: false
    },
    {
      id: 4,
      title: 'Cybersecurity in the Age of Remote Work',
      excerpt: 'Essential cybersecurity strategies to protect your organization in an increasingly distributed work environment.',
      content: '',
      author: 'James Wilson',
      publishDate: new Date('2023-12-28'),
      readTime: 7,
      tags: ['Cybersecurity', 'Remote Work', 'Security'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: false
    },
    {
      id: 5,
      title: 'Digital Transformation: A Strategic Approach',
      excerpt: 'A comprehensive guide to planning and executing successful digital transformation initiatives in your organization.',
      content: '',
      author: 'Lisa Thompson',
      publishDate: new Date('2023-12-20'),
      readTime: 12,
      tags: ['Digital Transformation', 'Strategy', 'Business'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: false
    },
    {
      id: 6,
      title: 'The Rise of Low-Code Development Platforms',
      excerpt: 'How low-code platforms are democratizing software development and accelerating digital innovation.',
      content: '',
      author: 'David Park',
      publishDate: new Date('2023-12-15'),
      readTime: 5,
      tags: ['Development', 'Low-Code', 'Innovation'],
      imageUrl: 'https://ik.imagekit.io/quadrate/assets/img/hero-image.avif',
      featured: false
    }
  ];

  filteredPosts: BlogPost[] = [];
  featuredPost: BlogPost | null = null;
  allTags: string[] = [];

  ngOnInit() {
    this.featuredPost = this.blogPosts.find(post => post.featured) || null;
    this.filteredPosts = this.blogPosts.filter(post => !post.featured);
    this.allTags = [...new Set(this.blogPosts.flatMap(post => post.tags))];
  }

  filterPosts() {
    let posts = this.blogPosts.filter(post => !post.featured);

    if (this.searchTerm) {
      posts = posts.filter(post =>
        post.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(this.searchTerm.toLowerCase()))
      );
    }

    if (this.selectedTag !== 'all') {
      posts = posts.filter(post => post.tags.includes(this.selectedTag));
    }

    this.filteredPosts = posts;
  }

  filterByTag(tag: string) {
    this.selectedTag = tag;
    this.filterPosts();
  }
}
