import { Component, OnInit, OnDestroy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { RouterLink } from '@angular/router';
import { HeroAnimationComponent } from '../../components/hero-animation/hero-animation.component';
import { InteractiveServiceCardComponent } from '../../components/interactive-service-card/interactive-service-card.component';
import { TestimonialsComponent } from '../../components/testimonials/testimonials.component';
import { ScrollAnimationService } from '../../services/scroll-animation.service';
import { SEOService } from '../../services/seo.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatCardModule, RouterLink, HeroAnimationComponent, InteractiveServiceCardComponent, TestimonialsComponent],
  template: `
    <div class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title gradient-text">
            Innovative Technology Solutions for Modern Businesses
          </h1>
          <p class="hero-subtitle">
            Quadrate Tech Solutions specializes in AI/ML-enabled technologies,
            custom software development, and digital transformation services.
          </p>
          <div class="hero-actions">
            <button mat-raised-button color="primary" routerLink="/services" class="scale-on-hover">
              Explore Our Services
            </button>
            <button mat-stroked-button routerLink="/contact" class="scale-on-hover">
              Get In Touch
            </button>
          </div>
        </div>
        <div class="hero-animation">
          <app-hero-animation></app-hero-animation>
        </div>
      </div>
    </div>

    <div class="services-preview" #servicesSection>
      <div class="container">
        <h2 class="section-title fade-in">Transform Your Business with Our Core Services</h2>
        <p class="section-subtitle fade-in">Discover how our cutting-edge solutions can drive your success</p>
        <div class="services-grid fade-in">
          @for (service of coreServices; track service.title) {
            <app-interactive-service-card
              [service]="service"
              class="stagger-child">
            </app-interactive-service-card>
          }
        </div>
      </div>
    </div>

    <!-- Testimonials Section -->
    <app-testimonials></app-testimonials>

    <div class="partnerships-section">
      <div class="container">
        <h2>Our Strategic Partnerships</h2>
        <p class="partnerships-subtitle">Certified partnerships that enable us to deliver world-class solutions</p>
        <div class="partnerships-grid">
          <div class="partnership-item">
            <div class="partnership-icon">
              <img src="https://via.placeholder.com/60x60/FF6B35/FFFFFF?text=Z" alt="ZOHO">
            </div>
            <h3>Certified ZOHO Partner</h3>
            <p>Providing comprehensive business solutions and CRM implementations</p>
          </div>
          <div class="partnership-item">
            <div class="partnership-icon">
              <img src="https://via.placeholder.com/60x60/0FAAFF/FFFFFF?text=S" alt="SAP">
            </div>
            <h3>SAP-Certified Partner</h3>
            <p>Enterprise data management and ERP solutions</p>
          </div>
          <div class="partnership-item">
            <div class="partnership-icon">
              <img src="https://via.placeholder.com/60x60/0078D4/FFFFFF?text=M" alt="Microsoft">
            </div>
            <h3>Microsoft 365 Solutions Provider</h3>
            <p>Business productivity and collaboration tools</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      min-height: 70vh;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(77, 10, 255, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
      pointer-events: none;
    }

    .hero-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
      line-height: 1.1;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
      font-size: 1.3rem;
      margin-bottom: 2.5rem;
      opacity: 0.95;
      line-height: 1.6;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .hero-actions {
      display: flex;
      gap: 1.5rem;
      flex-wrap: wrap;
    }

    .hero-animation {
      position: relative;
      z-index: 1;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .services-preview {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f0ff 100%);
      position: relative;
      overflow: hidden;
    }

    .services-preview::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .section-title {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 3rem;
      font-weight: 800;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
      z-index: 2;
    }

    .section-subtitle {
      text-align: center;
      margin-bottom: 4rem;
      font-size: 1.2rem;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      position: relative;
      z-index: 2;
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2.5rem;
      position: relative;
      z-index: 2;
    }

    .partnerships-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .partnerships-section h2 {
      text-align: center;
      margin-bottom: 1rem;
      font-size: 2.5rem;
      font-weight: 800;
      color: #333;
    }

    .partnerships-subtitle {
      text-align: center;
      margin-bottom: 4rem;
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .partnerships-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2.5rem;
    }

    .partnership-item {
      text-align: center;
      padding: 3rem 2rem;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .partnership-item:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .partnership-icon {
      margin-bottom: 1.5rem;
    }

    .partnership-icon img {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .partnership-item h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .partnership-item p {
      color: #666;
      line-height: 1.6;
    }

    @media (max-width: 768px) {
      .hero-section {
        min-height: 60vh;
        padding: 3rem 0;
      }

      .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
      }

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-subtitle {
        font-size: 1.1rem;
      }

      .hero-actions {
        justify-content: center;
        gap: 1rem;
      }

      .services-grid {
        grid-template-columns: 1fr;
      }

      .partnerships-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('servicesSection') servicesSection!: ElementRef;

  coreServices = [
    {
      title: 'AI/ML Solutions',
      description: 'Transform your business processes with advanced artificial intelligence and machine learning solutions that deliver measurable results.',
      benefits: [
        'Increase operational efficiency by up to 40%',
        'Reduce manual processing time significantly',
        'Gain predictive insights for better decision making',
        'Automate complex business workflows'
      ],
      features: ['Custom AI Models', 'Predictive Analytics', 'Process Automation', 'Data Intelligence'],
      icon: 'psychology',
      color: '#0607E1',
      gradient: 'linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%)'
    },
    {
      title: 'Custom Development',
      description: 'Get bespoke software solutions tailored to your unique business requirements and industry-specific challenges.',
      benefits: [
        'Perfectly aligned with your business needs',
        'Scalable architecture for future growth',
        'Seamless integration with existing systems',
        'Ongoing support and maintenance included'
      ],
      features: ['Web Applications', 'Mobile Apps', 'API Development', 'System Integration'],
      icon: 'code',
      color: '#F59E0B',
      gradient: 'linear-gradient(135deg, #F59E0B 0%, #EF4444 100%)'
    },
    {
      title: 'Cloud Solutions',
      description: 'Leverage scalable cloud infrastructure and migration services to modernize your IT operations and reduce costs.',
      benefits: [
        'Reduce infrastructure costs by up to 30%',
        'Improve system reliability and uptime',
        'Enable remote work capabilities',
        'Automatic scaling based on demand'
      ],
      features: ['Cloud Migration', 'Infrastructure Setup', 'DevOps', 'Security Management'],
      icon: 'cloud',
      color: '#8B5CF6',
      gradient: 'linear-gradient(135deg, #8B5CF6 0%, #06B6D4 100%)'
    }
  ];

  constructor(
    private scrollAnimationService: ScrollAnimationService,
    private seoService: SEOService
  ) {}

  ngOnInit() {
    // Update SEO for home page
    this.seoService.updateSEO(this.seoService.getHomeSEO());
  }

  ngAfterViewInit() {
    // Observe sections for scroll animations
    if (this.servicesSection) {
      this.scrollAnimationService.observeElement(this.servicesSection);
    }
  }

  ngOnDestroy() {
    // Clean up observers
    if (this.servicesSection) {
      this.scrollAnimationService.unobserveElement(this.servicesSection);
    }
  }
}
