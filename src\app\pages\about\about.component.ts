import { Component, OnInit } from '@angular/core';
import { SEOService } from '../../services/seo.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterLink } from '@angular/router';

interface TeamMember {
  name: string;
  position: string;
  bio: string;
  avatar: string;
  expertise: string[];
  certifications: string[];
}

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule, RouterLink],
  template: `
    <div class="about-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Engineering Business Momentum Since Day One</h1>
          <p class="hero-subtitle">
            At Quadrate Tech Solutions, we don't just build technology - we engineer competitive advantages.
            Our integrated approach combines deep technical expertise with strategic business insight to deliver
            solutions that accelerate your success and transform challenges into opportunities.
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">40%</span>
              <span class="stat-label">Average Efficiency Increase</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$2M+</span>
              <span class="stat-label">Annual Savings Delivered</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">System Uptime Achieved</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Company Overview -->
      <div class="overview-section">
        <div class="container">
          <div class="overview-grid">
            <div class="overview-content">
              <h2>Our Mission</h2>
              <p>
                Quadrate Tech Solutions specializes in AI/ML-enabled technologies, delivering
                comprehensive services designed to optimize processes, enhance productivity,
                and empower organizations to make data-driven decisions.
              </p>
              <p>
                We are committed to providing cutting-edge technology solutions that transform
                businesses and drive innovation across industries.
              </p>
            </div>
            <div class="overview-image">
              <img src="https://ik.imagekit.io/quadrate/assets/img/hero-image.avif"
                   alt="Our Mission"
                   loading="lazy">
            </div>
          </div>
        </div>
      </div>

      <!-- Values Section -->
      <div class="values-section">
        <div class="container">
          <h2>Our Core Values</h2>
          <div class="values-grid">
            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>lightbulb</mat-icon>
                <mat-card-title>Innovation</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We embrace cutting-edge technologies and creative solutions to solve complex business challenges.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>group</mat-icon>
                <mat-card-title>Collaboration</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We foster teamwork and partnership with our clients to achieve shared success.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>star</mat-icon>
                <mat-card-title>Excellence</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>We maintain the highest standards in everything we do, delivering quality solutions consistently.</p>
              </mat-card-content>
            </mat-card>

            <mat-card class="value-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>favorite</mat-icon>
                <mat-card-title>Customer-Centric</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <p>Our clients' success is our priority, and we tailor solutions to meet their unique needs.</p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>

      <!-- Leadership Section -->
      <div class="leadership-section">
        <div class="container">
          <h2>Leadership & Culture</h2>
          <div class="leadership-content">
            <div class="leadership-text">
              <h3>Skilled Leadership</h3>
              <p>
                Led by skilled professionals with expertise in technology and business strategy,
                our team brings years of experience in delivering innovative solutions.
              </p>

              <h3>Performance-Driven Culture</h3>
              <p>
                We maintain a strong emphasis on performance-driven HR initiatives, creating
                a supportive and rewarding workplace environment that encourages growth and innovation.
              </p>

              <h3>Our Approach</h3>
              <ul>
                <li>Focus on innovation and collaboration</li>
                <li>Customer-centric approach to solution development</li>
                <li>Continuous learning and professional development</li>
                <li>Agile methodologies and best practices</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Team Section -->
      <div class="team-section">
        <div class="container">
          <h2>Meet Our Expert Team</h2>
          <p class="team-subtitle">
            Our success is built on the expertise and dedication of our team. Each member brings deep technical knowledge
            and a commitment to delivering exceptional results that drive your business forward.
          </p>

          <div class="team-grid">
            @for (member of teamMembers; track member.name) {
              <mat-card class="team-card">
                <div class="member-avatar">
                  <img [src]="member.avatar" [alt]="member.name" loading="lazy">
                </div>

                <div class="member-info">
                  <h3>{{ member.name }}</h3>
                  <h4>{{ member.position }}</h4>
                  <p class="member-bio">{{ member.bio }}</p>

                  <div class="member-expertise">
                    <h5>Expertise:</h5>
                    <div class="expertise-tags">
                      @for (skill of member.expertise; track skill) {
                        <span class="expertise-tag">{{ skill }}</span>
                      }
                    </div>
                  </div>

                  <div class="member-certifications">
                    <h5>Certifications:</h5>
                    <div class="cert-list">
                      @for (cert of member.certifications; track cert) {
                        <span class="cert-badge">{{ cert }}</span>
                      }
                    </div>
                  </div>
                </div>
              </mat-card>
            }
          </div>
        </div>
      </div>

      <!-- Partnerships Section -->
      <div class="partnerships-section">
        <div class="container">
          <h2>Strategic Partnerships That Amplify Our Capabilities</h2>
          <p class="partnerships-subtitle">
            Our certified partnerships with industry leaders enable us to deliver world-class solutions
            and provide our clients with access to the latest technologies and best practices.
          </p>
          <div class="partnerships-grid">
            <div class="partnership-card">
              <div class="partnership-icon">
                <img src="https://via.placeholder.com/60x60/FF6B35/FFFFFF?text=Z" alt="ZOHO">
              </div>
              <h3>Certified ZOHO Partner</h3>
              <p>Comprehensive business solutions and CRM implementations that streamline your operations and boost productivity.</p>
            </div>
            <div class="partnership-card">
              <div class="partnership-icon">
                <img src="https://via.placeholder.com/60x60/0FAAFF/FFFFFF?text=S" alt="SAP">
              </div>
              <h3>SAP-Certified Partner</h3>
              <p>Enterprise data management and ERP solutions that provide real-time insights and operational excellence.</p>
            </div>
            <div class="partnership-card">
              <div class="partnership-icon">
                <img src="https://via.placeholder.com/60x60/0078D4/FFFFFF?text=M" alt="Microsoft">
              </div>
              <h3>Microsoft 365 Solutions Provider</h3>
              <p>Business productivity and collaboration tools that enable remote work and enhance team efficiency.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>
            Join the companies that have accelerated their success with our integrated technology solutions.
            Let's discuss how we can engineer your competitive advantage.
          </p>
          <div class="cta-buttons">
            <button mat-raised-button color="primary" routerLink="/contact">
              Start Your Project
            </button>
            <button mat-stroked-button routerLink="/case-studies">
              View Success Stories
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .about-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.3rem;
      max-width: 800px;
      margin: 0 auto 3rem auto;
      opacity: 0.95;
      line-height: 1.6;
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .stat-item {
      text-align: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      color: #06B6D4;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .overview-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .overview-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      align-items: center;
    }

    .overview-content h2 {
      color: #0607E1;
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }

    .overview-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 1rem;
      color: #555;
    }

    .overview-image img {
      width: 100%;
      height: auto;
      border-radius: 8px;
    }

    .values-section {
      padding: 4rem 0;
    }

    .values-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
    }

    .value-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .value-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .value-card mat-icon {
      color: #0607E1;
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .leadership-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .leadership-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .leadership-content h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    .leadership-content h3:first-child {
      margin-top: 0;
    }

    .leadership-content p {
      font-size: 1.1rem;
      line-height: 1.6;
      color: #555;
      margin-bottom: 1rem;
    }

    .leadership-content ul {
      list-style-type: none;
      padding: 0;
    }

    .leadership-content li {
      padding: 0.5rem 0;
      padding-left: 1.5rem;
      position: relative;
      color: #555;
    }

    .leadership-content li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #0607E1;
      font-weight: bold;
    }

    .team-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .team-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
    }

    .team-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .team-card {
      padding: 2rem !important;
      text-align: center;
      transition: all 0.3s ease !important;
      border-radius: 16px !important;
      background: white !important;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    }

    .team-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .member-avatar {
      width: 120px;
      height: 120px;
      margin: 0 auto 1.5rem auto;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid #0607E1;
    }

    .member-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .member-info h3 {
      color: #333;
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .member-info h4 {
      color: #0607E1;
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .member-bio {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
      font-size: 0.95rem;
    }

    .member-expertise,
    .member-certifications {
      margin-bottom: 1rem;
      text-align: left;
    }

    .member-expertise h5,
    .member-certifications h5 {
      color: #333;
      font-size: 0.9rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .expertise-tags,
    .cert-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .expertise-tag {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .cert-badge {
      background: rgba(16, 185, 129, 0.1);
      color: #10B981;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .partnerships-section {
      padding: 6rem 0;
    }

    .partnerships-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
    }

    .partnerships-subtitle {
      text-align: center;
      color: #666;
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto 4rem auto;
      line-height: 1.6;
    }

    .partnerships-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .partnership-card {
      padding: 3rem 2rem;
      background: white;
      border-radius: 16px;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .partnership-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      border-color: #0607E1;
    }

    .partnership-icon {
      margin-bottom: 1.5rem;
    }

    .partnership-icon img {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .partnership-card h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.3rem;
      font-weight: 700;
    }

    .partnership-card p {
      color: #666;
      line-height: 1.6;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .cta-buttons button {
      border-radius: 25px !important;
      padding: 0.75rem 2rem !important;
      font-weight: 600 !important;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .overview-grid {
        grid-template-columns: 1fr;
        text-align: center;
      }

      .values-grid {
        grid-template-columns: 1fr;
      }

      .partnerships-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class AboutComponent {
  teamMembers: TeamMember[] = [
    {
      name: 'M.F.M Fazrin',
      position: 'Founder & Chief Technology Officer',
      bio: 'Visionary leader with 15+ years of experience in enterprise software development and AI/ML solutions. Specializes in architecting scalable systems that drive business transformation.',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      expertise: ['AI/ML Architecture', 'Cloud Solutions', 'Enterprise Integration', 'Strategic Planning'],
      certifications: ['AWS Solutions Architect', 'Microsoft Azure Expert', 'SAP Certified', 'ZOHO Partner']
    },
    {
      name: 'Sarah Johnson',
      position: 'Lead AI/ML Engineer',
      bio: 'Expert in machine learning algorithms and data science with a track record of delivering AI solutions that increase operational efficiency by 40%+.',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      expertise: ['Machine Learning', 'Deep Learning', 'Computer Vision', 'Natural Language Processing'],
      certifications: ['TensorFlow Certified', 'AWS ML Specialty', 'Google Cloud ML Engineer']
    },
    {
      name: 'Michael Chen',
      position: 'Senior Full-Stack Developer',
      bio: 'Full-stack development specialist with expertise in modern web technologies and cloud-native applications. Passionate about creating scalable, user-centric solutions.',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      expertise: ['React/Angular', 'Node.js', 'Python', 'Microservices', 'DevOps'],
      certifications: ['AWS Developer Associate', 'Kubernetes Certified', 'Scrum Master']
    },
    {
      name: 'Emily Rodriguez',
      position: 'Cloud Solutions Architect',
      bio: 'Cloud infrastructure expert specializing in migration strategies and scalable architectures. Has helped companies reduce infrastructure costs by 30% while improving reliability.',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      expertise: ['AWS/Azure/GCP', 'Infrastructure as Code', 'Container Orchestration', 'Security'],
      certifications: ['AWS Solutions Architect Pro', 'Azure Solutions Architect', 'Terraform Certified']
    }
  ];
}
