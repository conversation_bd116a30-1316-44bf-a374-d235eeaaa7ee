import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="contact-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Contact Us</h1>
          <p class="hero-subtitle">
            Ready to transform your business with innovative technology solutions?
            Get in touch with our experts today.
          </p>
        </div>
      </div>

      <!-- Contact Content -->
      <div class="contact-content">
        <div class="container">
          <div class="contact-grid">
            <!-- Contact Form -->
            <div class="contact-form-section">
              <mat-card class="contact-form-card">
                <mat-card-header>
                  <mat-card-title>Send us a Message</mat-card-title>
                  <mat-card-subtitle>We'll get back to you within 24 hours</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Full Name</mat-label>
                        <input matInput formControlName="fullName" placeholder="Enter your full name">
                        <mat-icon matSuffix>person</mat-icon>
                        @if (contactForm.get('fullName')?.hasError('required') && contactForm.get('fullName')?.touched) {
                          <mat-error>Full name is required</mat-error>
                        }
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Email Address</mat-label>
                        <input matInput formControlName="email" type="email" placeholder="Enter your email">
                        <mat-icon matSuffix>email</mat-icon>
                        @if (contactForm.get('email')?.hasError('required') && contactForm.get('email')?.touched) {
                          <mat-error>Email is required</mat-error>
                        }
                        @if (contactForm.get('email')?.hasError('email') && contactForm.get('email')?.touched) {
                          <mat-error>Please enter a valid email</mat-error>
                        }
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Phone Number</mat-label>
                        <input matInput formControlName="phone" placeholder="Enter your phone number">
                        <mat-icon matSuffix>phone</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Company</mat-label>
                        <input matInput formControlName="company" placeholder="Enter your company name">
                        <mat-icon matSuffix>business</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Service Interest</mat-label>
                        <mat-select formControlName="serviceInterest">
                          <mat-option value="ai-ml">AI/ML Solutions</mat-option>
                          <mat-option value="custom-dev">Custom Development</mat-option>
                          <mat-option value="cloud">Cloud Solutions</mat-option>
                          <mat-option value="consulting">IT Consulting</mat-option>
                          <mat-option value="sap">SAP Solutions</mat-option>
                          <mat-option value="other">Other</mat-option>
                        </mat-select>
                        <mat-icon matSuffix>category</mat-icon>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Message</mat-label>
                        <textarea matInput formControlName="message" rows="5"
                                placeholder="Tell us about your project or requirements"></textarea>
                        @if (contactForm.get('message')?.hasError('required') && contactForm.get('message')?.touched) {
                          <mat-error>Message is required</mat-error>
                        }
                      </mat-form-field>
                    </div>

                    <div class="form-actions">
                      <button mat-raised-button color="primary" type="submit"
                              [disabled]="contactForm.invalid || isSubmitting">
                        @if (isSubmitting) {
                          Sending...
                        } @else {
                          Send Message
                        }
                      </button>
                    </div>
                  </form>
                </mat-card-content>
              </mat-card>
            </div>

            <!-- Contact Information -->
            <div class="contact-info-section">
              <div class="contact-info-card">
                <h3>Get in Touch</h3>
                <p>We're here to help you transform your business with innovative technology solutions.</p>

                <div class="contact-methods">
                  <div class="contact-method">
                    <mat-icon>email</mat-icon>
                    <div>
                      <h4>Email Us</h4>
                      <p><EMAIL></p>
                    </div>
                  </div>

                  <div class="contact-method">
                    <mat-icon>phone</mat-icon>
                    <div>
                      <h4>Call Us</h4>
                      <p>+****************</p>
                    </div>
                  </div>

                  <div class="contact-method">
                    <mat-icon>location_on</mat-icon>
                    <div>
                      <h4>Visit Us</h4>
                      <p>123 Technology Drive<br>Innovation City, IC 12345</p>
                    </div>
                  </div>

                  <div class="contact-method">
                    <mat-icon>schedule</mat-icon>
                    <div>
                      <h4>Business Hours</h4>
                      <p>Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quick Links -->
              <div class="quick-links-card">
                <h3>Quick Links</h3>
                <div class="quick-links">
                  <a href="/services" class="quick-link">
                    <mat-icon>arrow_forward</mat-icon>
                    <span>Our Services</span>
                  </a>
                  <a href="/about" class="quick-link">
                    <mat-icon>arrow_forward</mat-icon>
                    <span>About Us</span>
                  </a>
                  <a href="/pricing" class="quick-link">
                    <mat-icon>arrow_forward</mat-icon>
                    <span>Pricing</span>
                  </a>
                  <a href="/interview" class="quick-link">
                    <mat-icon>arrow_forward</mat-icon>
                    <span>Career Opportunities</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .contact-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto;
      opacity: 0.9;
    }

    .contact-content {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .contact-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 3rem;
      align-items: start;
    }

    .contact-form-card {
      height: fit-content;
    }

    .contact-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .form-row {
      width: 100%;
    }

    .full-width {
      width: 100%;
    }

    .form-actions {
      margin-top: 1rem;
      text-align: right;
    }

    .contact-info-section {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .contact-info-card,
    .quick-links-card {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .contact-info-card h3,
    .quick-links-card h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .contact-methods {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      margin-top: 2rem;
    }

    .contact-method {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
    }

    .contact-method mat-icon {
      color: #0607E1;
      margin-top: 0.2rem;
    }

    .contact-method h4 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.1rem;
    }

    .contact-method p {
      margin: 0;
      color: #666;
      line-height: 1.4;
    }

    .quick-links {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .quick-link {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #0607E1;
      text-decoration: none;
      padding: 0.5rem;
      border-radius: 4px;
      transition: background-color 0.3s ease;
    }

    .quick-link:hover {
      background-color: #f0f0ff;
    }

    .quick-link mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .contact-info-section {
        order: -1;
      }
    }
  `]
})
export class ContactComponent {
  contactForm: FormGroup;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.contactForm = this.fb.group({
      fullName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      company: [''],
      serviceInterest: [''],
      message: ['', Validators.required]
    });
  }

  onSubmit() {
    if (this.contactForm.valid) {
      this.isSubmitting = true;

      // Simulate form submission
      setTimeout(() => {
        this.isSubmitting = false;
        this.snackBar.open('Message sent successfully! We\'ll get back to you soon.', 'Close', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
        this.contactForm.reset();
      }, 2000);
    } else {
      this.snackBar.open('Please fill in all required fields correctly.', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }
}
