// Quadrate Tech Solutions - Angular App Styles
// Based on the brand colors and design system from the documentation

:host {
  // Brand Colors
  --primary-blue: #0607E1; // Chrysler Blue
  --primary-white: #FFFFFF;
  --primary-black: #000000;

  // Service Colors
  --ai-blue: #0607E1;
  --generative-purple: #4D0AFF;
  --vision-cyan: #06B6D4;
  --nlp-green: #10B981;
  --dev-orange: #F59E0B;
  --data-red: #EF4444;
  --cloud-purple: #8B5CF6;

  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Helvetica, Arial, sans-serif;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Navigation Styles
.navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--primary-blue) !important;

  .navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .navbar-brand {
    .brand-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: white;

      .logo {
        height: 40px;
        margin-right: 12px;
      }

      .brand-text {
        font-weight: bold;
        font-size: 1.2rem;
        color: white;
      }
    }
  }

  .desktop-nav {
    display: flex;
    gap: 1rem;

    .nav-link {
      color: white !important;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.active {
        background-color: rgba(255, 255, 255, 0.1);
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    @media (max-width: 768px) {
      display: none;
    }
  }

  .mobile-menu-btn {
    color: white;

    @media (min-width: 769px) {
      display: none;
    }
  }
}

// Main Content
.main-content {
  min-height: calc(100vh - 64px);
  padding-top: 2rem;
}

// Footer Styles
.footer {
  background-color: #1a1a1a;
  color: white;
  margin-top: 4rem;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 1rem 1rem;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .footer-section {
    h3, h4 {
      margin-bottom: 1rem;
      color: var(--primary-blue);
    }

    p {
      margin-bottom: 1rem;
      color: #ccc;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin-bottom: 0.5rem;

        a {
          color: #ccc;
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: var(--primary-blue);
          }
        }
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid #333;
    padding-top: 1rem;
    text-align: center;
    color: #999;
  }
}
