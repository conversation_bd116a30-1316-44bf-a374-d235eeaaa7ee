
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: [
  {
    "renderMode": 2,
    "redirectTo": "/home",
    "route": "/"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-BOR5POER.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/home"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-D7PVUTPY.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/about"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-LOUXUTWO.js",
      "chunk-3IZBLP5M.js",
      "chunk-EPGP6G5F.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/services"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-6FWD3OOC.js",
      "chunk-QOW62CD4.js",
      "chunk-4Z53IBGF.js",
      "chunk-NCZECHZ3.js",
      "chunk-EPGP6G5F.js",
      "chunk-V2VQE33L.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/blog"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-AYTAXP6B.js",
      "chunk-4Z53IBGF.js",
      "chunk-NCZECHZ3.js",
      "chunk-EPGP6G5F.js",
      "chunk-JPQ3QJGM.js",
      "chunk-V2VQE33L.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/contact"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-P3WMXKUH.js",
      "chunk-3IZBLP5M.js",
      "chunk-EPGP6G5F.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/pricing"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-J6BQSAFJ.js",
      "chunk-JPQ3QJGM.js",
      "chunk-V2VQE33L.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/interview"
  },
  {
    "renderMode": 2,
    "preload": [
      "chunk-QMTVJ5RG.js",
      "chunk-QOW62CD4.js",
      "chunk-NCZECHZ3.js",
      "chunk-EPGP6G5F.js",
      "chunk-V2VQE33L.js",
      "chunk-Q3WDI6BI.js"
    ],
    "route": "/case-studies"
  },
  {
    "renderMode": 2,
    "redirectTo": "/home",
    "route": "/**"
  }
],
  entryPointToBrowserMapping: undefined,
  assets: {
    'index.csr.html': {size: 11838, hash: '12879c809937506351cfb5c2f675a72f6ef004d17f8c59eec934c34259cd3fc6', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1262, hash: '05bf29a79d9d092f82aef06be77f2188b933c204237221a42abf5c8016bb21b9', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'home/index.html': {size: 126989, hash: 'd1da2c997b14446b0cec0013d6d5397139935a8e8367b6f76a95cb0003376d81', text: () => import('./assets-chunks/home_index_html.mjs').then(m => m.default)},
    'services/index.html': {size: 103205, hash: '1c18458c03321af91cec56b3e057d0581a338fa37fa1f3dd5ade2d39ce5d89f2', text: () => import('./assets-chunks/services_index_html.mjs').then(m => m.default)},
    'contact/index.html': {size: 136475, hash: '48ea0f1039f185a67a5f742aed9fc8ca1b63b5caadd350d2c36868bea6b0fb31', text: () => import('./assets-chunks/contact_index_html.mjs').then(m => m.default)},
    'interview/index.html': {size: 78429, hash: '8c9c4f770be88a73b65dce4c179c0955cae8797947fd88f150588a4354a8bad1', text: () => import('./assets-chunks/interview_index_html.mjs').then(m => m.default)},
    'about/index.html': {size: 80383, hash: '724e9633ec8e3a209bc49d30e5427d3267d25673993e4541a0bfe70757980a02', text: () => import('./assets-chunks/about_index_html.mjs').then(m => m.default)},
    'pricing/index.html': {size: 103207, hash: 'def2c02dda9ce18494ca82d865f045735813cf2f2661b3fed6d95451bd99d63e', text: () => import('./assets-chunks/pricing_index_html.mjs').then(m => m.default)},
    'blog/index.html': {size: 179427, hash: '8d73d387a46aae3eb10e1e281816ded83d68dcdb2ab30163fc938125492a7cfd', text: () => import('./assets-chunks/blog_index_html.mjs').then(m => m.default)},
    'case-studies/index.html': {size: 116676, hash: '742716191086999239a1ce5218bbf87e108d02be0c5c12daac5434c71242a029', text: () => import('./assets-chunks/case-studies_index_html.mjs').then(m => m.default)},
    'styles-O673MID2.css': {size: 13540, hash: 'yWIyYqZJNck', text: () => import('./assets-chunks/styles-O673MID2_css.mjs').then(m => m.default)}
  },
};
