import{$ as d,A as W,C as Z,Ca as Pt,D as ie,Da as x,F as Ot,Fa as wr,G as oe,H as Be,I as G,Ia as br,J as Mt,Ja as xt,K as hr,Ka as Ir,<PERSON> as dr,Ma as we,Nb as _r,P as fr,Pb as Lr,Q as U,Qa as X,R as Dt,Sb as jr,T as y,Ta as Ar,U as S,Ua as Tr,Ub as kr,Va as Er,W as C,Wa as Or,Xa as Mr,Xb as We,Y as pr,Yb as Ge,Z as D,_ as He,a as l,aa as gr,b as O,ba as se,bc as $r,ca as P,cc as Qe,d as or,e as sr,f as ar,fb as Dr,g as It,h as At,ha as vr,i as z,j as M,ja as mr,k as F,ka as Ve,l as I,lb as Nr,m as h,mc as zr,n as Se,na as Rr,o as ur,oa as yr,p as cr,pa as Ce,q as v,qa as Nt,qb as Ur,r as Tt,ra as Sr,s as A,sa as Ut,sb as Pr,t as Et,tb as xr,u as lr,v as qe,ya as Cr}from"./chunk-QPLGU5OF.js";var f="primary",je=Symbol("RouteTitle"),$t=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function te(t){return new $t(t)}function Qr(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=t[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function xn(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!j(t[e],n[e]))return!1;return!0}function j(t,n){let e=t?zt(t):void 0,r=n?zt(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let o=0;o<e.length;o++)if(i=e[o],!Kr(t[i],n[i]))return!1;return!0}function zt(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Kr(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,o)=>r[o]===i)}else return t===n}function Yr(t){return t.length>0?t[t.length-1]:null}function H(t){return ur(t)?t:Tr(t)?I(Promise.resolve(t)):h(t)}var _n={exact:Xr,subset:Jr},Zr={exact:Ln,subset:jn,ignored:()=>!0};function Fr(t,n,e){return _n[e.paths](t.root,n.root,e.matrixParams)&&Zr[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function Ln(t,n){return j(t,n)}function Xr(t,n,e){if(!J(t.segments,n.segments)||!Ze(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!Xr(t.children[r],n.children[r],e))return!1;return!0}function jn(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>Kr(t[e],n[e]))}function Jr(t,n,e){return en(t,n,n.segments,e)}function en(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!J(i,e)||n.hasChildren()||!Ze(i,e,r))}else if(t.segments.length===e.length){if(!J(t.segments,e)||!Ze(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!Jr(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),o=e.slice(t.segments.length);return!J(t.segments,i)||!Ze(t.segments,i,r)||!t.children[f]?!1:en(t.children[f],n,o,r)}}function Ze(t,n,e){return n.every((r,i)=>Zr[e](t[i].parameters,r.parameters))}var $=class{root;queryParams;fragment;_queryParamMap;constructor(n=new p([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=te(this.queryParams),this._queryParamMap}toString(){return zn.serialize(this)}},p=class{segments;children;parent=null;constructor(n,e){this.segments=n,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Xe(this)}},Q=class{path;parameters;_parameterMap;constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=te(this.parameters),this._parameterMap}toString(){return rn(this)}};function kn(t,n){return J(t,n)&&t.every((e,r)=>j(e.parameters,n[r].parameters))}function J(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function $n(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===f&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==f&&(e=e.concat(n(i,r)))}),e}var ke=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:()=>new re,providedIn:"root"})}return t})(),re=class{parse(n){let e=new qt(n);return new $(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${be(n.root,!0)}`,r=Bn(n.queryParams),i=typeof n.fragment=="string"?`#${Fn(n.fragment)}`:"";return`${e}${r}${i}`}},zn=new re;function Xe(t){return t.segments.map(n=>rn(n)).join("/")}function be(t,n){if(!t.hasChildren())return Xe(t);if(n){let e=t.children[f]?be(t.children[f],!1):"",r=[];return Object.entries(t.children).forEach(([i,o])=>{i!==f&&r.push(`${i}:${be(o,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=$n(t,(r,i)=>i===f?[be(t.children[f],!1)]:[`${i}:${be(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[f]!=null?`${Xe(t)}/${e[0]}`:`${Xe(t)}/(${e.join("//")})`}}function tn(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ke(t){return tn(t).replace(/%3B/gi,";")}function Fn(t){return encodeURI(t)}function Ft(t){return tn(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Je(t){return decodeURIComponent(t)}function qr(t){return Je(t.replace(/\+/g,"%20"))}function rn(t){return`${Ft(t.path)}${qn(t.parameters)}`}function qn(t){return Object.entries(t).map(([n,e])=>`;${Ft(n)}=${Ft(e)}`).join("")}function Bn(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${Ke(e)}=${Ke(i)}`).join("&"):`${Ke(e)}=${Ke(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var Hn=/^[^\/()?;#]+/;function _t(t){let n=t.match(Hn);return n?n[0]:""}var Vn=/^[^\/()?;=#]+/;function Wn(t){let n=t.match(Vn);return n?n[0]:""}var Gn=/^[^=?&#]+/;function Qn(t){let n=t.match(Gn);return n?n[0]:""}var Kn=/^[^&#]+/;function Yn(t){let n=t.match(Kn);return n?n[0]:""}var qt=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new p([],{}):new p([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[f]=new p(n,e)),r}parseSegment(){let n=_t(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new S(4009,!1);return this.capture(n),new Q(Je(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=Wn(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=_t(this.remaining);i&&(r=i,this.capture(r))}n[Je(e)]=Je(r)}parseQueryParam(n){let e=Qn(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let s=Yn(this.remaining);s&&(r=s,this.capture(r))}let i=qr(e),o=qr(r);if(n.hasOwnProperty(i)){let s=n[i];Array.isArray(s)||(s=[s],n[i]=s),s.push(o)}else n[i]=o}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=_t(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new S(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):n&&(o=f);let s=this.parseChildren();e[o]=Object.keys(s).length===1?s[f]:new p([],s),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new S(4011,!1)}};function nn(t){return t.segments.length>0?new p([],{[f]:t}):t}function on(t){let n={};for(let[r,i]of Object.entries(t.children)){let o=on(i);if(r===f&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))n[s]=a;else(o.segments.length>0||o.hasChildren())&&(n[r]=o)}let e=new p(t.segments,n);return Zn(e)}function Zn(t){if(t.numberOfChildren===1&&t.children[f]){let n=t.children[f];return new p(t.segments.concat(n.segments),n.children)}return t}function K(t){return t instanceof $}function sn(t,n,e=null,r=null){let i=an(t);return un(i,n,e,r)}function an(t){let n;function e(o){let s={};for(let u of o.children){let c=e(u);s[u.outlet]=c}let a=new p(o.url,s);return o===t&&(n=a),a}let r=e(t.root),i=nn(r);return n??i}function un(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return Lt(i,i,i,e,r);let o=Xn(n);if(o.toRoot())return Lt(i,i,new p([],{}),e,r);let s=Jn(o,i,t),a=s.processChildren?Ae(s.segmentGroup,s.index,o.commands):ln(s.segmentGroup,s.index,o.commands);return Lt(i,s.segmentGroup,a,e,r)}function et(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function Oe(t){return typeof t=="object"&&t!=null&&t.outlets}function Lt(t,n,e,r,i){let o={};r&&Object.entries(r).forEach(([u,c])=>{o[u]=Array.isArray(c)?c.map(g=>`${g}`):`${c}`});let s;t===n?s=e:s=cn(t,n,e);let a=nn(on(s));return new $(a,o,i)}function cn(t,n,e){let r={};return Object.entries(t.children).forEach(([i,o])=>{o===n?r[i]=e:r[i]=cn(o,n,e)}),new p(t.segments,r)}var tt=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&et(r[0]))throw new S(4003,!1);let i=r.find(Oe);if(i&&i!==Yr(r))throw new S(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Xn(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new tt(!0,0,t);let n=0,e=!1,r=t.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([u,c])=>{a[u]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,u)=>{u==0&&a==="."||(u==0&&a===""?e=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new tt(e,n,r)}var ce=class{segmentGroup;processChildren;index;constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function Jn(t,n,e){if(t.isAbsolute)return new ce(n,!0,0);if(!e)return new ce(n,!1,NaN);if(e.parent===null)return new ce(e,!0,0);let r=et(t.commands[0])?0:1,i=e.segments.length-1+r;return ei(e,i,t.numberOfDoubleDots)}function ei(t,n,e){let r=t,i=n,o=e;for(;o>i;){if(o-=i,r=r.parent,!r)throw new S(4005,!1);i=r.segments.length}return new ce(r,!1,i-o)}function ti(t){return Oe(t[0])?t[0].outlets:{[f]:t}}function ln(t,n,e){if(t??=new p([],{}),t.segments.length===0&&t.hasChildren())return Ae(t,n,e);let r=ri(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let o=new p(t.segments.slice(0,r.pathIndex),{});return o.children[f]=new p(t.segments.slice(r.pathIndex),t.children),Ae(o,0,i)}else return r.match&&i.length===0?new p(t.segments,{}):r.match&&!t.hasChildren()?Bt(t,n,e):r.match?Ae(t,0,i):Bt(t,n,e)}function Ae(t,n,e){if(e.length===0)return new p(t.segments,{});{let r=ti(e),i={};if(Object.keys(r).some(o=>o!==f)&&t.children[f]&&t.numberOfChildren===1&&t.children[f].segments.length===0){let o=Ae(t.children[f],n,e);return new p(t.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=ln(t.children[o],n,s))}),Object.entries(t.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new p(t.segments,i)}}function ri(t,n,e){let r=0,i=n,o={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return o;let s=t.segments[i],a=e[r];if(Oe(a))break;let u=`${a}`,c=r<e.length-1?e[r+1]:null;if(i>0&&u===void 0)break;if(u&&c&&typeof c=="object"&&c.outlets===void 0){if(!Hr(u,c,s))return o;r+=2}else{if(!Hr(u,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function Bt(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let o=e[i];if(Oe(o)){let u=ni(o.outlets);return new p(r,u)}if(i===0&&et(e[0])){let u=t.segments[n];r.push(new Q(u.path,Br(e[0]))),i++;continue}let s=Oe(o)?o.outlets[f]:`${o}`,a=i<e.length-1?e[i+1]:null;s&&a&&et(a)?(r.push(new Q(s,Br(a))),i+=2):(r.push(new Q(s,{})),i++)}return new p(r,{})}function ni(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=Bt(new p([],{}),0,r))}),n}function Br(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function Hr(t,n,e){return t==e.path&&j(n,e.parameters)}var Te="imperative",m=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(m||{}),E=class{id;url;constructor(n,e){this.id=n,this.url=e}},ne=class extends E{type=m.NavigationStart;navigationTrigger;restoredState;constructor(n,e,r="imperative",i=null){super(n,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},L=class extends E{urlAfterRedirects;type=m.NavigationEnd;constructor(n,e,r){super(n,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},w=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t[t.Aborted=4]="Aborted",t}(w||{}),Me=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Me||{}),k=class extends E{reason;code;type=m.NavigationCancel;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},q=class extends E{reason;code;type=m.NavigationSkipped;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}},he=class extends E{error;target;type=m.NavigationError;constructor(n,e,r,i){super(n,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},De=class extends E{urlAfterRedirects;state;type=m.RoutesRecognized;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},rt=class extends E{urlAfterRedirects;state;type=m.GuardsCheckStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},nt=class extends E{urlAfterRedirects;state;shouldActivate;type=m.GuardsCheckEnd;constructor(n,e,r,i,o){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},it=class extends E{urlAfterRedirects;state;type=m.ResolveStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ot=class extends E{urlAfterRedirects;state;type=m.ResolveEnd;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},st=class{route;type=m.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},at=class{route;type=m.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ut=class{snapshot;type=m.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ct=class{snapshot;type=m.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},lt=class{snapshot;type=m.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ht=class{snapshot;type=m.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Ne=class{},de=class{url;navigationBehaviorOptions;constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function ii(t){return!(t instanceof Ne)&&!(t instanceof de)}function oi(t,n){return t.providers&&!t._injector&&(t._injector=xt(t.providers,n,`Route: ${t.path}`)),t._injector??n}function _(t){return t.outlet||f}function si(t,n){let e=t.filter(r=>_(r)===n);return e.push(...t.filter(r=>_(r)!==n)),e}function ge(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var dt=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return ge(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new ve(this.rootInjector)}},ve=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new dt(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||t)(He(se))};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ft=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=Ht(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=Ht(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=Vt(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return Vt(n,this._root).map(e=>e.value)}};function Ht(t,n){if(t===n.value)return n;for(let e of n.children){let r=Ht(t,e);if(r)return r}return null}function Vt(t,n){if(t===n.value)return[n];for(let e of n.children){let r=Vt(t,e);if(r.length)return r.unshift(n),r}return[]}var T=class{value;children;constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function ue(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var Ue=class extends ft{snapshot;constructor(n,e){super(n),this.snapshot=e,Jt(this,n)}toString(){return this.snapshot.toString()}};function hn(t){let n=ai(t),e=new M([new Q("",{})]),r=new M({}),i=new M({}),o=new M({}),s=new M(""),a=new B(e,r,o,s,i,f,t,n.root);return a.snapshot=n.root,new Ue(new T(a,[]),n)}function ai(t){let n={},e={},r={},i="",o=new ee([],n,r,i,e,f,t,null,{});return new Pe("",new T(o,[]))}var B=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,e,r,i,o,s,a,u){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=u,this.title=this.dataSubject?.pipe(v(c=>c[je]))??h(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(v(n=>te(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(v(n=>te(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function pt(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:l(l({},n.params),t.params),data:l(l({},n.data),t.data),resolve:l(l(l(l({},t.data),n.data),i?.data),t._resolvedData)}:r={params:l({},t.params),data:l({},t.data),resolve:l(l({},t.data),t._resolvedData??{})},i&&fn(i)&&(r.resolve[je]=i.title),r}var ee=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[je]}constructor(n,e,r,i,o,s,a,u,c){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=te(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=te(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},Pe=class extends ft{url;constructor(n,e){super(e),this.url=n,Jt(this,e)}toString(){return dn(this._root)}};function Jt(t,n){n.value._routerState=t,n.children.forEach(e=>Jt(t,e))}function dn(t){let n=t.children.length>0?` { ${t.children.map(dn).join(", ")} } `:"";return`${t.value}${n}`}function jt(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,j(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),j(n.params,e.params)||t.paramsSubject.next(e.params),xn(n.url,e.url)||t.urlSubject.next(e.url),j(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Wt(t,n){let e=j(t.params,n.params)&&kn(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||Wt(t.parent,n.parent))}function fn(t){return typeof t.title=="string"||t.title===null}var pn=new D(""),er=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=f;activateEvents=new X;deactivateEvents=new X;attachEvents=new X;detachEvents=new X;routerOutletData=kr(void 0);parentContexts=d(ve);location=d(wr);changeDetector=d(We);inputBinder=d(Rt,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new S(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new S(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new S(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new S(4013,!1);this._activatedRoute=e;let i=this.location,s=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,u=new Gt(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(s,{index:i.length,injector:u,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||t)};static \u0275dir=we({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ce]})}return t})(),Gt=class{route;childContexts;parent;outletData;constructor(n,e,r,i){this.route=n,this.childContexts=e,this.parent=r,this.outletData=i}get(n,e){return n===B?this.route:n===ve?this.childContexts:n===pn?this.outletData:this.parent.get(n,e)}},Rt=new D("");var tr=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=Ir({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&Dr(0,"router-outlet")},dependencies:[er],encapsulation:2})}return t})();function rr(t){let n=t.children&&t.children.map(rr),e=n?O(l({},t),{children:n}):l({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==f&&(e.component=tr),e}function ui(t,n,e){let r=xe(t,n._root,e?e._root:void 0);return new Ue(r,n)}function xe(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=ci(t,n,e);return new T(r,i)}else{if(t.shouldAttach(n.value)){let o=t.retrieve(n.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>xe(t,a)),s}}let r=li(n.value),i=n.children.map(o=>xe(t,o));return new T(r,i)}}function ci(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return xe(t,r,i);return xe(t,r)})}function li(t){return new B(new M(t.url),new M(t.params),new M(t.queryParams),new M(t.fragment),new M(t.data),t.outlet,t.component,t)}var fe=class{redirectTo;navigationBehaviorOptions;constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},gn="ngNavigationCancelingError";function gt(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=K(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=vn(!1,w.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function vn(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[gn]=!0,e.cancellationCode=n,e}function hi(t){return mn(t)&&K(t.url)}function mn(t){return!!t&&t[gn]}var di=(t,n,e,r)=>v(i=>(new Qt(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),Qt=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,e,r,i,o){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),jt(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=ue(e);n.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(n,e,s.children)}else this.deactivateChildRoutes(n,e,r);else o&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=ue(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=ue(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=ue(e);n.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new ht(o.value.snapshot))}),n.children.length&&this.forwardEvent(new ct(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(jt(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,s.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),jt(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},vt=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},le=class{component;route;constructor(n,e){this.component=n,this.route=e}};function fi(t,n,e){let r=t._root,i=n?n._root:null;return Ie(r,i,e,[r.value])}function pi(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function me(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!pr(t)?t:n.get(t):r}function Ie(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=ue(n);return t.children.forEach(s=>{gi(s,o[s.value.outlet],e,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>Ee(a,e.getContext(s),i)),i}function gi(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=t.value,s=n?n.value:null,a=e?e.getContext(t.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let u=vi(s,o,o.routeConfig.runGuardsAndResolvers);u?i.canActivateChecks.push(new vt(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?Ie(t,n,a?a.children:null,r,i):Ie(t,n,e,r,i),u&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new le(a.outlet.component,s))}else s&&Ee(n,a,i),i.canActivateChecks.push(new vt(r)),o.component?Ie(t,null,a?a.children:null,r,i):Ie(t,null,e,r,i);return i}function vi(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!J(t.url,n.url);case"pathParamsOrQueryParamsChange":return!J(t.url,n.url)||!j(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Wt(t,n)||!j(t.queryParams,n.queryParams);case"paramsChange":default:return!Wt(t,n)}}function Ee(t,n,e){let r=ue(t),i=t.value;Object.entries(r).forEach(([o,s])=>{i.component?n?Ee(s,n.children.getContext(o),e):Ee(s,null,e):Ee(s,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new le(n.outlet.component,i)):e.canDeactivateChecks.push(new le(null,i)):e.canDeactivateChecks.push(new le(null,i))}function $e(t){return typeof t=="function"}function mi(t){return typeof t=="boolean"}function Ri(t){return t&&$e(t.canLoad)}function yi(t){return t&&$e(t.canActivate)}function Si(t){return t&&$e(t.canActivateChild)}function Ci(t){return t&&$e(t.canDeactivate)}function wi(t){return t&&$e(t.canMatch)}function Rn(t){return t instanceof cr||t?.name==="EmptyError"}var Ye=Symbol("INITIAL_VALUE");function pe(){return U(t=>Tt(t.map(n=>n.pipe(oe(1),fr(Ye)))).pipe(v(n=>{for(let e of n)if(e!==!0){if(e===Ye)return Ye;if(e===!1||bi(e))return e}return!0}),W(n=>n!==Ye),oe(1)))}function bi(t){return K(t)||t instanceof fe}function Ii(t,n){return A(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=e;return s.length===0&&o.length===0?h(O(l({},e),{guardsResult:!0})):Ai(s,r,i,t).pipe(A(a=>a&&mi(a)?Ti(r,o,t,n):h(a)),v(a=>O(l({},e),{guardsResult:a})))})}function Ai(t,n,e,r){return I(t).pipe(A(i=>Ni(i.component,i.route,e,n,r)),G(i=>i!==!0,!0))}function Ti(t,n,e,r){return I(n).pipe(ie(i=>lr(Oi(i.route.parent,r),Ei(i.route,r),Di(t,i.path,e),Mi(t,i.route,e))),G(i=>i!==!0,!0))}function Ei(t,n){return t!==null&&n&&n(new lt(t)),h(!0)}function Oi(t,n){return t!==null&&n&&n(new ut(t)),h(!0)}function Mi(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return h(!0);let i=r.map(o=>qe(()=>{let s=ge(n)??e,a=me(o,s),u=yi(a)?a.canActivate(n,t):P(s,()=>a(n,t));return H(u).pipe(G())}));return h(i).pipe(pe())}function Di(t,n,e){let r=n[n.length-1],o=n.slice(0,n.length-1).reverse().map(s=>pi(s)).filter(s=>s!==null).map(s=>qe(()=>{let a=s.guards.map(u=>{let c=ge(s.node)??e,g=me(u,c),R=Si(g)?g.canActivateChild(r,t):P(c,()=>g(r,t));return H(R).pipe(G())});return h(a).pipe(pe())}));return h(o).pipe(pe())}function Ni(t,n,e,r,i){let o=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!o||o.length===0)return h(!0);let s=o.map(a=>{let u=ge(n)??i,c=me(a,u),g=Ci(c)?c.canDeactivate(t,n,e,r):P(u,()=>c(t,n,e,r));return H(g).pipe(G())});return h(s).pipe(pe())}function Ui(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return h(!0);let o=i.map(s=>{let a=me(s,t),u=Ri(a)?a.canLoad(n,e):P(t,()=>a(n,e));return H(u)});return h(o).pipe(pe(),yn(r))}function yn(t){return sr(y(n=>{if(typeof n!="boolean")throw gt(t,n)}),v(n=>n===!0))}function Pi(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return h(!0);let o=i.map(s=>{let a=me(s,t),u=wi(a)?a.canMatch(n,e):P(t,()=>a(n,e));return H(u)});return h(o).pipe(pe(),yn(r))}var _e=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},Le=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function ae(t){return Se(new _e(t))}function xi(t){return Se(new S(4e3,!1))}function _i(t){return Se(vn(!1,w.GuardRejected))}var Kt=class{urlSerializer;urlTree;constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return h(r);if(i.numberOfChildren>1||!i.children[f])return xi(`${n.redirectTo}`);i=i.children[f]}}applyRedirectCommands(n,e,r,i,o){return Li(e,i,o).pipe(v(s=>{if(s instanceof $)throw new Le(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new Le(a);return a}))}applyRedirectCreateUrlTree(n,e,r,i){let o=this.createSegmentGroup(n,e.root,r,i);return new $(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=e[a]}else r[i]=o}),r}createSegmentGroup(n,e,r,i){let o=this.createSegments(n,e.segments,r,i),s={};return Object.entries(e.children).forEach(([a,u])=>{s[a]=this.createSegmentGroup(n,u,r,i)}),new p(o,s)}createSegments(n,e,r,i){return e.map(o=>o.path[0]===":"?this.findPosParam(n,o,i):this.findOrReturn(o,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new S(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}};function Li(t,n,e){if(typeof t=="string")return h(t);let r=t,{queryParams:i,fragment:o,routeConfig:s,url:a,outlet:u,params:c,data:g,title:R}=n;return H(P(e,()=>r({params:c,data:g,queryParams:i,fragment:o,routeConfig:s,url:a,outlet:u,title:R})))}var Yt={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function ji(t,n,e,r,i){let o=Sn(t,n,e);return o.matched?(r=oi(n,r),Pi(r,n,e,i).pipe(v(s=>s===!0?o:l({},Yt)))):h(o)}function Sn(t,n,e){if(n.path==="**")return ki(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?l({},Yt):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||Qr)(e,t,n);if(!i)return l({},Yt);let o={};Object.entries(i.posParams??{}).forEach(([a,u])=>{o[a]=u.path});let s=i.consumed.length>0?l(l({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function ki(t){return{matched:!0,parameters:t.length>0?Yr(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Vr(t,n,e,r){return e.length>0&&Fi(t,e,r)?{segmentGroup:new p(n,zi(r,new p(e,t.children))),slicedSegments:[]}:e.length===0&&qi(t,e,r)?{segmentGroup:new p(t.segments,$i(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new p(t.segments,t.children),slicedSegments:e}}function $i(t,n,e,r){let i={};for(let o of e)if(yt(t,n,o)&&!r[_(o)]){let s=new p([],{});i[_(o)]=s}return l(l({},r),i)}function zi(t,n){let e={};e[f]=n;for(let r of t)if(r.path===""&&_(r)!==f){let i=new p([],{});e[_(r)]=i}return e}function Fi(t,n,e){return e.some(r=>yt(t,n,r)&&_(r)!==f)}function qi(t,n,e){return e.some(r=>yt(t,n,r))}function yt(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function Bi(t,n,e){return n.length===0&&!t.children[e]}var Zt=class{};function Hi(t,n,e,r,i,o,s="emptyOnly"){return new Xt(t,n,e,r,i,s,o).recognize()}var Vi=31,Xt=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,e,r,i,o,s,a){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Kt(this.urlSerializer,this.urlTree)}noMatchError(n){return new S(4002,`'${n.segmentGroup}'`)}recognize(){let n=Vr(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(v(({children:e,rootSnapshot:r})=>{let i=new T(r,e),o=new Pe("",i),s=sn(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(n){let e=new ee([],Object.freeze({}),Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),f,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,f,e).pipe(v(r=>({children:r,rootSnapshot:e})),Z(r=>{if(r instanceof Le)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof _e?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,o):this.processSegment(n,e,r,r.segments,i,!0,o).pipe(v(s=>s instanceof T?[s]:[]))}processChildren(n,e,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return I(o).pipe(ie(s=>{let a=r.children[s],u=si(e,s);return this.processSegmentGroup(n,u,a,s,i)}),dr((s,a)=>(s.push(...a),s)),Ot(null),hr(),A(s=>{if(s===null)return ae(r);let a=Cn(s);return Wi(a),h(a)}))}processSegment(n,e,r,i,o,s,a){return I(e).pipe(ie(u=>this.processSegmentAgainstRoute(u._injector??n,e,u,r,i,o,s,a).pipe(Z(c=>{if(c instanceof _e)return h(null);throw c}))),G(u=>!!u),Z(u=>{if(Rn(u))return Bi(r,i,o)?h(new Zt):ae(r);throw u}))}processSegmentAgainstRoute(n,e,r,i,o,s,a,u){return _(r)!==s&&(s===f||!yt(i,o,r))?ae(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,o,s,u):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,o,s,u):ae(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,o,s,a){let{matched:u,parameters:c,consumedSegments:g,positionalParamSegments:R,remainingSegments:N}=Sn(e,i,o);if(!u)return ae(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Vi&&(this.allowRedirects=!1));let V=new ee(o,c,Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,Wr(i),_(i),i.component??i._loadedComponent??null,i,Gr(i)),b=pt(V,a,this.paramsInheritanceStrategy);return V.params=Object.freeze(b.params),V.data=Object.freeze(b.data),this.applyRedirects.applyRedirectCommands(g,i.redirectTo,R,V,n).pipe(U(Y=>this.applyRedirects.lineralizeSegments(i,Y)),A(Y=>this.processSegment(n,r,e,Y.concat(N),s,!1,a)))}matchSegmentAgainstRoute(n,e,r,i,o,s){let a=ji(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(U(u=>u.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(U(({routes:c})=>{let g=r._loadedInjector??n,{parameters:R,consumedSegments:N,remainingSegments:V}=u,b=new ee(N,R,Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,Wr(r),_(r),r.component??r._loadedComponent??null,r,Gr(r)),wt=pt(b,s,this.paramsInheritanceStrategy);b.params=Object.freeze(wt.params),b.data=Object.freeze(wt.data);let{segmentGroup:Y,slicedSegments:bt}=Vr(e,N,V,c);if(bt.length===0&&Y.hasChildren())return this.processChildren(g,c,Y,b).pipe(v(Fe=>new T(b,Fe)));if(c.length===0&&bt.length===0)return h(new T(b,[]));let Un=_(r)===o;return this.processSegment(g,c,Y,bt,Un?f:o,!0,b).pipe(v(Fe=>new T(b,Fe instanceof T?[Fe]:[])))}))):ae(e)))}getChildConfig(n,e,r){return e.children?h({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?h({routes:e._loadedRoutes,injector:e._loadedInjector}):Ui(n,e,r,this.urlSerializer).pipe(A(i=>i?this.configLoader.loadChildren(n,e).pipe(y(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):_i(e))):h({routes:[],injector:n})}};function Wi(t){t.sort((n,e)=>n.value.outlet===f?-1:e.value.outlet===f?1:n.value.outlet.localeCompare(e.value.outlet))}function Gi(t){let n=t.value.routeConfig;return n&&n.path===""}function Cn(t){let n=[],e=new Set;for(let r of t){if(!Gi(r)){n.push(r);continue}let i=n.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=Cn(r.children);n.push(new T(r.value,i))}return n.filter(r=>!e.has(r))}function Wr(t){return t.data||{}}function Gr(t){return t.resolve||{}}function Qi(t,n,e,r,i,o){return A(s=>Hi(t,n,e,r,s.extractedUrl,i,o).pipe(v(({state:a,tree:u})=>O(l({},s),{targetSnapshot:a,urlAfterRedirects:u}))))}function Ki(t,n){return A(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return h(e);let o=new Set(i.map(u=>u.route)),s=new Set;for(let u of o)if(!s.has(u))for(let c of wn(u))s.add(c);let a=0;return I(s).pipe(ie(u=>o.has(u)?Yi(u,r,t,n):(u.data=pt(u,u.parent,t).resolve,h(void 0))),y(()=>a++),Mt(1),A(u=>a===s.size?h(e):F))})}function wn(t){let n=t.children.map(e=>wn(e)).flat();return[t,...n]}function Yi(t,n,e,r){let i=t.routeConfig,o=t._resolve;return i?.title!==void 0&&!fn(i)&&(o[je]=i.title),qe(()=>(t.data=pt(t,t.parent,e).resolve,Zi(o,t,n,r).pipe(v(s=>(t._resolvedData=s,t.data=l(l({},t.data),s),null)))))}function Zi(t,n,e,r){let i=zt(t);if(i.length===0)return h({});let o={};return I(i).pipe(A(s=>Xi(t[s],n,e,r).pipe(G(),y(a=>{if(a instanceof fe)throw gt(new re,a);o[s]=a}))),Mt(1),v(()=>o),Z(s=>Rn(s)?F:Se(s)))}function Xi(t,n,e,r){let i=ge(n)??r,o=me(t,i),s=o.resolve?o.resolve(n,e):P(i,()=>o(n,e));return H(s)}function kt(t){return U(n=>{let e=t(n);return e?I(e).pipe(v(()=>n)):h(n)})}var nr=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===f);return r}getResolvedTitleForRoute(e){return e.data[je]}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:()=>d(bn),providedIn:"root"})}return t})(),bn=(()=>{class t extends nr{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||t)(He(zr))};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Re=new D("",{providedIn:"root",factory:()=>({})}),ze=new D(""),In=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=d(_r);loadComponent(e,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return h(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let i=H(P(e,()=>r.loadComponent())).pipe(v(Tn),y(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Be(()=>{this.componentLoaders.delete(r)})),o=new At(i,()=>new z).pipe(It());return this.componentLoaders.set(r,o),o}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return h({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=An(r,this.compiler,e,this.onLoadEndListener).pipe(Be(()=>{this.childrenLoaders.delete(r)})),s=new At(o,()=>new z).pipe(It());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function An(t,n,e,r){return H(P(e,()=>t.loadChildren())).pipe(v(Tn),A(i=>i instanceof br||Array.isArray(i)?h(i):I(n.compileModuleAsync(i))),v(i=>{r&&r(t);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(e).injector,s=o.get(ze,[],{optional:!0,self:!0}).flat()),{routes:s.map(rr),injector:o}}))}function Ji(t){return t&&typeof t=="object"&&"default"in t}function Tn(t){return Ji(t)?t.default:t}var St=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:()=>d(eo),providedIn:"root"})}return t})(),eo=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),En=new D("");var On=new D(""),Mn=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new z;transitionAbortWithErrorSubject=new z;configLoader=d(In);environmentInjector=d(se);destroyRef=d(mr);urlSerializer=d(ke);rootContexts=d(ve);location=d(Qe);inputBindingEnabled=d(Rt,{optional:!0})!==null;titleStrategy=d(nr);options=d(Re,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=d(St);createViewTransition=d(En,{optional:!0});navigationErrorHandler=d(On,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>h(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new st(i)),r=i=>this.events.next(new at(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(O(l({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(e){return this.transitions=new M(null),this.transitions.pipe(W(r=>r!==null),U(r=>{let i=!1;return h(r).pipe(U(o=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",w.SupersededByNewNavigation),F;this.currentTransition=r,this.currentNavigation={id:o.id,initialUrl:o.rawUrl,extractedUrl:o.extractedUrl,targetBrowserUrl:typeof o.extras.browserUrl=="string"?this.urlSerializer.parse(o.extras.browserUrl):o.extras.browserUrl,trigger:o.source,extras:o.extras,previousNavigation:this.lastSuccessfulNavigation?O(l({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>o.abortController.abort()};let s=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=o.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!s&&a!=="reload"){let u="";return this.events.next(new q(o.id,this.urlSerializer.serialize(o.rawUrl),u,Me.IgnoredSameUrlNavigation)),o.resolve(!1),F}if(this.urlHandlingStrategy.shouldProcessUrl(o.rawUrl))return h(o).pipe(U(u=>(this.events.next(new ne(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?F:Promise.resolve(u))),Qi(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),y(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=O(l({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let c=new De(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(c)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(o.currentRawUrl)){let{id:u,extractedUrl:c,source:g,restoredState:R,extras:N}=o,V=new ne(u,this.urlSerializer.serialize(c),g,R);this.events.next(V);let b=hn(this.rootComponentType).snapshot;return this.currentTransition=r=O(l({},o),{targetSnapshot:b,urlAfterRedirects:c,extras:O(l({},N),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=c,h(r)}else{let u="";return this.events.next(new q(o.id,this.urlSerializer.serialize(o.extractedUrl),u,Me.IgnoredByUrlHandlingStrategy)),o.resolve(!1),F}}),y(o=>{let s=new rt(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(s)}),v(o=>(this.currentTransition=r=O(l({},o),{guards:fi(o.targetSnapshot,o.currentSnapshot,this.rootContexts)}),r)),Ii(this.environmentInjector,o=>this.events.next(o)),y(o=>{if(r.guardsResult=o.guardsResult,o.guardsResult&&typeof o.guardsResult!="boolean")throw gt(this.urlSerializer,o.guardsResult);let s=new nt(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot,!!o.guardsResult);this.events.next(s)}),W(o=>o.guardsResult?!0:(this.cancelNavigationTransition(o,"",w.GuardRejected),!1)),kt(o=>{if(o.guards.canActivateChecks.length!==0)return h(o).pipe(y(s=>{let a=new it(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),U(s=>{let a=!1;return h(s).pipe(Ki(this.paramsInheritanceStrategy,this.environmentInjector),y({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",w.NoDataFromResolver)}}))}),y(s=>{let a=new ot(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),kt(o=>{let s=a=>{let u=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let c=ge(a)??this.environmentInjector;u.push(this.configLoader.loadComponent(c,a.routeConfig).pipe(y(g=>{a.component=g}),v(()=>{})))}for(let c of a.children)u.push(...s(c));return u};return Tt(s(o.targetSnapshot.root)).pipe(Ot(null),oe(1))}),kt(()=>this.afterPreactivation()),U(()=>{let{currentSnapshot:o,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,o.root,s.root);return a?I(a).pipe(v(()=>r)):h(r)}),v(o=>{let s=ui(e.routeReuseStrategy,o.targetSnapshot,o.currentRouterState);return this.currentTransition=r=O(l({},o),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),y(()=>{this.events.next(new Ne)}),di(this.rootContexts,e.routeReuseStrategy,o=>this.events.next(o),this.inputBindingEnabled),oe(1),Dt(new ar(o=>{let s=r.abortController.signal,a=()=>o.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(W(()=>!i&&!r.targetRouterState),y(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",w.Aborted)}))),y({next:o=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new L(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects))),this.titleStrategy?.updateTitle(o.targetRouterState.snapshot),o.resolve(!0)},complete:()=>{i=!0}}),Dt(this.transitionAbortWithErrorSubject.pipe(y(o=>{throw o}))),Be(()=>{i||this.cancelNavigationTransition(r,"",w.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),Z(o=>{if(this.destroyed)return r.resolve(!1),F;if(i=!0,mn(o))this.events.next(new k(r.id,this.urlSerializer.serialize(r.extractedUrl),o.message,o.cancellationCode)),hi(o)?this.events.next(new de(o.url,o.navigationBehaviorOptions)):r.resolve(!1);else{let s=new he(r.id,this.urlSerializer.serialize(r.extractedUrl),o,r.targetSnapshot??void 0);try{let a=P(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof fe){let{message:u,cancellationCode:c}=gt(this.urlSerializer,a);this.events.next(new k(r.id,this.urlSerializer.serialize(r.extractedUrl),u,c)),this.events.next(new de(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),o}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return F}))}))}cancelNavigationTransition(e,r,i){let o=new k(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(o),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function to(t){return t!==Te}var Dn=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:()=>d(ro),providedIn:"root"})}return t})(),mt=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},ro=(()=>{class t extends mt{static \u0275fac=(()=>{let e;return function(i){return(e||(e=Nt(t)))(i||t)}})();static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Nn=(()=>{class t{urlSerializer=d(ke);options=d(Re,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=d(Qe);urlHandlingStrategy=d(St);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new $;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let o=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,s=i??o;return s instanceof $?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=hn(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:()=>d(no),providedIn:"root"})}return t})(),no=(()=>{class t extends Nn{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof ne?this.updateStateMemento():e instanceof q?this.commitTransition(r):e instanceof De?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof Ne?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof k&&e.code!==w.SupersededByNewNavigation&&e.code!==w.Redirect?this.restoreHistory(r):e instanceof he?this.restoreHistory(r,!0):e instanceof L&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:o,state:s}=r;if(this.location.isCurrentPathEqualTo(e)||o){let a=this.browserPageId,u=l(l({},s),this.generateNgRouterState(i,a));this.location.replaceState(e,"",u)}else{let a=l(l({},s),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.getCurrentUrlTree()===e.finalUrl&&o===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Nt(t)))(i||t)}})();static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function ir(t,n){t.events.pipe(W(e=>e instanceof L||e instanceof k||e instanceof he||e instanceof q),v(e=>e instanceof L||e instanceof q?0:(e instanceof k?e.code===w.Redirect||e.code===w.SupersededByNewNavigation:!1)?2:1),W(e=>e!==2),oe(1)).subscribe(()=>{n()})}var io={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},oo={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ye=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=d(Ar);stateManager=d(Nn);options=d(Re,{optional:!0})||{};pendingTasks=d(yr);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=d(Mn);urlSerializer=d(ke);location=d(Qe);urlHandlingStrategy=d(St);injector=d(se);_events=new z;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=d(Dn);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=d(ze,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!d(Rt,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new or;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof k&&r.code!==w.Redirect&&r.code!==w.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof L)this.navigated=!0;else if(r instanceof de){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),u=l({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||to(i.source)},s);this.scheduleNavigation(a,Te,null,u,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}ii(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortWithErrorSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Te,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let u=l({},i);delete u.navigationId,delete u.\u0275routerPageId,Object.keys(u).length!==0&&(o.state=u)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,s,o).catch(u=>{this.disposed||this.injector.get(Ve)(u)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(rr),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:u}=r,c=u?this.currentUrlTree.fragment:s,g=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":g=l(l({},this.currentUrlTree.queryParams),o);break;case"preserve":g=this.currentUrlTree.queryParams;break;default:g=o||null}g!==null&&(g=this.removeEmptyProps(g));let R;try{let N=i?i.snapshot:this.routerState.snapshot.root;R=an(N)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),R=this.currentUrlTree.root}return un(R,e,g,c??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=K(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,Te,null,r)}navigate(e,r={skipLocationChange:!1}){return so(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=l({},io):r===!1?i=l({},oo):i=r,K(e))return Fr(this.currentUrlTree,e,i);let o=this.parseUrl(e);return Fr(this.currentUrlTree,o,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(e,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,u,c;s?(a=s.resolve,u=s.reject,c=s.promise):c=new Promise((R,N)=>{a=R,u=N});let g=this.pendingTasks.add();return ir(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(g))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:a,reject:u,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(R=>Promise.reject(R))}static \u0275fac=function(r){return new(r||t)};static \u0275prov=C({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function so(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new S(4008,!1)}var Ct=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Rr(null);get href(){return Lr(this.reactiveHref)}set href(e){this.reactiveHref.set(e)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new z;applicationErrorHandler=d(Ve);options=d(Re,{optional:!0});constructor(e,r,i,o,s,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.reactiveHref.set(d(new jr("href"),{optional:!0}));let u=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=u==="a"||u==="area"||!!(typeof customElements=="object"&&customElements.get(u)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let e=this.preserveFragment,r=i=>i==="merge"||i==="preserve";e||=r(this.queryParamsHandling),e||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),e&&(this.subscription=this.router.events.subscribe(i=>{i instanceof L&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(K(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let u={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,u)?.catch(c=>{this.applicationErrorHandler(c)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.reactiveHref.set(e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e))??"":null)}applyAttributeValue(e,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,e,r):i.removeAttribute(o,e)}get urlTree(){return this.routerLinkInput===null?null:K(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||t)(x(ye),x(B),Sr("tabindex"),x(Pt),x(Ut),x($r))};static \u0275dir=we({type:t,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,i){r&1&&Nr("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Mr("href",i.reactiveHref(),Cr)("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Ge],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Ge],replaceUrl:[2,"replaceUrl","replaceUrl",Ge],routerLink:"routerLink"},features:[Ce]})}return t})(),uo=(()=>{class t{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new X;constructor(e,r,i,o,s){this.router=e,this.element=r,this.renderer=i,this.cdr=o,this.link=s,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof L&&this.update()})}ngAfterContentInit(){h(this.links.changes,h(null)).pipe(Et()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=I(e).pipe(Et()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(e){let r=Array.isArray(e)?e:e.split(" ");this.classes=r.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(r=>{e?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let r=co(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let o=i.urlTree;return o?e.isActive(o,r):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(r){return new(r||t)(x(ye),x(Ut),x(Pt),x(We),x(Ct,8))};static \u0275dir=we({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(r,i,o){if(r&1&&Ur(o,Ct,5),r&2){let s;Pr(s=xr())&&(i.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Ce]})}return t})();function co(t){return!!t.paths}var lo=new D("");function ho(t,...n){return gr([{provide:ze,multi:!0,useValue:t},[],{provide:B,useFactory:fo,deps:[ye]},{provide:Er,multi:!0,useFactory:po},n.map(e=>e.\u0275providers)])}function fo(t){return t.routerState.root}function po(){let t=d(vr);return n=>{let e=t.get(Or);if(n!==e.components[0])return;let r=t.get(ye),i=t.get(go);t.get(vo)===1&&r.initialNavigation(),t.get(mo,null,{optional:!0})?.setUpPreloading(),t.get(lo,null,{optional:!0})?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var go=new D("",{factory:()=>new z}),vo=new D("",{providedIn:"root",factory:()=>1});var mo=new D("");export{er as a,Ct as b,uo as c,ho as d};
