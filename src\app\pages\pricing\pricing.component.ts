import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterLink } from '@angular/router';

interface PricingPlan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  highlighted: boolean;
  buttonText: string;
  popular?: boolean;
}

@Component({
  selector: 'app-pricing',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatTabsModule, RouterLink],
  template: `
    <div class="pricing-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1>Transparent Pricing</h1>
          <p class="hero-subtitle">
            Choose the perfect plan for your business needs. All plans include our core features
            with flexible options to scale as you grow.
          </p>
        </div>
      </div>

      <!-- Pricing Plans -->
      <div class="pricing-section">
        <div class="container">
          <mat-tab-group class="pricing-tabs" animationDuration="300ms">
            <mat-tab label="AI/ML Services">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of aiPlans; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted" [class.popular]="plan.popular">
                      @if (plan.popular) {
                        <div class="popular-badge">Most Popular</div>
                      }
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Development Services">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of developmentPlans; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted" [class.popular]="plan.popular">
                      @if (plan.popular) {
                        <div class="popular-badge">Most Popular</div>
                      }
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Consulting Services">
              <div class="tab-content">
                <div class="pricing-grid">
                  @for (plan of consultingPlans; track plan.name) {
                    <mat-card class="pricing-card" [class.highlighted]="plan.highlighted">
                      <mat-card-header>
                        <mat-card-title>{{ plan.name }}</mat-card-title>
                        <mat-card-subtitle>{{ plan.description }}</mat-card-subtitle>
                      </mat-card-header>
                      <mat-card-content>
                        <div class="price-section">
                          <span class="price">{{ plan.price }}</span>
                          <span class="period">{{ plan.period }}</span>
                        </div>
                        <ul class="features-list">
                          @for (feature of plan.features; track feature) {
                            <li>
                              <mat-icon>check</mat-icon>
                              <span>{{ feature }}</span>
                            </li>
                          }
                        </ul>
                      </mat-card-content>
                      <mat-card-actions>
                        <button mat-raised-button
                                [color]="plan.highlighted ? 'primary' : 'accent'"
                                class="plan-button"
                                routerLink="/contact">
                          {{ plan.buttonText }}
                        </button>
                      </mat-card-actions>
                    </mat-card>
                  }
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="faq-section">
        <div class="container">
          <h2>Frequently Asked Questions</h2>
          <div class="faq-grid">
            <div class="faq-item">
              <h3>How do you determine project pricing?</h3>
              <p>Our pricing is based on project complexity, timeline, and resource requirements. We provide detailed estimates after understanding your specific needs.</p>
            </div>
            <div class="faq-item">
              <h3>Do you offer custom packages?</h3>
              <p>Yes, we create tailored solutions for unique requirements. Contact us to discuss your specific needs and get a custom quote.</p>
            </div>
            <div class="faq-item">
              <h3>What's included in ongoing support?</h3>
              <p>Our support includes bug fixes, minor updates, performance monitoring, and technical assistance during business hours.</p>
            </div>
            <div class="faq-item">
              <h3>Can I upgrade my plan later?</h3>
              <p>Absolutely! You can upgrade your service level at any time. We'll work with you to ensure a smooth transition.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Get Started?</h2>
          <p>Contact us today for a free consultation and custom quote for your project.</p>
          <div class="cta-buttons">
            <button mat-raised-button color="primary" routerLink="/contact">
              Get Free Consultation
            </button>
            <button mat-stroked-button routerLink="/services">
              View Our Services
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pricing-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-section h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .hero-subtitle {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto;
      opacity: 0.9;
    }

    .pricing-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .pricing-tabs {
      margin-top: 2rem;
    }

    .tab-content {
      padding: 2rem 0;
    }

    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .pricing-card {
      position: relative;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .pricing-card.highlighted {
      border: 2px solid #0607E1;
      transform: scale(1.05);
    }

    .pricing-card.popular {
      border: 2px solid #4D0AFF;
    }

    .popular-badge {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: #4D0AFF;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: bold;
    }

    .price-section {
      text-align: center;
      margin: 1.5rem 0;
    }

    .price {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0607E1;
    }

    .period {
      font-size: 1rem;
      color: #666;
      margin-left: 0.5rem;
    }

    .features-list {
      list-style: none;
      padding: 0;
      margin: 1.5rem 0;
    }

    .features-list li {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
      color: #555;
    }

    .features-list mat-icon {
      color: #10B981;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .plan-button {
      width: 100%;
      margin-top: auto;
    }

    .faq-section {
      padding: 4rem 0;
    }

    .faq-section h2 {
      text-align: center;
      color: #333;
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }

    .faq-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .faq-item {
      padding: 1.5rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .faq-item h3 {
      color: #0607E1;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }

    .faq-item p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .hero-section h1 {
        font-size: 2rem;
      }

      .pricing-grid {
        grid-template-columns: 1fr;
      }

      .pricing-card.highlighted {
        transform: none;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class PricingComponent {
  aiPlans: PricingPlan[] = [
    {
      name: 'AI Starter',
      price: '$5,000',
      period: 'per project',
      description: 'Perfect for small businesses exploring AI',
      features: [
        'AI readiness assessment',
        'Basic ML model development',
        'Data analysis and insights',
        '30 days support',
        'Documentation and training'
      ],
      highlighted: false,
      buttonText: 'Get Started'
    },
    {
      name: 'AI Professional',
      price: '$15,000',
      period: 'per project',
      description: 'Comprehensive AI solutions for growing businesses',
      features: [
        'Custom AI strategy development',
        'Advanced ML models',
        'Computer vision or NLP integration',
        'Cloud deployment',
        '90 days support',
        'Performance monitoring',
        'Team training sessions'
      ],
      highlighted: true,
      popular: true,
      buttonText: 'Most Popular'
    },
    {
      name: 'AI Enterprise',
      price: 'Custom',
      period: 'quote',
      description: 'Enterprise-grade AI transformation',
      features: [
        'End-to-end AI transformation',
        'Multiple AI solutions',
        'MLOps implementation',
        'Dedicated team',
        '1 year support',
        'Ongoing optimization',
        'Executive reporting',
        'Priority support'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];

  developmentPlans: PricingPlan[] = [
    {
      name: 'Web Essentials',
      price: '$3,000',
      period: 'per project',
      description: 'Professional websites and web applications',
      features: [
        'Responsive web design',
        'Up to 10 pages',
        'Contact forms',
        'SEO optimization',
        'Mobile-friendly',
        '30 days support'
      ],
      highlighted: false,
      buttonText: 'Get Started'
    },
    {
      name: 'Full Stack Pro',
      price: '$10,000',
      period: 'per project',
      description: 'Complete web and mobile applications',
      features: [
        'Custom web application',
        'Database design',
        'User authentication',
        'API development',
        'Mobile app (iOS/Android)',
        'Admin dashboard',
        '90 days support'
      ],
      highlighted: true,
      popular: true,
      buttonText: 'Most Popular'
    },
    {
      name: 'Enterprise Solution',
      price: 'Custom',
      period: 'quote',
      description: 'Large-scale enterprise applications',
      features: [
        'Scalable architecture',
        'Microservices design',
        'Cloud infrastructure',
        'DevOps setup',
        'Security implementation',
        'Performance optimization',
        '1 year support'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];

  consultingPlans: PricingPlan[] = [
    {
      name: 'Strategy Session',
      price: '$500',
      period: 'per session',
      description: 'Expert guidance for your technology decisions',
      features: [
        '2-hour consultation',
        'Technology assessment',
        'Roadmap recommendations',
        'Follow-up report',
        'Email support for 1 week'
      ],
      highlighted: false,
      buttonText: 'Book Session'
    },
    {
      name: 'Monthly Retainer',
      price: '$2,500',
      period: 'per month',
      description: 'Ongoing technology consulting and support',
      features: [
        '10 hours monthly consulting',
        'Strategic planning',
        'Technology reviews',
        'Team mentoring',
        'Priority email support',
        'Monthly reports'
      ],
      highlighted: true,
      buttonText: 'Start Retainer'
    },
    {
      name: 'Transformation Program',
      price: 'Custom',
      period: 'quote',
      description: 'Complete digital transformation consulting',
      features: [
        'Comprehensive assessment',
        'Digital strategy development',
        'Implementation roadmap',
        'Change management',
        'Team training',
        'Ongoing support',
        'Success metrics tracking'
      ],
      highlighted: false,
      buttonText: 'Contact Sales'
    }
  ];
}
