import './polyfills.server.mjs';
import{b as Ot,d as Ct,g as kt,h as X,j as x,l as U}from"./chunk-YJQYZ7V2.mjs";import{b as mt,d as K,h as p}from"./chunk-X7CFQVIU.mjs";import{Fa as _t,Ga as M,Ha as pt,Ka as ft,P as rt,Pa as ut,Qa as gt,Ra as j,T as v,U as at,Uc as A,Va as S,W as lt,Wa as O,Xa as Z,Xc as Y,Y as d,_ as ht,a as m,bb as yt,cd as $,ea as w,ed as G,f as b,fa as R,kc as C,na as ct,nd as bt,oa as z,od as wt,qa as B,ud as q,vd as St,wa as dt,x as nt,zc as vt}from"./chunk-A77TV5YY.mjs";import{a as D,b as ot}from"./chunk-X2SEQXRR.mjs";var Rt=mt();function At(s){return new F(s.get(x),s.get(R))}var F=class{_viewportRuler;_previousHTMLStyles={top:"",left:""};_previousScrollPosition;_isEnabled=!1;_document;constructor(t,e){this._viewportRuler=t,this._document=e}attach(){}enable(){if(this._canBeEnabled()){let t=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=t.style.left||"",this._previousHTMLStyles.top=t.style.top||"",t.style.left=p(-this._previousScrollPosition.left),t.style.top=p(-this._previousScrollPosition.top),t.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let t=this._document.documentElement,e=this._document.body,i=t.style,o=e.style,n=i.scrollBehavior||"",r=o.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,t.classList.remove("cdk-global-scrollblock"),Rt&&(i.scrollBehavior=o.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),Rt&&(i.scrollBehavior=n,o.scrollBehavior=r)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let e=this._document.documentElement,i=this._viewportRuler.getViewportSize();return e.scrollHeight>i.height||e.scrollWidth>i.width}};function Yt(s,t){return new V(s.get(X),s.get(O),s.get(x),t)}var V=class{_scrollDispatcher;_ngZone;_viewportRuler;_config;_scrollSubscription=null;_overlayRef;_initialScrollPosition;constructor(t,e,i,o){this._scrollDispatcher=t,this._ngZone=e,this._viewportRuler=i,this._config=o}attach(t){this._overlayRef,this._overlayRef=t}enable(){if(this._scrollSubscription)return;let t=this._scrollDispatcher.scrolled(0).pipe(nt(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=t.subscribe(()=>{let e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=t.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}_detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}};var E=class{enable(){}disable(){}attach(){}};function J(s,t){return t.some(e=>{let i=s.bottom<e.top,o=s.top>e.bottom,n=s.right<e.left,r=s.left>e.right;return i||o||n||r})}function xt(s,t){return t.some(e=>{let i=s.top<e.top,o=s.bottom>e.bottom,n=s.left<e.left,r=s.right>e.right;return i||o||n||r})}function W(s,t){return new T(s.get(X),s.get(x),s.get(O),t)}var T=class{_scrollDispatcher;_viewportRuler;_ngZone;_config;_scrollSubscription=null;_overlayRef;constructor(t,e,i,o){this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=i,this._config=o}attach(t){this._overlayRef,this._overlayRef=t}enable(){if(!this._scrollSubscription){let t=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(t).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:o}=this._viewportRuler.getViewportSize();J(e,[{width:i,height:o,bottom:o,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},Xt=(()=>{class s{_injector=d(w);constructor(){}noop=()=>new E;close=e=>Yt(this._injector,e);block=()=>At(this._injector);reposition=e=>W(this._injector,e);static \u0275fac=function(i){return new(i||s)};static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})(),P=class{positionStrategy;scrollStrategy=new E;panelClass="";hasBackdrop=!1;backdropClass="cdk-overlay-dark-backdrop";disableAnimations;width;height;minWidth;minHeight;maxWidth;maxHeight;direction;disposeOnNavigation=!1;constructor(t){if(t){let e=Object.keys(t);for(let i of e)t[i]!==void 0&&(this[i]=t[i])}}};var I=class{connectionPair;scrollableViewProperties;constructor(t,e){this.connectionPair=t,this.scrollableViewProperties=e}};var Ft=(()=>{class s{_attachedOverlays=[];_document=d(R);_isAttached;constructor(){}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){let i=this._attachedOverlays.indexOf(e);i>-1&&this._attachedOverlays.splice(i,1),this._attachedOverlays.length===0&&this.detach()}static \u0275fac=function(i){return new(i||s)};static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})(),Vt=(()=>{class s extends Ft{_ngZone=d(O);_renderer=d(M).createRenderer(null,null);_cleanupKeydown;add(e){super.add(e),this._isAttached||(this._ngZone.runOutsideAngular(()=>{this._cleanupKeydown=this._renderer.listen("body","keydown",this._keydownListener)}),this._isAttached=!0)}detach(){this._isAttached&&(this._cleanupKeydown?.(),this._isAttached=!1)}_keydownListener=e=>{let i=this._attachedOverlays;for(let o=i.length-1;o>-1;o--)if(i[o]._keydownEvents.observers.length>0){this._ngZone.run(()=>i[o]._keydownEvents.next(e));break}};static \u0275fac=(()=>{let e;return function(o){return(e||(e=z(s)))(o||s)}})();static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})(),Tt=(()=>{class s extends Ft{_platform=d(A);_ngZone=d(O);_renderer=d(M).createRenderer(null,null);_cursorOriginalValue;_cursorStyleIsSet=!1;_pointerDownEventTarget;_cleanups;add(e){if(super.add(e),!this._isAttached){let i=this._document.body,o={capture:!0},n=this._renderer;this._cleanups=this._ngZone.runOutsideAngular(()=>[n.listen(i,"pointerdown",this._pointerDownListener,o),n.listen(i,"click",this._clickListener,o),n.listen(i,"auxclick",this._clickListener,o),n.listen(i,"contextmenu",this._clickListener,o)]),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){this._isAttached&&(this._cleanups?.forEach(e=>e()),this._cleanups=void 0,this._platform.IOS&&this._cursorStyleIsSet&&(this._document.body.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1)}_pointerDownListener=e=>{this._pointerDownEventTarget=Y(e)};_clickListener=e=>{let i=Y(e),o=e.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:i;this._pointerDownEventTarget=null;let n=this._attachedOverlays.slice();for(let r=n.length-1;r>-1;r--){let a=n[r];if(a._outsidePointerEvents.observers.length<1||!a.hasAttached())continue;if(Et(a.overlayElement,i)||Et(a.overlayElement,o))break;let h=a._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>h.next(e)):h.next(e)}};static \u0275fac=(()=>{let e;return function(o){return(e||(e=z(s)))(o||s)}})();static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})();function Et(s,t){let e=typeof ShadowRoot<"u"&&ShadowRoot,i=t;for(;i;){if(i===s)return!0;i=e&&i instanceof ShadowRoot?i.host:i.parentNode}return!1}var It=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275cmp=ut({type:s,selectors:[["ng-component"]],hostAttrs:["cdk-overlay-style-loader",""],decls:0,vars:0,template:function(i,o){},styles:[`.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}
`],encapsulation:2,changeDetection:0})}return s})(),Lt=(()=>{class s{_platform=d(A);_containerElement;_document=d(R);_styleLoader=d($);constructor(){}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._loadStyles(),this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let e="cdk-overlay-container";if(this._platform.isBrowser||K()){let o=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let n=0;n<o.length;n++)o[n].remove()}let i=this._document.createElement("div");i.classList.add(e),K()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}_loadStyles(){this._styleLoader.load(It)}static \u0275fac=function(i){return new(i||s)};static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})(),Q=class{_renderer;_ngZone;element;_cleanupClick;_cleanupTransitionEnd;_fallbackTimeout;constructor(t,e,i,o){this._renderer=e,this._ngZone=i,this.element=t.createElement("div"),this.element.classList.add("cdk-overlay-backdrop"),this._cleanupClick=e.listen(this.element,"click",o)}detach(){this._ngZone.runOutsideAngular(()=>{let t=this.element;clearTimeout(this._fallbackTimeout),this._cleanupTransitionEnd?.(),this._cleanupTransitionEnd=this._renderer.listen(t,"transitionend",this.dispose),this._fallbackTimeout=setTimeout(this.dispose,500),t.style.pointerEvents="none",t.classList.remove("cdk-overlay-backdrop-showing")})}dispose=()=>{clearTimeout(this._fallbackTimeout),this._cleanupClick?.(),this._cleanupTransitionEnd?.(),this._cleanupClick=this._cleanupTransitionEnd=this._fallbackTimeout=void 0,this.element.remove()}},L=class{_portalOutlet;_host;_pane;_config;_ngZone;_keyboardDispatcher;_document;_location;_outsideClickDispatcher;_animationsDisabled;_injector;_renderer;_backdropClick=new b;_attachments=new b;_detachments=new b;_positionStrategy;_scrollStrategy;_locationChanges=m.EMPTY;_backdropRef=null;_detachContentMutationObserver;_detachContentAfterRenderRef;_previousHostParent;_keydownEvents=new b;_outsidePointerEvents=new b;_afterNextRenderRef;constructor(t,e,i,o,n,r,a,h,c,l=!1,_,y){this._portalOutlet=t,this._host=e,this._pane=i,this._config=o,this._ngZone=n,this._keyboardDispatcher=r,this._document=a,this._location=h,this._outsideClickDispatcher=c,this._animationsDisabled=l,this._injector=_,this._renderer=y,o.scrollStrategy&&(this._scrollStrategy=o.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=o.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropRef?.element||null}get hostElement(){return this._host}attach(t){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let e=this._portalOutlet.attach(t);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=Z(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._completeDetachContent(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof e?.onDestroy=="function"&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let t=this._portalOutlet.detach();return this._detachments.next(),this._completeDetachContent(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),t}dispose(){let t=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._backdropRef?.dispose(),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=this._backdropRef=null,t&&this._detachments.next(),this._detachments.complete(),this._completeDetachContent()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(t){t!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=t,this.hasAttached()&&(t.attach(this),this.updatePosition()))}updateSize(t){this._config=D(D({},this._config),t),this._updateElementSize()}setDirection(t){this._config=ot(D({},this._config),{direction:t}),this._updateElementDirection()}addPanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!0)}removePanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!1)}getDirection(){let t=this._config.direction;return t?typeof t=="string"?t:t.value:"ltr"}updateScrollStrategy(t){t!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=t,this.hasAttached()&&(t.attach(this),t.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let t=this._pane.style;t.width=p(this._config.width),t.height=p(this._config.height),t.minWidth=p(this._config.minWidth),t.minHeight=p(this._config.minHeight),t.maxWidth=p(this._config.maxWidth),t.maxHeight=p(this._config.maxHeight)}_togglePointerEvents(t){this._pane.style.pointerEvents=t?"":"none"}_attachBackdrop(){let t="cdk-overlay-backdrop-showing";this._backdropRef?.dispose(),this._backdropRef=new Q(this._document,this._renderer,this._ngZone,e=>{this._backdropClick.next(e)}),this._animationsDisabled&&this._backdropRef.element.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropRef.element,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropRef.element,this._host),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._backdropRef?.element.classList.add(t))}):this._backdropRef.element.classList.add(t)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){this._animationsDisabled?(this._backdropRef?.dispose(),this._backdropRef=null):this._backdropRef?.detach()}_toggleClasses(t,e,i){let o=G(e||[]).filter(n=>!!n);o.length&&(i?t.classList.add(...o):t.classList.remove(...o))}_detachContentWhenEmpty(){let t=!1;try{this._detachContentAfterRenderRef=Z(()=>{t=!0,this._detachContent()},{injector:this._injector})}catch(e){if(t)throw e;this._detachContent()}globalThis.MutationObserver&&this._pane&&(this._detachContentMutationObserver||=new globalThis.MutationObserver(()=>{this._detachContent()}),this._detachContentMutationObserver.observe(this._pane,{childList:!0}))}_detachContent(){(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),this._completeDetachContent())}_completeDetachContent(){this._detachContentAfterRenderRef?.destroy(),this._detachContentAfterRenderRef=void 0,this._detachContentMutationObserver?.disconnect()}_disposeScrollStrategy(){let t=this._scrollStrategy;t?.disable(),t?.detach?.()}},Pt="cdk-overlay-connected-position-bounding-box",jt=/([A-Za-z%]+)$/;function et(s,t){return new N(t,s.get(x),s.get(R),s.get(A),s.get(Lt))}var N=class{_viewportRuler;_document;_platform;_overlayContainer;_overlayRef;_isInitialRender;_lastBoundingBoxSize={width:0,height:0};_isPushed=!1;_canPush=!0;_growAfterOpen=!1;_hasFlexibleDimensions=!0;_positionLocked=!1;_originRect;_overlayRect;_viewportRect;_containerRect;_viewportMargin=0;_scrollables=[];_preferredPositions=[];_origin;_pane;_isDisposed;_boundingBox;_lastPosition;_lastScrollVisibility;_positionChanges=new b;_resizeSubscription=m.EMPTY;_offsetX=0;_offsetY=0;_transformOriginSelector;_appliedPanelClasses=[];_previousPushAmount;positionChanges=this._positionChanges;get positions(){return this._preferredPositions}constructor(t,e,i,o,n){this._viewportRuler=e,this._document=i,this._platform=o,this._overlayContainer=n,this.setOrigin(t)}attach(t){this._overlayRef&&this._overlayRef,this._validatePositions(),t.hostElement.classList.add(Pt),this._overlayRef=t,this._boundingBox=t.hostElement,this._pane=t.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let t=this._originRect,e=this._overlayRect,i=this._viewportRect,o=this._containerRect,n=[],r;for(let a of this._preferredPositions){let h=this._getOriginPoint(t,o,a),c=this._getOverlayPoint(h,e,a),l=this._getOverlayFit(c,e,i,a);if(l.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(a,h);return}if(this._canFitWithFlexibleDimensions(l,c,i)){n.push({position:a,origin:h,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(h,a)});continue}(!r||r.overlayFit.visibleArea<l.visibleArea)&&(r={overlayFit:l,overlayPoint:c,originPoint:h,position:a,overlayRect:e})}if(n.length){let a=null,h=-1;for(let c of n){let l=c.boundingBoxRect.width*c.boundingBoxRect.height*(c.position.weight||1);l>h&&(h=l,a=c)}this._isPushed=!1,this._applyPosition(a.position,a.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(r.position,r.originPoint);return}this._applyPosition(r.position,r.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&k(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(Pt),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let t=this._lastPosition;if(t){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let e=this._getOriginPoint(this._originRect,this._containerRect,t);this._applyPosition(t,e)}else this.apply()}withScrollableContainers(t){return this._scrollables=t,this}withPositions(t){return this._preferredPositions=t,t.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(t){return this._viewportMargin=t,this}withFlexibleDimensions(t=!0){return this._hasFlexibleDimensions=t,this}withGrowAfterOpen(t=!0){return this._growAfterOpen=t,this}withPush(t=!0){return this._canPush=t,this}withLockedPosition(t=!0){return this._positionLocked=t,this}setOrigin(t){return this._origin=t,this}withDefaultOffsetX(t){return this._offsetX=t,this}withDefaultOffsetY(t){return this._offsetY=t,this}withTransformOriginOn(t){return this._transformOriginSelector=t,this}_getOriginPoint(t,e,i){let o;if(i.originX=="center")o=t.left+t.width/2;else{let r=this._isRtl()?t.right:t.left,a=this._isRtl()?t.left:t.right;o=i.originX=="start"?r:a}e.left<0&&(o-=e.left);let n;return i.originY=="center"?n=t.top+t.height/2:n=i.originY=="top"?t.top:t.bottom,e.top<0&&(n-=e.top),{x:o,y:n}}_getOverlayPoint(t,e,i){let o;i.overlayX=="center"?o=-e.width/2:i.overlayX==="start"?o=this._isRtl()?-e.width:0:o=this._isRtl()?0:-e.width;let n;return i.overlayY=="center"?n=-e.height/2:n=i.overlayY=="top"?0:-e.height,{x:t.x+o,y:t.y+n}}_getOverlayFit(t,e,i,o){let n=Bt(e),{x:r,y:a}=t,h=this._getOffset(o,"x"),c=this._getOffset(o,"y");h&&(r+=h),c&&(a+=c);let l=0-r,_=r+n.width-i.width,y=0-a,u=a+n.height-i.height,f=this._subtractOverflows(n.width,l,_),g=this._subtractOverflows(n.height,y,u),st=f*g;return{visibleArea:st,isCompletelyWithinViewport:n.width*n.height===st,fitsInViewportVertically:g===n.height,fitsInViewportHorizontally:f==n.width}}_canFitWithFlexibleDimensions(t,e,i){if(this._hasFlexibleDimensions){let o=i.bottom-e.y,n=i.right-e.x,r=Dt(this._overlayRef.getConfig().minHeight),a=Dt(this._overlayRef.getConfig().minWidth),h=t.fitsInViewportVertically||r!=null&&r<=o,c=t.fitsInViewportHorizontally||a!=null&&a<=n;return h&&c}return!1}_pushOverlayOnScreen(t,e,i){if(this._previousPushAmount&&this._positionLocked)return{x:t.x+this._previousPushAmount.x,y:t.y+this._previousPushAmount.y};let o=Bt(e),n=this._viewportRect,r=Math.max(t.x+o.width-n.width,0),a=Math.max(t.y+o.height-n.height,0),h=Math.max(n.top-i.top-t.y,0),c=Math.max(n.left-i.left-t.x,0),l=0,_=0;return o.width<=n.width?l=c||-r:l=t.x<this._viewportMargin?n.left-i.left-t.x:0,o.height<=n.height?_=h||-a:_=t.y<this._viewportMargin?n.top-i.top-t.y:0,this._previousPushAmount={x:l,y:_},{x:t.x+l,y:t.y+_}}_applyPosition(t,e){if(this._setTransformOrigin(t),this._setOverlayElementStyles(e,t),this._setBoundingBoxStyles(e,t),t.panelClass&&this._addPanelClasses(t.panelClass),this._positionChanges.observers.length){let i=this._getScrollVisibility();if(t!==this._lastPosition||!this._lastScrollVisibility||!Zt(this._lastScrollVisibility,i)){let o=new I(t,i);this._positionChanges.next(o)}this._lastScrollVisibility=i}this._lastPosition=t,this._isInitialRender=!1}_setTransformOrigin(t){if(!this._transformOriginSelector)return;let e=this._boundingBox.querySelectorAll(this._transformOriginSelector),i,o=t.overlayY;t.overlayX==="center"?i="center":this._isRtl()?i=t.overlayX==="start"?"right":"left":i=t.overlayX==="start"?"left":"right";for(let n=0;n<e.length;n++)e[n].style.transformOrigin=`${i} ${o}`}_calculateBoundingBoxRect(t,e){let i=this._viewportRect,o=this._isRtl(),n,r,a;if(e.overlayY==="top")r=t.y,n=i.height-r+this._viewportMargin;else if(e.overlayY==="bottom")a=i.height-t.y+this._viewportMargin*2,n=i.height-a+this._viewportMargin;else{let u=Math.min(i.bottom-t.y+i.top,t.y),f=this._lastBoundingBoxSize.height;n=u*2,r=t.y-u,n>f&&!this._isInitialRender&&!this._growAfterOpen&&(r=t.y-f/2)}let h=e.overlayX==="start"&&!o||e.overlayX==="end"&&o,c=e.overlayX==="end"&&!o||e.overlayX==="start"&&o,l,_,y;if(c)y=i.width-t.x+this._viewportMargin*2,l=t.x-this._viewportMargin;else if(h)_=t.x,l=i.right-t.x;else{let u=Math.min(i.right-t.x+i.left,t.x),f=this._lastBoundingBoxSize.width;l=u*2,_=t.x-u,l>f&&!this._isInitialRender&&!this._growAfterOpen&&(_=t.x-f/2)}return{top:r,left:_,bottom:a,right:y,width:l,height:n}}_setBoundingBoxStyles(t,e){let i=this._calculateBoundingBoxRect(t,e);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));let o={};if(this._hasExactPosition())o.top=o.left="0",o.bottom=o.right=o.maxHeight=o.maxWidth="",o.width=o.height="100%";else{let n=this._overlayRef.getConfig().maxHeight,r=this._overlayRef.getConfig().maxWidth;o.height=p(i.height),o.top=p(i.top),o.bottom=p(i.bottom),o.width=p(i.width),o.left=p(i.left),o.right=p(i.right),e.overlayX==="center"?o.alignItems="center":o.alignItems=e.overlayX==="end"?"flex-end":"flex-start",e.overlayY==="center"?o.justifyContent="center":o.justifyContent=e.overlayY==="bottom"?"flex-end":"flex-start",n&&(o.maxHeight=p(n)),r&&(o.maxWidth=p(r))}this._lastBoundingBoxSize=i,k(this._boundingBox.style,o)}_resetBoundingBoxStyles(){k(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){k(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(t,e){let i={},o=this._hasExactPosition(),n=this._hasFlexibleDimensions,r=this._overlayRef.getConfig();if(o){let l=this._viewportRuler.getViewportScrollPosition();k(i,this._getExactOverlayY(e,t,l)),k(i,this._getExactOverlayX(e,t,l))}else i.position="static";let a="",h=this._getOffset(e,"x"),c=this._getOffset(e,"y");h&&(a+=`translateX(${h}px) `),c&&(a+=`translateY(${c}px)`),i.transform=a.trim(),r.maxHeight&&(o?i.maxHeight=p(r.maxHeight):n&&(i.maxHeight="")),r.maxWidth&&(o?i.maxWidth=p(r.maxWidth):n&&(i.maxWidth="")),k(this._pane.style,i)}_getExactOverlayY(t,e,i){let o={top:"",bottom:""},n=this._getOverlayPoint(e,this._overlayRect,t);if(this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i)),t.overlayY==="bottom"){let r=this._document.documentElement.clientHeight;o.bottom=`${r-(n.y+this._overlayRect.height)}px`}else o.top=p(n.y);return o}_getExactOverlayX(t,e,i){let o={left:"",right:""},n=this._getOverlayPoint(e,this._overlayRect,t);this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i));let r;if(this._isRtl()?r=t.overlayX==="end"?"left":"right":r=t.overlayX==="end"?"right":"left",r==="right"){let a=this._document.documentElement.clientWidth;o.right=`${a-(n.x+this._overlayRect.width)}px`}else o.left=p(n.x);return o}_getScrollVisibility(){let t=this._getOriginRect(),e=this._pane.getBoundingClientRect(),i=this._scrollables.map(o=>o.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:xt(t,i),isOriginOutsideView:J(t,i),isOverlayClipped:xt(e,i),isOverlayOutsideView:J(e,i)}}_subtractOverflows(t,...e){return e.reduce((i,o)=>i-Math.max(o,0),t)}_getNarrowedViewportRect(){let t=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+t-this._viewportMargin,bottom:i.top+e-this._viewportMargin,width:t-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(t,e){return e==="x"?t.offsetX==null?this._offsetX:t.offsetX:t.offsetY==null?this._offsetY:t.offsetY}_validatePositions(){}_addPanelClasses(t){this._pane&&G(t).forEach(e=>{e!==""&&this._appliedPanelClasses.indexOf(e)===-1&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(t=>{this._pane.classList.remove(t)}),this._appliedPanelClasses=[])}_getOriginRect(){let t=this._origin;if(t instanceof B)return t.nativeElement.getBoundingClientRect();if(t instanceof Element)return t.getBoundingClientRect();let e=t.width||0,i=t.height||0;return{top:t.y,bottom:t.y+i,left:t.x,right:t.x+e,height:i,width:e}}};function k(s,t){for(let e in t)t.hasOwnProperty(e)&&(s[e]=t[e]);return s}function Dt(s){if(typeof s!="number"&&s!=null){let[t,e]=s.split(jt);return!e||e==="px"?parseFloat(t):null}return s||null}function Bt(s){return{top:Math.floor(s.top),right:Math.floor(s.right),bottom:Math.floor(s.bottom),left:Math.floor(s.left),width:Math.floor(s.width),height:Math.floor(s.height)}}function Zt(s,t){return s===t?!0:s.isOriginClipped===t.isOriginClipped&&s.isOriginOutsideView===t.isOriginOutsideView&&s.isOverlayClipped===t.isOverlayClipped&&s.isOverlayOutsideView===t.isOverlayOutsideView}var Mt="cdk-global-overlay-wrapper";function Nt(s){return new H}var H=class{_overlayRef;_cssPosition="static";_topOffset="";_bottomOffset="";_alignItems="";_xPosition="";_xOffset="";_width="";_height="";_isDisposed=!1;attach(t){let e=t.getConfig();this._overlayRef=t,this._width&&!e.width&&t.updateSize({width:this._width}),this._height&&!e.height&&t.updateSize({height:this._height}),t.hostElement.classList.add(Mt),this._isDisposed=!1}top(t=""){return this._bottomOffset="",this._topOffset=t,this._alignItems="flex-start",this}left(t=""){return this._xOffset=t,this._xPosition="left",this}bottom(t=""){return this._topOffset="",this._bottomOffset=t,this._alignItems="flex-end",this}right(t=""){return this._xOffset=t,this._xPosition="right",this}start(t=""){return this._xOffset=t,this._xPosition="start",this}end(t=""){return this._xOffset=t,this._xPosition="end",this}width(t=""){return this._overlayRef?this._overlayRef.updateSize({width:t}):this._width=t,this}height(t=""){return this._overlayRef?this._overlayRef.updateSize({height:t}):this._height=t,this}centerHorizontally(t=""){return this.left(t),this._xPosition="center",this}centerVertically(t=""){return this.top(t),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:o,height:n,maxWidth:r,maxHeight:a}=i,h=(o==="100%"||o==="100vw")&&(!r||r==="100%"||r==="100vw"),c=(n==="100%"||n==="100vh")&&(!a||a==="100%"||a==="100vh"),l=this._xPosition,_=this._xOffset,y=this._overlayRef.getConfig().direction==="rtl",u="",f="",g="";h?g="flex-start":l==="center"?(g="center",y?f=_:u=_):y?l==="left"||l==="end"?(g="flex-end",u=_):(l==="right"||l==="start")&&(g="flex-start",f=_):l==="left"||l==="start"?(g="flex-start",u=_):(l==="right"||l==="end")&&(g="flex-end",f=_),t.position=this._cssPosition,t.marginLeft=h?"0":u,t.marginTop=c?"0":this._topOffset,t.marginBottom=this._bottomOffset,t.marginRight=h?"0":f,e.justifyContent=g,e.alignItems=c?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,i=e.style;e.classList.remove(Mt),i.justifyContent=i.alignItems=t.marginTop=t.marginBottom=t.marginLeft=t.marginRight=t.position="",this._overlayRef=null,this._isDisposed=!0}},Ht=(()=>{class s{_injector=d(w);constructor(){}global(){return Nt()}flexibleConnectedTo(e){return et(this._injector,e)}static \u0275fac=function(i){return new(i||s)};static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})();function it(s,t){s.get($).load(It);let e=s.get(Lt),i=s.get(R),o=s.get(bt),n=s.get(yt),r=s.get(q),a=i.createElement("div"),h=i.createElement("div");h.id=o.getId("cdk-overlay-"),h.classList.add("cdk-overlay-pane"),a.appendChild(h),e.getContainerElement().appendChild(a);let c=new Ct(h,n,s),l=new P(t),_=s.get(pt,null,{optional:!0})||s.get(M).createRenderer(null,null);return l.direction=l.direction||r.value,new L(c,a,h,l,s.get(O),s.get(Vt),i,s.get(vt),s.get(Tt),t?.disableAnimations??s.get(dt,null,{optional:!0})==="NoopAnimations",s.get(ht),_)}var Wt=(()=>{class s{scrollStrategies=d(Xt);_positionBuilder=d(Ht);_injector=d(w);constructor(){}create(e){return it(this._injector,e)}position(){return this._positionBuilder}static \u0275fac=function(i){return new(i||s)};static \u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}return s})(),Kt=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],zt=new lt("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let s=d(w);return()=>W(s)}}),tt=(()=>{class s{elementRef=d(B);constructor(){}static \u0275fac=function(i){return new(i||s)};static \u0275dir=j({type:s,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"]})}return s})(),$t=(()=>{class s{_dir=d(q,{optional:!0});_injector=d(w);_overlayRef;_templatePortal;_backdropSubscription=m.EMPTY;_attachSubscription=m.EMPTY;_detachSubscription=m.EMPTY;_positionSubscription=m.EMPTY;_offsetX;_offsetY;_position;_scrollStrategyFactory=d(zt);_disposeOnNavigation=!1;_ngZone=d(O);origin;positions;positionStrategy;get offsetX(){return this._offsetX}set offsetX(e){this._offsetX=e,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(e){this._offsetY=e,this._position&&this._updatePositionStrategy(this._position)}width;height;minWidth;minHeight;backdropClass;panelClass;viewportMargin=0;scrollStrategy;open=!1;disableClose=!1;transformOriginSelector;hasBackdrop=!1;lockPosition=!1;flexibleDimensions=!1;growAfterOpen=!1;push=!1;get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(e){this._disposeOnNavigation=e}backdropClick=new S;positionChange=new S;attach=new S;detach=new S;overlayKeydown=new S;overlayOutsideClick=new S;constructor(){let e=d(_t),i=d(ft);this._templatePortal=new Ot(e,i),this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef?.dispose()}ngOnChanges(e){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef?.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),e.origin&&this.open&&this._position.apply()),e.open&&(this.open?this.attachOverlay():this.detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=Kt);let e=this._overlayRef=it(this._injector,this._buildConfig());this._attachSubscription=e.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=e.detachments().subscribe(()=>this.detach.emit()),e.keydownEvents().subscribe(i=>{this.overlayKeydown.next(i),i.keyCode===27&&!this.disableClose&&!wt(i)&&(i.preventDefault(),this.detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(i=>{let o=this._getOriginElement(),n=Y(i);(!o||o!==n&&!o.contains(n))&&this.overlayOutsideClick.next(i)})}_buildConfig(){let e=this._position=this.positionStrategy||this._createPositionStrategy(),i=new P({direction:this._dir||"ltr",positionStrategy:e,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(i.width=this.width),(this.height||this.height===0)&&(i.height=this.height),(this.minWidth||this.minWidth===0)&&(i.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(i.minHeight=this.minHeight),this.backdropClass&&(i.backdropClass=this.backdropClass),this.panelClass&&(i.panelClass=this.panelClass),i}_updatePositionStrategy(e){let i=this.positions.map(o=>({originX:o.originX,originY:o.originY,overlayX:o.overlayX,overlayY:o.overlayY,offsetX:o.offsetX||this.offsetX,offsetY:o.offsetY||this.offsetY,panelClass:o.panelClass||void 0}));return e.setOrigin(this._getOrigin()).withPositions(i).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let e=et(this._injector,this._getOrigin());return this._updatePositionStrategy(e),e}_getOrigin(){return this.origin instanceof tt?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof tt?this.origin.elementRef.nativeElement:this.origin instanceof B?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(e=>{this.backdropClick.emit(e)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(rt(()=>this.positionChange.observers.length>0)).subscribe(e=>{this._ngZone.run(()=>this.positionChange.emit(e)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()})),this.open=!0}detachOverlay(){this._overlayRef?.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.open=!1}static \u0275fac=function(i){return new(i||s)};static \u0275dir=j({type:s,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",C],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",C],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",C],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",C],push:[2,"cdkConnectedOverlayPush","push",C],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",C]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],features:[ct]})}return s})();function Gt(s){let t=d(w);return()=>W(t)}var qt={provide:zt,useFactory:Gt},Ut=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275mod=gt({type:s});static \u0275inj=at({providers:[Wt,qt],imports:[St,kt,U,U]})}return s})();export{At as a,W as b,P as c,Lt as d,L as e,et as f,Nt as g,it as h,tt as i,$t as j,Ut as k};
