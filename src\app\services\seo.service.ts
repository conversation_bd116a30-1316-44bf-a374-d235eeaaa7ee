import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Meta, Title } from '@angular/platform-browser';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class SEOService {
  private defaultImage = 'https://ik.imagekit.io/quadrate/assets/img/qts-og-image.jpg';
  private defaultUrl = 'https://quadratetechsolutions.com';
  private siteName = 'Quadrate Tech Solutions';

  constructor(
    private meta: Meta,
    private title: Title,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  updateSEO(data: SEOData): void {
    // Update title
    this.title.setTitle(data.title);

    // Update meta tags
    this.meta.updateTag({ name: 'description', content: data.description });
    
    if (data.keywords) {
      this.meta.updateTag({ name: 'keywords', content: data.keywords });
    }

    this.meta.updateTag({ name: 'author', content: data.author || 'Quadrate Tech Solutions' });

    // Open Graph tags
    this.meta.updateTag({ property: 'og:title', content: data.title });
    this.meta.updateTag({ property: 'og:description', content: data.description });
    this.meta.updateTag({ property: 'og:image', content: data.image || this.defaultImage });
    this.meta.updateTag({ property: 'og:url', content: data.url || this.defaultUrl });
    this.meta.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.meta.updateTag({ property: 'og:site_name', content: this.siteName });

    // Twitter Card tags
    this.meta.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:title', content: data.title });
    this.meta.updateTag({ name: 'twitter:description', content: data.description });
    this.meta.updateTag({ name: 'twitter:image', content: data.image || this.defaultImage });

    // Article specific tags
    if (data.type === 'article') {
      if (data.publishedTime) {
        this.meta.updateTag({ property: 'article:published_time', content: data.publishedTime });
      }
      if (data.modifiedTime) {
        this.meta.updateTag({ property: 'article:modified_time', content: data.modifiedTime });
      }
      if (data.section) {
        this.meta.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        // Remove existing article:tag tags
        this.meta.removeTag('property="article:tag"');
        // Add new tags
        data.tags.forEach(tag => {
          this.meta.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Add structured data
    this.addStructuredData(data);
  }

  private addStructuredData(data: SEOData): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Create new structured data
    const structuredData = this.createStructuredData(data);
    
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    document.head.appendChild(script);
  }

  private createStructuredData(data: SEOData): any {
    const baseStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Quadrate Tech Solutions',
      description: 'Engineering Business Momentum Through Technology - AI/ML Solutions, Custom Development, Cloud Solutions',
      url: this.defaultUrl,
      logo: this.defaultImage,
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-0123',
        contactType: 'customer service',
        availableLanguage: 'English'
      },
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'US'
      },
      sameAs: [
        'https://linkedin.com/company/quadrate-tech-solutions',
        'https://github.com/quadrate-tech-solutions'
      ],
      foundingDate: '2020',
      numberOfEmployees: '10-50',
      industry: 'Information Technology',
      services: [
        'AI/ML Solutions',
        'Custom Software Development',
        'Cloud Solutions',
        'Data Engineering',
        'Computer Vision',
        'Natural Language Processing'
      ]
    };

    // Add page-specific structured data
    if (data.type === 'article') {
      return {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: data.title,
        description: data.description,
        image: data.image || this.defaultImage,
        author: {
          '@type': 'Organization',
          name: 'Quadrate Tech Solutions'
        },
        publisher: {
          '@type': 'Organization',
          name: 'Quadrate Tech Solutions',
          logo: {
            '@type': 'ImageObject',
            url: this.defaultImage
          }
        },
        datePublished: data.publishedTime,
        dateModified: data.modifiedTime || data.publishedTime
      };
    }

    return baseStructuredData;
  }

  // Predefined SEO data for different pages
  getHomeSEO(): SEOData {
    return {
      title: 'Quadrate Tech Solutions - Engineering Business Momentum Through Technology',
      description: 'Transform your business with AI/ML solutions, custom development, and cloud services. Increase efficiency by 40%, reduce costs, and gain competitive advantages with our integrated technology solutions.',
      keywords: 'AI ML solutions, custom software development, cloud solutions, business automation, digital transformation, technology consulting, enterprise software, data engineering',
      image: this.defaultImage,
      url: this.defaultUrl,
      type: 'website'
    };
  }

  getAboutSEO(): SEOData {
    return {
      title: 'About Us - Expert Technology Team | Quadrate Tech Solutions',
      description: 'Meet our expert team of AI/ML engineers, full-stack developers, and cloud architects. Learn about our mission to engineer business momentum through innovative technology solutions.',
      keywords: 'technology team, AI experts, software developers, cloud architects, company culture, technology leadership',
      url: `${this.defaultUrl}/about`,
      type: 'website'
    };
  }

  getServicesSEO(): SEOData {
    return {
      title: 'Technology Services - AI/ML, Development, Cloud | Quadrate Tech Solutions',
      description: 'Comprehensive technology services including AI/ML solutions, custom development, cloud migration, and data engineering. Proven to increase efficiency by 40% and reduce costs by 30%.',
      keywords: 'AI ML services, custom development, cloud migration, data engineering, computer vision, NLP, enterprise solutions',
      url: `${this.defaultUrl}/services`,
      type: 'website'
    };
  }

  getCaseStudiesSEO(): SEOData {
    return {
      title: 'Success Stories & Case Studies | Quadrate Tech Solutions',
      description: 'Discover how we\'ve helped businesses achieve 40% efficiency increases, $2M+ annual savings, and 99.9% system uptime through our technology solutions.',
      keywords: 'case studies, success stories, client results, technology ROI, business transformation, efficiency improvements',
      url: `${this.defaultUrl}/case-studies`,
      type: 'website'
    };
  }

  getCareersSEO(): SEOData {
    return {
      title: 'Careers - Join Our Technology Team | Quadrate Tech Solutions',
      description: 'Join our mission to engineer business momentum. Remote-first culture, competitive compensation, and opportunities to work on cutting-edge AI/ML and cloud projects.',
      keywords: 'technology careers, AI ML jobs, software developer jobs, remote work, technology team, career opportunities',
      url: `${this.defaultUrl}/careers`,
      type: 'website'
    };
  }

  getContactSEO(): SEOData {
    return {
      title: 'Contact Us - Start Your Technology Project | Quadrate Tech Solutions',
      description: 'Ready to transform your business with technology? Contact our expert team to discuss AI/ML solutions, custom development, and cloud services. Free consultation available.',
      keywords: 'contact technology company, AI ML consultation, custom development quote, cloud services contact',
      url: `${this.defaultUrl}/contact`,
      type: 'website'
    };
  }
}
