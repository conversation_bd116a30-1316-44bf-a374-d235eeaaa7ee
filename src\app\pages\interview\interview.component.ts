import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { interval, Subscription } from 'rxjs';

interface Question {
  id: number;
  category: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
}

interface AssessmentResult {
  score: number;
  totalQuestions: number;
  percentage: number;
  categoryScores: { [key: string]: { correct: number; total: number } };
  timeSpent: number;
  recommendations: string[];
}

@Component({
  selector: 'app-interview',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatRadioModule,
    MatProgressBarModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule
  ],
  template: `
    <div class="interview-page">
      @if (!assessmentStarted) {
        <!-- Welcome Section -->
        <div class="welcome-section">
          <div class="container">
            <h1>Technical Interview Assessment</h1>
            <p class="welcome-subtitle">
              Test your technical knowledge with our comprehensive assessment platform.
              Get personalized feedback and recommendations for your career growth.
            </p>

            <div class="assessment-info">
              <div class="info-grid">
                <div class="info-card">
                  <mat-icon>quiz</mat-icon>
                  <h3>{{ questions.length }} Questions</h3>
                  <p>Covering multiple technical domains</p>
                </div>
                <div class="info-card">
                  <mat-icon>timer</mat-icon>
                  <h3>{{ totalTimeMinutes }} Minutes</h3>
                  <p>Timed assessment for realistic experience</p>
                </div>
                <div class="info-card">
                  <mat-icon>analytics</mat-icon>
                  <h3>Detailed Report</h3>
                  <p>Comprehensive analysis and recommendations</p>
                </div>
              </div>
            </div>

            <div class="categories-section">
              <h2>Assessment Categories</h2>
              <div class="categories-grid">
                @for (category of categories; track category.name) {
                  <div class="category-card">
                    <mat-icon [style.color]="category.color">{{ category.icon }}</mat-icon>
                    <h3>{{ category.name }}</h3>
                    <p>{{ category.description }}</p>
                    <span class="question-count">{{ category.questionCount }} questions</span>
                  </div>
                }
              </div>
            </div>

            <div class="start-section">
              <h2>Ready to Begin?</h2>
              <p>Make sure you have a stable internet connection and won't be interrupted.</p>
              <button mat-raised-button color="primary" (click)="startAssessment()" class="start-button">
                Start Assessment
              </button>
            </div>
          </div>
        </div>
      }

      @if (assessmentStarted && !assessmentCompleted) {
        <!-- Assessment Section -->
        <div class="assessment-section">
          <div class="container">
            <!-- Progress Header -->
            <div class="progress-header">
              <div class="progress-info">
                <span class="question-counter">
                  Question {{ currentQuestionIndex + 1 }} of {{ questions.length }}
                </span>
                <span class="time-remaining">
                  Time: {{ formatTime(timeRemaining) }}
                </span>
              </div>
              <mat-progress-bar
                mode="determinate"
                [value]="(currentQuestionIndex / questions.length) * 100">
              </mat-progress-bar>
            </div>

            <!-- Question Card -->
            <mat-card class="question-card">
              <mat-card-header>
                <div class="question-header">
                  <span class="category-badge" [style.background-color]="getCurrentCategoryColor()">
                    {{ currentQuestion.category }}
                  </span>
                  <span class="difficulty-badge" [class]="'difficulty-' + currentQuestion.difficulty.toLowerCase()">
                    {{ currentQuestion.difficulty }}
                  </span>
                </div>
              </mat-card-header>
              <mat-card-content>
                <h2 class="question-text">{{ currentQuestion.question }}</h2>
                <form [formGroup]="answerForm" class="answer-form">
                  <mat-radio-group formControlName="selectedAnswer" class="answer-options">
                    @for (option of currentQuestion.options; track $index) {
                      <mat-radio-button [value]="$index" class="answer-option">
                        {{ option }}
                      </mat-radio-button>
                    }
                  </mat-radio-group>
                </form>
              </mat-card-content>
              <mat-card-actions class="question-actions">
                <button mat-button
                        (click)="previousQuestion()"
                        [disabled]="currentQuestionIndex === 0">
                  Previous
                </button>
                <div class="spacer"></div>
                @if (currentQuestionIndex === questions.length - 1) {
                  <button mat-raised-button
                          color="primary"
                          (click)="completeAssessment()"
                          [disabled]="!answerForm.valid">
                    Complete Assessment
                  </button>
                } @else {
                  <button mat-raised-button
                          color="primary"
                          (click)="nextQuestion()"
                          [disabled]="!answerForm.valid">
                    Next Question
                  </button>
                }
              </mat-card-actions>
            </mat-card>
          </div>
        </div>
      }

      @if (assessmentCompleted && results) {
        <!-- Results Section -->
        <div class="results-section">
          <div class="container">
            <div class="results-header">
              <h1>Assessment Complete!</h1>
              <p>Here's your detailed performance analysis</p>
            </div>

            <!-- Score Overview -->
            <mat-card class="score-card">
              <mat-card-content>
                <div class="score-overview">
                  <div class="score-circle">
                    <span class="score-percentage">{{ results.percentage }}%</span>
                    <span class="score-label">Overall Score</span>
                  </div>
                  <div class="score-details">
                    <div class="score-item">
                      <span class="label">Correct Answers:</span>
                      <span class="value">{{ results.score }} / {{ results.totalQuestions }}</span>
                    </div>
                    <div class="score-item">
                      <span class="label">Time Spent:</span>
                      <span class="value">{{ formatTime(results.timeSpent) }}</span>
                    </div>
                    <div class="score-item">
                      <span class="label">Performance:</span>
                      <span class="value" [class]="getPerformanceClass(results.percentage)">
                        {{ getPerformanceLabel(results.percentage) }}
                      </span>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Category Breakdown -->
            <mat-card class="category-breakdown">
              <mat-card-header>
                <mat-card-title>Category Performance</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="category-scores">
                  @for (category of Object.keys(results.categoryScores); track category) {
                    <div class="category-score">
                      <div class="category-info">
                        <span class="category-name">{{ category }}</span>
                        <span class="category-percentage">
                          {{ getCategoryPercentage(category) }}%
                        </span>
                      </div>
                      <mat-progress-bar
                        mode="determinate"
                        [value]="getCategoryPercentage(category)"
                        [color]="getCategoryPercentage(category) >= 70 ? 'primary' : 'warn'">
                      </mat-progress-bar>
                      <span class="category-fraction">
                        {{ results.categoryScores[category].correct }} / {{ results.categoryScores[category].total }}
                      </span>
                    </div>
                  }
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Recommendations -->
            <mat-card class="recommendations">
              <mat-card-header>
                <mat-card-title>Personalized Recommendations</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <ul class="recommendations-list">
                  @for (recommendation of results.recommendations; track recommendation) {
                    <li>
                      <mat-icon>lightbulb</mat-icon>
                      <span>{{ recommendation }}</span>
                    </li>
                  }
                </ul>
              </mat-card-content>
            </mat-card>

            <!-- Actions -->
            <div class="results-actions">
              <button mat-raised-button color="primary" (click)="restartAssessment()">
                Take Assessment Again
              </button>
              <button mat-stroked-button (click)="downloadResults()">
                Download Report
              </button>
            </div>
          </div>
        </div>
      }
    </div>
  `,
  styles: [`
    .interview-page {
      padding-top: 0;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    /* Welcome Section */
    .welcome-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
    }

    .welcome-section h1 {
      font-size: 3rem;
      text-align: center;
      margin-bottom: 1rem;
      font-weight: bold;
    }

    .welcome-subtitle {
      font-size: 1.2rem;
      text-align: center;
      max-width: 800px;
      margin: 0 auto 3rem;
      opacity: 0.9;
    }

    .assessment-info {
      margin: 3rem 0;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }

    .info-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 2rem;
      border-radius: 8px;
      text-align: center;
      backdrop-filter: blur(10px);
    }

    .info-card mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
      color: white;
    }

    .info-card h3 {
      margin-bottom: 0.5rem;
      font-size: 1.5rem;
    }

    .categories-section {
      background: white;
      color: #333;
      padding: 3rem;
      border-radius: 12px;
      margin: 3rem 0;
    }

    .categories-section h2 {
      text-align: center;
      margin-bottom: 2rem;
      color: #0607E1;
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }

    .category-card {
      padding: 1.5rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      text-align: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .category-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .category-card mat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      margin-bottom: 1rem;
    }

    .category-card h3 {
      margin-bottom: 0.5rem;
      color: #333;
    }

    .category-card p {
      color: #666;
      margin-bottom: 1rem;
    }

    .question-count {
      background: #0607E1;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
    }

    .start-section {
      text-align: center;
      margin-top: 3rem;
    }

    .start-section h2 {
      margin-bottom: 1rem;
    }

    .start-section p {
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .start-button {
      font-size: 1.2rem;
      padding: 12px 32px;
    }

    /* Assessment Section */
    .assessment-section {
      padding: 2rem 0;
      background-color: #f8f9fa;
      min-height: 100vh;
    }

    .progress-header {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .question-counter {
      font-weight: 600;
      color: #333;
    }

    .time-remaining {
      font-weight: 600;
      color: #0607E1;
    }

    .question-card {
      margin-bottom: 2rem;
    }

    .question-header {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .category-badge {
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .difficulty-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .difficulty-easy {
      background: #10B981;
      color: white;
    }

    .difficulty-medium {
      background: #F59E0B;
      color: white;
    }

    .difficulty-hard {
      background: #EF4444;
      color: white;
    }

    .question-text {
      font-size: 1.3rem;
      margin: 1.5rem 0;
      line-height: 1.5;
      color: #333;
    }

    .answer-options {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .answer-option {
      padding: 1rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      transition: background-color 0.3s ease;
    }

    .answer-option:hover {
      background-color: #f5f5f5;
    }

    .question-actions {
      display: flex;
      align-items: center;
      padding: 1rem 1.5rem;
    }

    .spacer {
      flex: 1;
    }

    /* Results Section */
    .results-section {
      padding: 4rem 0;
      background-color: #f8f9fa;
    }

    .results-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .results-header h1 {
      color: #0607E1;
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .score-card {
      margin-bottom: 2rem;
    }

    .score-overview {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 3rem;
      align-items: center;
    }

    .score-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 150px;
      height: 150px;
      border: 8px solid #0607E1;
      border-radius: 50%;
      text-align: center;
    }

    .score-percentage {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0607E1;
    }

    .score-label {
      font-size: 0.9rem;
      color: #666;
      margin-top: 0.5rem;
    }

    .score-details {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .score-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .score-item .label {
      font-weight: 600;
      color: #333;
    }

    .score-item .value {
      font-weight: 600;
    }

    .value.excellent {
      color: #10B981;
    }

    .value.good {
      color: #0607E1;
    }

    .value.average {
      color: #F59E0B;
    }

    .value.poor {
      color: #EF4444;
    }

    .category-breakdown {
      margin-bottom: 2rem;
    }

    .category-scores {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .category-score {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .category-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .category-name {
      font-weight: 600;
      color: #333;
    }

    .category-percentage {
      font-weight: 600;
      color: #0607E1;
    }

    .category-fraction {
      font-size: 0.9rem;
      color: #666;
      text-align: right;
    }

    .recommendations {
      margin-bottom: 2rem;
    }

    .recommendations-list {
      list-style: none;
      padding: 0;
    }

    .recommendations-list li {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 1rem;
      padding: 1rem;
      background: #f0f0ff;
      border-radius: 8px;
    }

    .recommendations-list mat-icon {
      color: #0607E1;
      margin-top: 0.2rem;
    }

    .results-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .welcome-section h1 {
        font-size: 2rem;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .categories-grid {
        grid-template-columns: 1fr;
      }

      .score-overview {
        grid-template-columns: 1fr;
        text-align: center;
      }

      .progress-info {
        flex-direction: column;
        gap: 0.5rem;
      }

      .question-actions {
        flex-direction: column;
        gap: 1rem;
      }

      .spacer {
        display: none;
      }
    }
  `]
})
export class InterviewComponent implements OnInit, OnDestroy {
  assessmentStarted = false;
  assessmentCompleted = false;
  currentQuestionIndex = 0;
  timeRemaining = 0;
  totalTimeMinutes = 45;
  answers: number[] = [];
  results: AssessmentResult | null = null;
  answerForm: FormGroup;

  private timerSubscription?: Subscription;
  private startTime = 0;

  Object = Object; // Make Object available in template

  categories = [
    {
      name: 'JavaScript/TypeScript',
      icon: 'code',
      color: '#F7DF1E',
      description: 'Modern JavaScript and TypeScript concepts',
      questionCount: 8
    },
    {
      name: 'React/Angular',
      icon: 'web',
      color: '#61DAFB',
      description: 'Frontend frameworks and best practices',
      questionCount: 6
    },
    {
      name: 'Node.js/Backend',
      icon: 'storage',
      color: '#339933',
      description: 'Server-side development and APIs',
      questionCount: 5
    },
    {
      name: 'Database & SQL',
      icon: 'database',
      color: '#336791',
      description: 'Database design and query optimization',
      questionCount: 4
    },
    {
      name: 'System Design',
      icon: 'architecture',
      color: '#FF6B6B',
      description: 'Scalable system architecture',
      questionCount: 3
    },
    {
      name: 'DevOps & Cloud',
      icon: 'cloud',
      color: '#4285F4',
      description: 'Deployment and cloud technologies',
      questionCount: 4
    }
  ];

  questions: Question[] = [
    {
      id: 1,
      category: 'JavaScript/TypeScript',
      question: 'What is the difference between `let`, `const`, and `var` in JavaScript?',
      options: [
        'They are all the same, just different syntax',
        '`var` is function-scoped, `let` and `const` are block-scoped, `const` cannot be reassigned',
        '`let` is for numbers, `const` is for strings, `var` is for objects',
        'Only `var` can be used in functions'
      ],
      correctAnswer: 1,
      explanation: '`var` is function-scoped and can be redeclared, while `let` and `const` are block-scoped. `const` cannot be reassigned after declaration.',
      difficulty: 'Easy'
    },
    {
      id: 2,
      category: 'React/Angular',
      question: 'What is the purpose of React hooks?',
      options: [
        'To replace class components entirely',
        'To allow functional components to use state and lifecycle methods',
        'To improve performance only',
        'To handle routing in React applications'
      ],
      correctAnswer: 1,
      explanation: 'React hooks allow functional components to use state and other React features that were previously only available in class components.',
      difficulty: 'Medium'
    },
    {
      id: 3,
      category: 'Node.js/Backend',
      question: 'What is middleware in Express.js?',
      options: [
        'A database connection layer',
        'Functions that execute during the request-response cycle',
        'A templating engine',
        'A testing framework'
      ],
      correctAnswer: 1,
      explanation: 'Middleware functions are functions that have access to the request object, response object, and the next middleware function in the application\'s request-response cycle.',
      difficulty: 'Medium'
    },
    {
      id: 4,
      category: 'Database & SQL',
      question: 'What is the difference between INNER JOIN and LEFT JOIN?',
      options: [
        'There is no difference',
        'INNER JOIN returns only matching records, LEFT JOIN returns all records from left table',
        'LEFT JOIN is faster than INNER JOIN',
        'INNER JOIN is used for updates, LEFT JOIN for selects'
      ],
      correctAnswer: 1,
      explanation: 'INNER JOIN returns only records that have matching values in both tables, while LEFT JOIN returns all records from the left table and matched records from the right table.',
      difficulty: 'Medium'
    },
    {
      id: 5,
      category: 'System Design',
      question: 'What is horizontal scaling?',
      options: [
        'Adding more power to existing servers',
        'Adding more servers to handle increased load',
        'Optimizing database queries',
        'Reducing server response time'
      ],
      correctAnswer: 1,
      explanation: 'Horizontal scaling involves adding more servers to your pool of resources to handle increased load, as opposed to vertical scaling which adds more power to existing servers.',
      difficulty: 'Hard'
    }
  ];

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.answerForm = this.fb.group({
      selectedAnswer: ['', Validators.required]
    });
  }

  ngOnInit() {
    // Initialize with more questions for a complete assessment
    this.initializeQuestions();
  }

  ngOnDestroy() {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }

  private initializeQuestions() {
    // In a real application, these would be loaded from a service
    // For now, we'll use the sample questions
    this.answers = new Array(this.questions.length).fill(-1);
  }

  get currentQuestion(): Question {
    return this.questions[this.currentQuestionIndex];
  }

  startAssessment() {
    this.assessmentStarted = true;
    this.startTime = Date.now();
    this.timeRemaining = this.totalTimeMinutes * 60; // Convert to seconds
    this.startTimer();
  }

  private startTimer() {
    this.timerSubscription = interval(1000).subscribe(() => {
      this.timeRemaining--;
      if (this.timeRemaining <= 0) {
        this.completeAssessment();
      }
    });
  }

  nextQuestion() {
    if (this.answerForm.valid) {
      this.answers[this.currentQuestionIndex] = this.answerForm.value.selectedAnswer;
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++;
        this.loadQuestion();
      }
    }
  }

  previousQuestion() {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--;
      this.loadQuestion();
    }
  }

  private loadQuestion() {
    const savedAnswer = this.answers[this.currentQuestionIndex];
    this.answerForm.patchValue({
      selectedAnswer: savedAnswer >= 0 ? savedAnswer : ''
    });
  }

  completeAssessment() {
    if (this.answerForm.valid) {
      this.answers[this.currentQuestionIndex] = this.answerForm.value.selectedAnswer;
    }

    this.assessmentCompleted = true;
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }

    this.calculateResults();
  }

  private calculateResults() {
    const timeSpent = this.totalTimeMinutes * 60 - this.timeRemaining;
    let score = 0;
    const categoryScores: { [key: string]: { correct: number; total: number } } = {};

    // Initialize category scores
    this.categories.forEach(cat => {
      categoryScores[cat.name] = { correct: 0, total: 0 };
    });

    // Calculate scores
    this.questions.forEach((question, index) => {
      categoryScores[question.category].total++;
      if (this.answers[index] === question.correctAnswer) {
        score++;
        categoryScores[question.category].correct++;
      }
    });

    const percentage = Math.round((score / this.questions.length) * 100);

    this.results = {
      score,
      totalQuestions: this.questions.length,
      percentage,
      categoryScores,
      timeSpent,
      recommendations: this.generateRecommendations(percentage, categoryScores)
    };
  }

  private generateRecommendations(percentage: number, categoryScores: { [key: string]: { correct: number; total: number } }): string[] {
    const recommendations: string[] = [];

    if (percentage >= 80) {
      recommendations.push('Excellent performance! You demonstrate strong technical knowledge across multiple domains.');
      recommendations.push('Consider pursuing senior-level positions or technical leadership roles.');
    } else if (percentage >= 60) {
      recommendations.push('Good foundation! Focus on strengthening areas where you scored lower.');
      recommendations.push('Practice coding problems and system design scenarios regularly.');
    } else {
      recommendations.push('Focus on fundamental concepts and practice regularly.');
      recommendations.push('Consider taking online courses or bootcamps to strengthen your skills.');
    }

    // Category-specific recommendations
    Object.keys(categoryScores).forEach(category => {
      const categoryPercentage = (categoryScores[category].correct / categoryScores[category].total) * 100;
      if (categoryPercentage < 50) {
        recommendations.push(`Improve your ${category} skills through focused study and practice.`);
      }
    });

    return recommendations;
  }

  getCurrentCategoryColor(): string {
    const category = this.categories.find(cat => cat.name === this.currentQuestion.category);
    return category?.color || '#0607E1';
  }

  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  getCategoryPercentage(category: string): number {
    if (!this.results) return 0;
    const categoryData = this.results.categoryScores[category];
    return Math.round((categoryData.correct / categoryData.total) * 100);
  }

  getPerformanceLabel(percentage: number): string {
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 70) return 'Good';
    if (percentage >= 60) return 'Average';
    return 'Needs Improvement';
  }

  getPerformanceClass(percentage: number): string {
    if (percentage >= 80) return 'excellent';
    if (percentage >= 70) return 'good';
    if (percentage >= 60) return 'average';
    return 'poor';
  }

  restartAssessment() {
    this.assessmentStarted = false;
    this.assessmentCompleted = false;
    this.currentQuestionIndex = 0;
    this.answers = new Array(this.questions.length).fill(-1);
    this.results = null;
    this.answerForm.reset();
  }

  downloadResults() {
    this.snackBar.open('Results download feature coming soon!', 'Close', {
      duration: 3000
    });
  }
}
