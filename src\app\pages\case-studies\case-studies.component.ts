import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { RouterLink } from '@angular/router';

interface CaseStudy {
  id: string;
  title: string;
  client: string;
  industry: string;
  challenge: string;
  solution: string;
  results: string[];
  technologies: string[];
  duration: string;
  teamSize: string;
  image: string;
  clientLogo: string;
  featured: boolean;
  metrics: {
    label: string;
    value: string;
    improvement: string;
  }[];
}

@Component({
  selector: 'app-case-studies',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, RouterLink],
  template: `
    <div class="case-studies-page">
      <!-- Hero Section -->
      <div class="hero-section">
        <div class="container">
          <h1 class="hero-title">Success Stories</h1>
          <p class="hero-subtitle">
            Discover how we've helped businesses transform their operations with innovative technology solutions.
            Real projects, real results, real impact.
          </p>
        </div>
      </div>

      <!-- Featured Case Study -->
      @if (featuredCaseStudy) {
        <div class="featured-section">
          <div class="container">
            <h2 class="section-title">Featured Case Study</h2>
            <div class="featured-card">
              <div class="featured-content">
                <div class="featured-header">
                  <img [src]="featuredCaseStudy.clientLogo" [alt]="featuredCaseStudy.client" class="client-logo">
                  <div class="featured-meta">
                    <h3>{{ featuredCaseStudy.title }}</h3>
                    <p class="client-info">{{ featuredCaseStudy.client }} • {{ featuredCaseStudy.industry }}</p>
                  </div>
                </div>
                
                <div class="featured-body">
                  <div class="challenge-solution">
                    <div class="challenge">
                      <h4><mat-icon>problem</mat-icon> Challenge</h4>
                      <p>{{ featuredCaseStudy.challenge }}</p>
                    </div>
                    <div class="solution">
                      <h4><mat-icon>lightbulb</mat-icon> Solution</h4>
                      <p>{{ featuredCaseStudy.solution }}</p>
                    </div>
                  </div>
                  
                  <div class="metrics-grid">
                    @for (metric of featuredCaseStudy.metrics; track metric.label) {
                      <div class="metric-card">
                        <div class="metric-value">{{ metric.value }}</div>
                        <div class="metric-label">{{ metric.label }}</div>
                        <div class="metric-improvement">{{ metric.improvement }}</div>
                      </div>
                    }
                  </div>
                </div>
              </div>
              <div class="featured-image">
                <img [src]="featuredCaseStudy.image" [alt]="featuredCaseStudy.title" loading="lazy">
              </div>
            </div>
          </div>
        </div>
      }

      <!-- Case Studies Grid -->
      <div class="case-studies-section">
        <div class="container">
          <h2 class="section-title">All Case Studies</h2>
          <div class="case-studies-grid">
            @for (caseStudy of caseStudies; track caseStudy.id) {
              <mat-card class="case-study-card" [class.featured]="caseStudy.featured">
                <div class="card-image">
                  <img [src]="caseStudy.image" [alt]="caseStudy.title" loading="lazy">
                  <div class="card-overlay">
                    <button mat-raised-button color="primary" class="view-details-btn">
                      View Details
                      <mat-icon>arrow_forward</mat-icon>
                    </button>
                  </div>
                </div>
                
                <mat-card-content class="card-content">
                  <div class="card-header">
                    <img [src]="caseStudy.clientLogo" [alt]="caseStudy.client" class="card-client-logo">
                    <div class="card-meta">
                      <h3>{{ caseStudy.title }}</h3>
                      <p class="card-client">{{ caseStudy.client }}</p>
                      <span class="industry-tag">{{ caseStudy.industry }}</span>
                    </div>
                  </div>
                  
                  <p class="card-challenge">{{ caseStudy.challenge }}</p>
                  
                  <div class="card-results">
                    <h4>Key Results:</h4>
                    <ul>
                      @for (result of caseStudy.results.slice(0, 2); track result) {
                        <li>{{ result }}</li>
                      }
                    </ul>
                  </div>
                  
                  <div class="card-technologies">
                    @for (tech of caseStudy.technologies.slice(0, 3); track tech) {
                      <mat-chip class="tech-chip">{{ tech }}</mat-chip>
                    }
                    @if (caseStudy.technologies.length > 3) {
                      <span class="more-tech">+{{ caseStudy.technologies.length - 3 }} more</span>
                    }
                  </div>
                  
                  <div class="card-footer">
                    <div class="project-info">
                      <span><mat-icon>schedule</mat-icon> {{ caseStudy.duration }}</span>
                      <span><mat-icon>group</mat-icon> {{ caseStudy.teamSize }}</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            }
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="cta-section">
        <div class="container">
          <h2>Ready to Create Your Success Story?</h2>
          <p>Let's discuss how we can help you achieve similar results for your business.</p>
          <div class="cta-buttons">
            <button mat-raised-button color="primary" routerLink="/contact">
              Start Your Project
            </button>
            <button mat-stroked-button routerLink="/services">
              Explore Our Services
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .case-studies-page {
      padding-top: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .hero-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 800;
      margin-bottom: 1rem;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
      font-size: 1.3rem;
      max-width: 800px;
      margin: 0 auto;
      opacity: 0.95;
      line-height: 1.6;
    }

    .featured-section {
      padding: 6rem 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 3rem;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .featured-card {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      align-items: center;
      background: white;
      border-radius: 20px;
      padding: 3rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(6, 7, 225, 0.1);
    }

    .featured-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .client-logo {
      width: 60px;
      height: 60px;
      object-fit: contain;
      border-radius: 8px;
    }

    .featured-meta h3 {
      font-size: 1.5rem;
      font-weight: 700;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .client-info {
      color: #666;
      margin: 0;
    }

    .challenge-solution {
      display: grid;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .challenge h4,
    .solution h4 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #333;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .challenge mat-icon {
      color: #EF4444;
    }

    .solution mat-icon {
      color: #10B981;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
    }

    .metric-card {
      text-align: center;
      padding: 1.5rem;
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      border-radius: 12px;
    }

    .metric-value {
      font-size: 2rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
    }

    .metric-label {
      font-size: 0.9rem;
      opacity: 0.9;
      margin-bottom: 0.25rem;
    }

    .metric-improvement {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .featured-image img {
      width: 100%;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .case-studies-section {
      padding: 6rem 0;
      background: #f8f9fa;
    }

    .case-studies-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .case-study-card {
      border-radius: 16px !important;
      overflow: hidden !important;
      transition: all 0.3s ease !important;
      background: white !important;
    }

    .case-study-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .card-image {
      position: relative;
      height: 200px;
      overflow: hidden;
    }

    .card-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .case-study-card:hover .card-image img {
      transform: scale(1.05);
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(6, 7, 225, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .case-study-card:hover .card-overlay {
      opacity: 1;
    }

    .view-details-btn {
      background: white !important;
      color: #0607E1 !important;
    }

    .card-content {
      padding: 2rem !important;
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .card-client-logo {
      width: 40px;
      height: 40px;
      object-fit: contain;
    }

    .card-meta h3 {
      font-size: 1.2rem;
      font-weight: 700;
      color: #333;
      margin: 0 0 0.25rem 0;
    }

    .card-client {
      color: #666;
      margin: 0 0 0.5rem 0;
      font-size: 0.9rem;
    }

    .industry-tag {
      background: rgba(6, 7, 225, 0.1);
      color: #0607E1;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .card-challenge {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .card-results h4 {
      color: #333;
      font-weight: 600;
      margin-bottom: 0.75rem;
      font-size: 1rem;
    }

    .card-results ul {
      margin: 0 0 1.5rem 0;
      padding-left: 1.5rem;
    }

    .card-results li {
      color: #666;
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    .card-technologies {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }

    .tech-chip {
      background: rgba(6, 7, 225, 0.1) !important;
      color: #0607E1 !important;
      font-size: 0.8rem !important;
    }

    .more-tech {
      color: #666;
      font-size: 0.8rem;
      align-self: center;
    }

    .card-footer {
      border-top: 1px solid #eee;
      padding-top: 1rem;
    }

    .project-info {
      display: flex;
      gap: 1rem;
      font-size: 0.9rem;
      color: #666;
    }

    .project-info span {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .project-info mat-icon {
      font-size: 1rem !important;
      width: 1rem !important;
      height: 1rem !important;
    }

    .cta-section {
      background: linear-gradient(135deg, #0607E1 0%, #4D0AFF 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }

      .featured-card {
        grid-template-columns: 1fr;
        padding: 2rem;
      }

      .case-studies-grid {
        grid-template-columns: 1fr;
      }

      .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class CaseStudiesComponent implements OnInit {
  caseStudies: CaseStudy[] = [
    {
      id: '1',
      title: 'AI-Powered Inventory Management System',
      client: 'TechCorp Solutions',
      industry: 'Technology',
      challenge: 'Manual inventory tracking was causing significant delays and errors, leading to stockouts and overstock situations that cost the company $2M annually.',
      solution: 'Developed a custom AI-powered inventory management system with predictive analytics, automated reordering, and real-time tracking capabilities.',
      results: [
        'Reduced inventory costs by 35%',
        'Eliminated stockouts by 90%',
        'Improved order accuracy to 99.8%',
        'Saved 20 hours per week in manual processing'
      ],
      technologies: ['Python', 'TensorFlow', 'React', 'PostgreSQL', 'AWS', 'Docker'],
      duration: '6 months',
      teamSize: '5 developers',
      image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&h=400&fit=crop',
      clientLogo: 'https://via.placeholder.com/100x50/0607E1/FFFFFF?text=TechCorp',
      featured: true,
      metrics: [
        { label: 'Cost Reduction', value: '35%', improvement: 'vs previous year' },
        { label: 'Accuracy', value: '99.8%', improvement: 'order accuracy' },
        { label: 'Time Saved', value: '20hrs', improvement: 'per week' },
        { label: 'ROI', value: '300%', improvement: 'in first year' }
      ]
    },
    {
      id: '2',
      title: 'Cloud Migration & Modernization',
      client: 'Global Manufacturing Inc.',
      industry: 'Manufacturing',
      challenge: 'Legacy on-premise infrastructure was limiting scalability and increasing maintenance costs significantly.',
      solution: 'Comprehensive cloud migration strategy with microservices architecture, automated CI/CD pipelines, and enhanced security measures.',
      results: [
        'Reduced infrastructure costs by 40%',
        'Improved system uptime to 99.9%',
        'Accelerated deployment cycles by 80%',
        'Enhanced security compliance'
      ],
      technologies: ['AWS', 'Kubernetes', 'Docker', 'Terraform', 'Jenkins', 'Node.js'],
      duration: '8 months',
      teamSize: '8 developers',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop',
      clientLogo: 'https://via.placeholder.com/100x50/4D0AFF/FFFFFF?text=GMI',
      featured: false,
      metrics: [
        { label: 'Cost Savings', value: '40%', improvement: 'infrastructure costs' },
        { label: 'Uptime', value: '99.9%', improvement: 'system availability' },
        { label: 'Deployment', value: '80%', improvement: 'faster cycles' }
      ]
    },
    {
      id: '3',
      title: 'Real-time Analytics Dashboard',
      client: 'FinanceFirst Bank',
      industry: 'Finance',
      challenge: 'Decision makers lacked real-time insights into key business metrics, leading to delayed responses to market changes.',
      solution: 'Built a comprehensive real-time analytics dashboard with interactive visualizations, automated reporting, and predictive insights.',
      results: [
        'Reduced reporting time by 75%',
        'Improved decision-making speed by 60%',
        'Increased data accuracy to 99.5%',
        'Enhanced regulatory compliance'
      ],
      technologies: ['React', 'D3.js', 'Python', 'Apache Kafka', 'MongoDB', 'Redis'],
      duration: '4 months',
      teamSize: '6 developers',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop',
      clientLogo: 'https://via.placeholder.com/100x50/06B6D4/FFFFFF?text=FFB',
      featured: false,
      metrics: [
        { label: 'Reporting Time', value: '75%', improvement: 'reduction' },
        { label: 'Decision Speed', value: '60%', improvement: 'faster' },
        { label: 'Data Accuracy', value: '99.5%', improvement: 'precision' }
      ]
    }
  ];

  featuredCaseStudy: CaseStudy | null = null;

  ngOnInit() {
    this.featuredCaseStudy = this.caseStudies.find(cs => cs.featured) || null;
  }
}
