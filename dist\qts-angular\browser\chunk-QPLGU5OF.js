var RD=Object.defineProperty,xD=Object.defineProperties;var OD=Object.getOwnPropertyDescriptors;var ii=Object.getOwnPropertySymbols;var jf=Object.prototype.hasOwnProperty,Bf=Object.prototype.propertyIsEnumerable;var Lf=(e,t,n)=>t in e?RD(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,B=(e,t)=>{for(var n in t||={})jf.call(t,n)&&Lf(e,n,t[n]);if(ii)for(var n of ii(t))Bf.call(t,n)&&Lf(e,n,t[n]);return e},te=(e,t)=>xD(e,OD(t));var Vf=(e,t)=>{var n={};for(var r in e)jf.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&ii)for(var r of ii(e))t.indexOf(r)<0&&Bf.call(e,r)&&(n[r]=e[r]);return n};var ot=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(u){o(u)}},s=c=>{try{a(n.throw(c))}catch(u){o(u)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function A(e){return typeof e=="function"}function Zn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var si=Zn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function an(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Z=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(A(r))try{r()}catch(i){t=i instanceof si?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Uf(i)}catch(s){t=t??[],s instanceof si?t=[...t,...s.errors]:t.push(s)}}if(t)throw new si(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Uf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&an(n,t)}remove(t){let{_finalizers:n}=this;n&&an(n,t),t instanceof e&&t._removeParent(this)}};Z.EMPTY=(()=>{let e=new Z;return e.closed=!0,e})();var Ja=Z.EMPTY;function ai(e){return e instanceof Z||e&&"closed"in e&&A(e.remove)&&A(e.add)&&A(e.unsubscribe)}function Uf(e){A(e)?e():e.unsubscribe()}var ci=class extends Z{constructor(t,n){super()}schedule(t,n=0){return this}};var Vr={setInterval(e,t,...n){let{delegate:r}=Vr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Vr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var ui=class extends ci{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Vr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Vr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,an(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Ur={now(){return(Ur.delegate||Date).now()},delegate:void 0};var Yn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Yn.now=Ur.now;var li=class extends Yn{constructor(t,n=Yn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var cn=new li(ui),Hf=cn;var qe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Kn={setTimeout(e,t,...n){let{delegate:r}=Kn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Kn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function di(e){Kn.setTimeout(()=>{let{onUnhandledError:t}=qe;if(t)t(e);else throw e})}function Hr(){}var $f=ec("C",void 0,void 0);function zf(e){return ec("E",void 0,e)}function Gf(e){return ec("N",e,void 0)}function ec(e,t,n){return{kind:e,value:t,error:n}}var un=null;function Qn(e){if(qe.useDeprecatedSynchronousErrorHandling){let t=!un;if(t&&(un={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=un;if(un=null,n)throw r}}else e()}function Wf(e){qe.useDeprecatedSynchronousErrorHandling&&un&&(un.errorThrown=!0,un.error=e)}var ln=class extends Z{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ai(t)&&t.add(this)):this.destination=PD}static create(t,n,r){return new It(t,n,r)}next(t){this.isStopped?nc(Gf(t),this):this._next(t)}error(t){this.isStopped?nc(zf(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?nc($f,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},FD=Function.prototype.bind;function tc(e,t){return FD.call(e,t)}var rc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){fi(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){fi(r)}else fi(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){fi(n)}}},It=class extends ln{constructor(t,n,r){super();let o;if(A(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&qe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&tc(t.next,i),error:t.error&&tc(t.error,i),complete:t.complete&&tc(t.complete,i)}):o=t}this.destination=new rc(o)}};function fi(e){qe.useDeprecatedSynchronousErrorHandling?Wf(e):di(e)}function kD(e){throw e}function nc(e,t){let{onStoppedNotification:n}=qe;n&&Kn.setTimeout(()=>n(e,t))}var PD={closed:!0,next:Hr,error:kD,complete:Hr};var Xn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ye(e){return e}function LD(...e){return oc(e)}function oc(e){return e.length===0?ye:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var x=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=BD(n)?n:new It(n,r,o);return Qn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=qf(r),new r((o,i)=>{let s=new It({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Xn](){return this}pipe(...n){return oc(n)(this)}toPromise(n){return n=qf(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function qf(e){var t;return(t=e??qe.Promise)!==null&&t!==void 0?t:Promise}function jD(e){return e&&A(e.next)&&A(e.error)&&A(e.complete)}function BD(e){return e&&e instanceof ln||jD(e)&&ai(e)}function pi(e){return e&&A(e.schedule)}function Zf(e){return e instanceof Date&&!isNaN(e)}function $r(e=0,t,n=Hf){let r=-1;return t!=null&&(pi(t)?n=t:r=t),new x(o=>{let i=Zf(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function VD(e=0,t=cn){return e<0&&(e=0),$r(e,e,t)}function ic(e){return A(e?.lift)}function N(e){return t=>{if(ic(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function M(e,t,n,r,o){return new sc(e,t,n,r,o)}var sc=class extends ln{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function ac(){return N((e,t)=>{let n=null;e._refCount++;let r=M(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var cc=class extends x{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,ic(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Z;let n=this.getSubject();t.add(this.source.subscribe(M(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Z.EMPTY)}return t}refCount(){return ac()(this)}};var Yf=Zn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var $=(()=>{class e extends x{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new hi(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Yf}next(n){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ja:(this.currentObservers=null,i.push(n),new Z(()=>{this.currentObservers=null,an(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new x;return n.source=this,n}}return e.create=(t,n)=>new hi(t,n),e})(),hi=class extends ${constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ja}};var dn=class extends ${constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var gi=class extends ${constructor(t=1/0,n=1/0,r=Ur){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var fn=new x(e=>e.complete());function uc(e){return e[e.length-1]}function mi(e){return A(uc(e))?e.pop():void 0}function it(e){return pi(uc(e))?e.pop():void 0}function Kf(e,t){return typeof uc(e)=="number"?e.pop():t}function Xf(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Qf(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function pn(e){return this instanceof pn?(this.v=e,this):new pn(e)}function Jf(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(y){return new Promise(function(m,v){i.push([f,y,m,v])>1||c(f,y)})},g&&(o[f]=g(o[f])))}function c(f,g){try{u(r[f](g))}catch(y){p(i[0][3],y)}}function u(f){f.value instanceof pn?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function p(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ep(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Qf=="function"?Qf(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var yi=e=>e&&typeof e.length=="number"&&typeof e!="function";function vi(e){return A(e?.then)}function Ei(e){return A(e[Xn])}function Di(e){return Symbol.asyncIterator&&A(e?.[Symbol.asyncIterator])}function _i(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function UD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ii=UD();function Ci(e){return A(e?.[Ii])}function wi(e){return Jf(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield pn(n.read());if(o)return yield pn(void 0);yield yield pn(r)}}finally{n.releaseLock()}})}function bi(e){return A(e?.getReader)}function z(e){if(e instanceof x)return e;if(e!=null){if(Ei(e))return HD(e);if(yi(e))return $D(e);if(vi(e))return zD(e);if(Di(e))return tp(e);if(Ci(e))return GD(e);if(bi(e))return WD(e)}throw _i(e)}function HD(e){return new x(t=>{let n=e[Xn]();if(A(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function $D(e){return new x(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function zD(e){return new x(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,di)})}function GD(e){return new x(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function tp(e){return new x(t=>{qD(e,t).catch(n=>t.error(n))})}function WD(e){return tp(wi(e))}function qD(e,t){var n,r,o,i;return Xf(this,void 0,void 0,function*(){try{for(n=ep(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Ce(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ti(e,t=0){return N((n,r)=>{n.subscribe(M(r,o=>Ce(r,e,()=>r.next(o),t),()=>Ce(r,e,()=>r.complete(),t),o=>Ce(r,e,()=>r.error(o),t)))})}function Mi(e,t=0){return N((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function np(e,t){return z(e).pipe(Mi(t),Ti(t))}function rp(e,t){return z(e).pipe(Mi(t),Ti(t))}function op(e,t){return new x(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ip(e,t){return new x(n=>{let r;return Ce(n,t,()=>{r=e[Ii](),Ce(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>A(r?.return)&&r.return()})}function Si(e,t){if(!e)throw new Error("Iterable cannot be null");return new x(n=>{Ce(n,t,()=>{let r=e[Symbol.asyncIterator]();Ce(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function sp(e,t){return Si(wi(e),t)}function ap(e,t){if(e!=null){if(Ei(e))return np(e,t);if(yi(e))return op(e,t);if(vi(e))return rp(e,t);if(Di(e))return Si(e,t);if(Ci(e))return ip(e,t);if(bi(e))return sp(e,t)}throw _i(e)}function st(e,t){return t?ap(e,t):z(e)}function we(...e){let t=it(e);return st(e,t)}function lc(e,t){let n=A(e)?e:()=>e,r=o=>o.error(n());return new x(t?o=>t.schedule(r,0,o):r)}function ZD(e){return!!e&&(e instanceof x||A(e.lift)&&A(e.subscribe))}var hn=Zn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function W(e,t){return N((n,r)=>{let o=0;n.subscribe(M(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:YD}=Array;function KD(e,t){return YD(t)?e(...t):e(t)}function Ni(e){return W(t=>KD(e,t))}var{isArray:QD}=Array,{getPrototypeOf:XD,prototype:JD,keys:e_}=Object;function Ai(e){if(e.length===1){let t=e[0];if(QD(t))return{args:t,keys:null};if(t_(t)){let n=e_(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function t_(e){return e&&typeof e=="object"&&XD(e)===JD}function Ri(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function dc(...e){let t=it(e),n=mi(e),{args:r,keys:o}=Ai(e);if(r.length===0)return st([],t);let i=new x(n_(r,t,o?s=>Ri(o,s):ye));return n?i.pipe(Ni(n)):i}function n_(e,t,n=ye){return r=>{cp(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)cp(t,()=>{let u=st(e[c],t),l=!1;u.subscribe(M(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function cp(e,t,n){e?Ce(n,e,t):t()}function up(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,p=()=>{d&&!c.length&&!u&&t.complete()},f=y=>u<r?g(y):c.push(y),g=y=>{i&&t.next(y),u++;let m=!1;z(n(y,l++)).subscribe(M(t,v=>{o?.(v),i?f(v):t.next(v)},()=>{m=!0},void 0,()=>{if(m)try{for(u--;c.length&&u<r;){let v=c.shift();s?Ce(t,s,()=>g(v)):g(v)}p()}catch(v){t.error(v)}}))};return e.subscribe(M(t,f,()=>{d=!0,p()})),()=>{a?.()}}function gn(e,t,n=1/0){return A(t)?gn((r,o)=>W((i,s)=>t(r,i,o,s))(z(e(r,o))),n):(typeof t=="number"&&(n=t),N((r,o)=>up(r,o,e,n)))}function zr(e=1/0){return gn(ye,e)}function lp(){return zr(1)}function Jn(...e){return lp()(st(e,it(e)))}function r_(e){return new x(t=>{z(e()).subscribe(t)})}function fc(...e){let t=mi(e),{args:n,keys:r}=Ai(e),o=new x(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;z(n[l]).subscribe(M(i,p=>{d||(d=!0,u--),a[l]=p},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Ri(r,a):a),i.complete())}))}});return t?o.pipe(Ni(t)):o}function o_(...e){let t=it(e),n=Kf(e,1/0),r=e;return r.length?r.length===1?z(r[0]):zr(n)(st(r,t)):fn}function Ne(e,t){return N((n,r)=>{let o=0;n.subscribe(M(r,i=>e.call(t,i,o++)&&r.next(i)))})}function dp(e){return N((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(M(n,u=>{r=!0,o=u,i||z(e(u)).subscribe(i=M(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function i_(e,t=cn){return dp(()=>$r(e,t))}function xi(e){return N((t,n)=>{let r=null,o=!1,i;r=t.subscribe(M(n,void 0,void 0,s=>{i=z(e(s,xi(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function fp(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(M(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function pc(e,t){return A(t)?gn(e,t,1):gn(e,1)}function mn(e,t=cn){return N((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(M(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Gr(e){return N((t,n)=>{let r=!1;t.subscribe(M(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function yn(e){return e<=0?()=>fn:N((t,n)=>{let r=0;t.subscribe(M(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function hc(e,t=ye){return e=e??s_,N((n,r)=>{let o,i=!0;n.subscribe(M(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function s_(e,t){return e===t}function Oi(e=a_){return N((t,n)=>{let r=!1;t.subscribe(M(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function a_(){return new hn}function Wr(e){return N((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function c_(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ne((o,i)=>e(o,i,r)):ye,yn(1),n?Gr(t):Oi(()=>new hn))}function gc(e){return e<=0?()=>fn:N((t,n)=>{let r=[];t.subscribe(M(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function u_(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ne((o,i)=>e(o,i,r)):ye,gc(1),n?Gr(t):Oi(()=>new hn))}function l_(){return N((e,t)=>{let n,r=!1;e.subscribe(M(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function d_(e,t){return N(fp(e,t,arguments.length>=2,!0))}function qr(e={}){let{connector:t=()=>new $,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,l=d=!1},g=()=>{let y=s;f(),y?.unsubscribe()};return N((y,m)=>{u++,!d&&!l&&p();let v=c=c??t();m.add(()=>{u--,u===0&&!d&&!l&&(a=mc(g,o))}),v.subscribe(m),!s&&u>0&&(s=new It({next:U=>v.next(U),error:U=>{d=!0,p(),a=mc(f,n,U),v.error(U)},complete:()=>{l=!0,p(),a=mc(f,r),v.complete()}}),z(y).subscribe(s))})(i)}}function mc(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new It({next:()=>{r.unsubscribe(),e()}});return z(t(...n)).subscribe(r)}function f_(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,qr({connector:()=>new gi(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Zr(e){return Ne((t,n)=>e<=n)}function yc(...e){let t=it(e);return N((n,r)=>{(t?Jn(e,n,t):Jn(e,n)).subscribe(r)})}function vc(e,t){return N((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(M(r,c=>{o?.unsubscribe();let u=0,l=i++;z(e(c,l)).subscribe(o=M(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Yr(e){return N((t,n)=>{z(e).subscribe(M(n,()=>n.complete(),Hr)),!n.closed&&t.subscribe(n)})}function p_(e,t=!1){return N((n,r)=>{let o=0;n.subscribe(M(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function vn(e,t,n){let r=A(e)||t||n?{next:e,error:t,complete:n}:e;return r?N((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(M(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ye}var Ec;function Fi(){return Ec}function at(e){let t=Ec;return Ec=e,t}var pp=Symbol("NotFound");function er(e){return e===pp||e?.name==="\u0275NotFound"}function Bi(e,t){return Object.is(e,t)}var ne=null,ki=!1,Dc=1,h_=null,re=Symbol("SIGNAL");function S(e){let t=ne;return ne=e,t}function Vi(){return ne}var Ut={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function En(e){if(ki)throw new Error("");if(ne===null)return;ne.consumerOnSignalRead(e);let t=ne.nextProducerIndex++;if($i(ne),t<ne.producerNode.length&&ne.producerNode[t]!==e&&Qr(ne)){let n=ne.producerNode[t];Hi(n,ne.producerIndexOfThis[t])}ne.producerNode[t]!==e&&(ne.producerNode[t]=e,ne.producerIndexOfThis[t]=Qr(ne)?gp(e,ne,t):0),ne.producerLastReadVersion[t]=e.version}function hp(){Dc++}function Ui(e){if(!(Qr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Dc)){if(!e.producerMustRecompute(e)&&!Dn(e)){ji(e);return}e.producerRecomputeValue(e),ji(e)}}function _c(e){if(e.liveConsumerNode===void 0)return;let t=ki;ki=!0;try{for(let n of e.liveConsumerNode)n.dirty||g_(n)}finally{ki=t}}function Ic(){return ne?.consumerAllowSignalWrites!==!1}function g_(e){e.dirty=!0,_c(e),e.consumerMarkedDirty?.(e)}function ji(e){e.dirty=!1,e.lastCleanEpoch=Dc}function Ct(e){return e&&(e.nextProducerIndex=0),S(e)}function Ht(e,t){if(S(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Qr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Hi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Dn(e){$i(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ui(n),r!==n.version))return!0}return!1}function tr(e){if($i(e),Qr(e))for(let t=0;t<e.producerNode.length;t++)Hi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function gp(e,t,n){if(mp(e),e.liveConsumerNode.length===0&&yp(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=gp(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Hi(e,t){if(mp(e),e.liveConsumerNode.length===1&&yp(e))for(let r=0;r<e.producerNode.length;r++)Hi(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];$i(o),o.producerIndexOfThis[r]=t}}function Qr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function $i(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function mp(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function yp(e){return e.producerNode!==void 0}function zi(e){h_?.(e)}function Xr(e,t){let n=Object.create(m_);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ui(n),En(n),n.value===Kr)throw n.error;return n.value};return r[re]=n,zi(n),r}var Pi=Symbol("UNSET"),Li=Symbol("COMPUTING"),Kr=Symbol("ERRORED"),m_=te(B({},Ut),{value:Pi,dirty:!0,error:null,equal:Bi,kind:"computed",producerMustRecompute(e){return e.value===Pi||e.value===Li},producerRecomputeValue(e){if(e.value===Li)throw new Error("");let t=e.value;e.value=Li;let n=Ct(e),r,o=!1;try{r=e.computation(),S(null),o=t!==Pi&&t!==Kr&&r!==Kr&&e.equal(t,r)}catch(i){r=Kr,e.error=i}finally{Ht(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function y_(){throw new Error}var vp=y_;function Ep(e){vp(e)}function Cc(e){vp=e}var v_=null;function wc(e,t){let n=Object.create(Jr);n.value=e,t!==void 0&&(n.equal=t);let r=()=>Dp(n);return r[re]=n,zi(n),[r,s=>nr(n,s),s=>bc(n,s)]}function Dp(e){return En(e),e.value}function nr(e,t){Ic()||Ep(e),e.equal(e.value,t)||(e.value=t,E_(e))}function bc(e,t){Ic()||Ep(e),nr(e,t(e.value))}var Jr=te(B({},Ut),{equal:Bi,value:void 0,kind:"signal"});function E_(e){e.version++,hp(),_c(e),v_?.(e)}function _p(e){let t=S(null);try{return e()}finally{S(t)}}var Yi="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(Tt(t,n)),this.code=t}};function D_(e){return`NG0${Math.abs(e)}`}function Tt(e,t){return`${D_(e)}${t?": "+t:""}`}var je=globalThis;function j(e){for(let t in e)if(e[t]===j)return t;throw Error("")}function wp(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function bt(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(bt).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Ki(e,t){return e?t?`${e} ${t}`:e:t||""}var __=j({__forward_ref__:j});function Qi(e){return e.__forward_ref__=Qi,e.toString=function(){return bt(this())},e}function ce(e){return Pc(e)?e():e}function Pc(e){return typeof e=="function"&&e.hasOwnProperty(__)&&e.__forward_ref__===Qi}function bp(e,t){e==null&&Lc(t,e,null,"!=")}function Lc(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function de(e){return{providers:e.providers||[],imports:e.imports||[]}}function io(e){return C_(e,Xi)}function I_(e){return io(e)!==null}function C_(e,t){return e.hasOwnProperty(t)&&e[t]||null}function w_(e){let t=e?.[Xi]??null;return t||null}function Mc(e){return e&&e.hasOwnProperty(Wi)?e[Wi]:null}var Xi=j({\u0275prov:j}),Wi=j({\u0275inj:j}),E=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=D({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function jc(e){return e&&!!e.\u0275providers}var Bc=j({\u0275cmp:j}),Vc=j({\u0275dir:j}),Uc=j({\u0275pipe:j}),Hc=j({\u0275mod:j}),no=j({\u0275fac:j}),bn=j({__NG_ELEMENT_ID__:j}),Ip=j({__NG_ENV_ID__:j});function Gt(e){return typeof e=="string"?e:e==null?"":String(e)}function qi(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Gt(e)}var $c=j({ngErrorCode:j}),Tp=j({ngErrorMessage:j}),to=j({ngTokenPath:j});function zc(e,t){return Mp("",-200,t)}function Ji(e,t){throw new _(-201,!1)}function b_(e,t){e[to]??=[];let n=e[to],r;typeof t=="object"&&"multi"in t&&t?.multi===!0?(bp(t.provide,"Token with multi: true should have a provide property"),r=qi(t.provide)):r=qi(t),n[0]!==r&&e[to].unshift(r)}function T_(e,t){let n=e[to],r=e[$c],o=e[Tp]||e.message;return e.message=S_(o,r,n,t),e}function Mp(e,t,n){let r=new _(t,e);return r[$c]=t,r[Tp]=e,n&&(r[to]=n),r}function M_(e){return e[$c]}function S_(e,t,n=[],r=null){let o="";n&&n.length>1&&(o=` Path: ${n.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return Tt(t,`${e}${i}${o}`)}var Sc;function Sp(){return Sc}function ve(e){let t=Sc;return Sc=e,t}function Gc(e,t,n){let r=io(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;Ji(e,"Injector")}var N_={},_n=N_,A_="__NG_DI_FLAG__",Nc=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=In(n)||0;try{return this.injector.get(t,r&8?null:_n,r)}catch(o){if(er(o))return o;throw o}}};function R_(e,t=0){let n=Fi();if(n===void 0)throw new _(-203,!1);if(n===null)return Gc(e,void 0,t);{let r=x_(t),o=n.retrieve(e,r);if(er(o)){if(r.optional)return null;throw o}return o}}function C(e,t=0){return(Sp()||R_)(ce(e),t)}function h(e,t){return C(e,In(t))}function In(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function x_(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Ac(e){let t=[];for(let n=0;n<e.length;n++){let r=ce(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=O_(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function O_(e){return e[A_]}function $t(e,t){let n=e.hasOwnProperty(no);return n?e[no]:null}function Np(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Ap(e){return e.flat(Number.POSITIVE_INFINITY)}function es(e,t){e.forEach(n=>Array.isArray(n)?es(n,t):t(n))}function Wc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function so(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Rp(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function xp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ts(e,t,n){let r=or(e,t);return r>=0?e[r|1]=n:(r=~r,xp(e,r,t,n)),r}function ns(e,t){let n=or(e,t);if(n>=0)return e[n|1]}function or(e,t){return F_(e,t,1)}function F_(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Wt={},pe=[],Ze=new E(""),qc=new E("",-1),Zc=new E(""),ro=class{get(t,n=_n){if(n===_n){let o=Mp("",-201);throw o.name="\u0275NotFound",o}return n}};function Yc(e){return e[Hc]||null}function ct(e){return e[Bc]||null}function rs(e){return e[Vc]||null}function Kc(e){return e[Uc]||null}function ut(e){return{\u0275providers:e}}function Op(e){return ut([{provide:Ze,multi:!0,useValue:e}])}function Fp(...e){return{\u0275providers:os(!0,e),\u0275fromNgModule:!0}}function os(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return es(t,s=>{let a=s;Zi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&kp(o,i),n}function kp(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Qc(o,i=>{t(i,r)})}}function Zi(e,t,n,r){if(e=ce(e),!e)return!1;let o=null,i=Mc(e),s=!i&&ct(e);if(!i&&!s){let c=e.ngModule;if(i=Mc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Zi(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{es(i.imports,l=>{Zi(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&kp(u,t)}if(!a){let u=$t(o)||(()=>new o);t({provide:o,useFactory:u,deps:pe},o),t({provide:Zc,useValue:o,multi:!0},o),t({provide:Ze,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Qc(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Qc(e,t){for(let n of e)jc(n)&&(n=n.\u0275providers),Array.isArray(n)?Qc(n,t):t(n)}var k_=j({provide:String,useValue:j});function Pp(e){return e!==null&&typeof e=="object"&&k_ in e}function P_(e){return!!(e&&e.useExisting)}function L_(e){return!!(e&&e.useFactory)}function Cn(e){return typeof e=="function"}function Lp(e){return!!e.useClass}var ao=new E(""),Gi={},Cp={},Tc;function ir(){return Tc===void 0&&(Tc=new ro),Tc}var ue=class{},wn=class extends ue{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,xc(t,s=>this.processProvider(s)),this.records.set(qc,rr(void 0,this)),o.has("environment")&&this.records.set(ue,rr(void 0,this));let i=this.records.get(ao);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Zc,pe,{self:!0}))}retrieve(t,n){let r=In(n)||0;try{return this.get(t,_n,r)}catch(o){if(er(o))return o;throw o}}destroy(){eo(this),this._destroyed=!0;let t=S(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),S(t)}}onDestroy(t){return eo(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){eo(this);let n=at(this),r=ve(void 0),o;try{return t()}finally{at(n),ve(r)}}get(t,n=_n,r){if(eo(this),t.hasOwnProperty(Ip))return t[Ip](this);let o=In(r),i,s=at(this),a=ve(void 0);try{if(!(o&4)){let u=this.records.get(t);if(u===void 0){let l=H_(t)&&io(t);l&&this.injectableDefInScope(l)?u=rr(Rc(t),Gi):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u,o)}let c=o&2?ir():this.parent;return n=o&8&&n===_n?null:n,c.get(t,n)}catch(c){let u=M_(c);throw u===-200||u===-201?new _(u,null):c}finally{ve(a),at(s)}}resolveInjectorInitializers(){let t=S(null),n=at(this),r=ve(void 0),o;try{let i=this.get(Ze,pe,{self:!0});for(let s of i)s()}finally{at(n),ve(r),S(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(bt(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ce(t);let n=Cn(t)?t:ce(t&&t.provide),r=B_(t);if(!Cn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=rr(void 0,Gi,!0),o.factory=()=>Ac(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=S(null);try{if(n.value===Cp)throw zc(bt(t));return n.value===Gi&&(n.value=Cp,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&U_(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{S(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ce(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Rc(e){let t=io(e),n=t!==null?t.factory:$t(e);if(n!==null)return n;if(e instanceof E)throw new _(204,!1);if(e instanceof Function)return j_(e);throw new _(204,!1)}function j_(e){if(e.length>0)throw new _(204,!1);let n=w_(e);return n!==null?()=>n.factory(e):()=>new e}function B_(e){if(Pp(e))return rr(void 0,e.useValue);{let t=Xc(e);return rr(t,Gi)}}function Xc(e,t,n){let r;if(Cn(e)){let o=ce(e);return $t(o)||Rc(o)}else if(Pp(e))r=()=>ce(e.useValue);else if(L_(e))r=()=>e.useFactory(...Ac(e.deps||[]));else if(P_(e))r=(o,i)=>C(ce(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=ce(e&&(e.useClass||e.provide));if(V_(e))r=()=>new o(...Ac(e.deps));else return $t(o)||Rc(o)}return r}function eo(e){if(e.destroyed)throw new _(205,!1)}function rr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function V_(e){return!!e.deps}function U_(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function H_(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function xc(e,t){for(let n of e)Array.isArray(n)?xc(n,t):n&&jc(n)?xc(n.\u0275providers,t):t(n)}function sr(e,t){let n;e instanceof wn?(eo(e),n=e):n=new Nc(e);let r,o=at(n),i=ve(void 0);try{return t()}finally{at(o),ve(i)}}function jp(){return Sp()!==void 0||Fi()!=null}var Ee=0,I=1,T=2,Y=3,Be=4,De=5,_e=6,ar=7,K=8,Ye=9,lt=10,F=11,cr=12,Jc=13,Tn=14,fe=15,qt=16,Mn=17,dt=18,co=19,eu=20,wt=21,is=22,Mt=23,Ae=24,Sn=25,P=26,Bp=1,Ke=6,ft=7,uo=8,Nn=9,ie=10;function Re(e){return Array.isArray(e)&&typeof e[Bp]=="object"}function xe(e){return Array.isArray(e)&&e[Bp]===!0}function tu(e){return(e.flags&4)!==0}function St(e){return e.componentOffset>-1}function lo(e){return(e.flags&1)===1}function pt(e){return!!e.template}function An(e){return(e[T]&512)!==0}function Zt(e){return(e[T]&256)===256}var nu="svg",Vp="math";function Oe(e){for(;Array.isArray(e);)e=e[Ee];return e}function ru(e,t){return Oe(t[e])}function Ve(e,t){return Oe(t[e.index])}function Rn(e,t){return e.data[t]}function ou(e,t){return e[t]}function iu(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function Ue(e,t){let n=t[e];return Re(n)?n:n[Ee]}function Up(e){return(e[T]&4)===4}function ss(e){return(e[T]&128)===128}function Hp(e){return xe(e[Y])}function Fe(e,t){return t==null?null:e[t]}function su(e){e[Mn]=0}function au(e){e[T]&1024||(e[T]|=1024,ss(e)&&Yt(e))}function $p(e,t){for(;e>0;)t=t[Tn],e--;return t}function fo(e){return!!(e[T]&9216||e[Ae]?.dirty)}function as(e){e[lt].changeDetectionScheduler?.notify(8),e[T]&64&&(e[T]|=1024),fo(e)&&Yt(e)}function Yt(e){e[lt].changeDetectionScheduler?.notify(0);let t=zt(e);for(;t!==null&&!(t[T]&8192||(t[T]|=8192,!ss(t)));)t=zt(t)}function cu(e,t){if(Zt(e))throw new _(911,!1);e[wt]===null&&(e[wt]=[]),e[wt].push(t)}function zp(e,t){if(e[wt]===null)return;let n=e[wt].indexOf(t);n!==-1&&e[wt].splice(n,1)}function zt(e){let t=e[Y];return xe(t)?t[Y]:t}function uu(e){return e[ar]??=[]}function lu(e){return e.cleanup??=[]}function Gp(e,t,n,r){let o=uu(t);o.push(n),e.firstCreatePass&&lu(e).push(r,o.length-1)}var R={lFrame:ah(null),bindingsEnabled:!0,skipHydrationRootTNode:null},po=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(po||{}),$_=0,Oc=!1;function Wp(){return R.lFrame.elementDepthCount}function qp(){R.lFrame.elementDepthCount++}function du(){R.lFrame.elementDepthCount--}function fu(){return R.bindingsEnabled}function cs(){return R.skipHydrationRootTNode!==null}function pu(e){return R.skipHydrationRootTNode===e}function Zp(e){R.skipHydrationRootTNode=e}function hu(){R.skipHydrationRootTNode=null}function b(){return R.lFrame.lView}function q(){return R.lFrame.tView}function Yp(e){return R.lFrame.contextLView=e,e[K]}function Kp(e){return R.lFrame.contextLView=null,e}function se(){let e=gu();for(;e!==null&&e.type===64;)e=e.parent;return e}function gu(){return R.lFrame.currentTNode}function Qp(){let e=R.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ur(e,t){let n=R.lFrame;n.currentTNode=e,n.isParent=t}function mu(){return R.lFrame.isParent}function yu(){R.lFrame.isParent=!1}function Xp(){return R.lFrame.contextLView}function vu(e){Lc("Must never be called in production mode"),$_=e}function Eu(){return Oc}function lr(e){let t=Oc;return Oc=e,t}function Jp(){let e=R.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function eh(){return R.lFrame.bindingIndex}function th(e){return R.lFrame.bindingIndex=e}function Kt(){return R.lFrame.bindingIndex++}function us(e){let t=R.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function nh(){return R.lFrame.inI18n}function rh(e,t){let n=R.lFrame;n.bindingIndex=n.bindingRootIndex=e,ls(t)}function oh(){return R.lFrame.currentDirectiveIndex}function ls(e){R.lFrame.currentDirectiveIndex=e}function ih(e){let t=R.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function ds(){return R.lFrame.currentQueryIndex}function ho(e){R.lFrame.currentQueryIndex=e}function z_(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[De]:null}function Du(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=z_(i),o===null||(i=i[Tn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=R.lFrame=sh();return r.currentTNode=t,r.lView=e,!0}function fs(e){let t=sh(),n=e[I];R.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function sh(){let e=R.lFrame,t=e===null?null:e.child;return t===null?ah(e):t}function ah(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function ch(){let e=R.lFrame;return R.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var _u=ch;function ps(){let e=ch();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function uh(e){return(R.lFrame.contextLView=$p(e,R.lFrame.contextLView))[K]}function ht(){return R.lFrame.selectedIndex}function Qt(e){R.lFrame.selectedIndex=e}function go(){let e=R.lFrame;return Rn(e.tView,e.selectedIndex)}function lh(){R.lFrame.currentNamespace=nu}function dh(){G_()}function G_(){R.lFrame.currentNamespace=null}function Iu(){return R.lFrame.currentNamespace}var fh=!0;function hs(){return fh}function Nt(e){fh=e}function Fc(e,t=null,n=null,r){let o=Cu(e,t,n,r);return o.resolveInjectorInitializers(),o}function Cu(e,t=null,n=null,r,o=new Set){let i=[n||pe,Fp(e)];return r=r||(typeof e=="object"?void 0:bt(e)),new wn(i,t||ir(),r||null,o)}var oe=class e{static THROW_IF_NOT_FOUND=_n;static NULL=new ro;static create(t,n){if(Array.isArray(t))return Fc({name:""},n,t,"");{let r=t.name??"";return Fc({name:r},t.parent,t.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>C(qc)});static __NG_ELEMENT_ID__=-1},O=new E(""),Qe=(()=>{class e{static __NG_ELEMENT_ID__=W_;static __NG_ENV_ID__=n=>n}return e})(),oo=class extends Qe{_lView;constructor(t){super(),this._lView=t}get destroyed(){return Zt(this._lView)}onDestroy(t){let n=this._lView;return cu(n,t),()=>zp(n,t)}};function W_(){return new oo(b())}var le=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Xe=new E("",{providedIn:"root",factory:()=>{let e=h(ue),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(le),t.handleError(n))}}}),ph={provide:Ze,useValue:()=>void h(le),multi:!0},q_=new E("",{providedIn:"root",factory:()=>{let e=h(O).defaultView;if(!e)return;let t=h(Xe),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),h(Qe).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function Z_(){return ut([Op(()=>void h(q_))])}function dr(e){return typeof e=="function"&&e[re]!==void 0}function gt(e,t){let[n,r,o]=wc(e,t?.equal),i=n,s=i[re];return i.set=r,i.update=o,i.asReadonly=wu.bind(i),i}function wu(){let e=this[re];if(e.readonlyFn===void 0){let t=()=>this();t[re]=e,e.readonlyFn=t}return e.readonlyFn}function bu(e){return dr(e)&&typeof e.set=="function"}var Le=class{},fr=new E("",{providedIn:"root",factory:()=>!1});var Tu=new E(""),Mu=new E("");var xn=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Y_}return e})();function Y_(){return new xn(b(),se())}var At=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new dn(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new x(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),pr=(()=>{class e{internalPendingTasks=h(At);scheduler=h(Le);errorHandler=h(Xe);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();function On(...e){}var mo=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new kc})}return e})(),kc=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};var gs={JSACTION:"jsaction"};function Mo(e){return{toString:e}.toString()}function nI(e){return typeof e=="function"}var Ms=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function rg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var qs=(()=>{let e=()=>og;return e.ngInherit=!0,e})();function og(e){return e.type.prototype.ngOnChanges&&(e.setInput=oI),rI}function rI(){let e=sg(this),t=e?.current;if(t){let n=e.previous;if(n===Wt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function oI(e,t,n,r,o){let i=this.declaredInputs[r],s=sg(e)||iI(e,{previous:Wt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Ms(u&&u.currentValue,n,c===Wt),rg(e,t,o,n)}var ig="__ngSimpleChanges__";function sg(e){return e[ig]||null}function iI(e,t){return e[ig]=t}var hh=[];var L=function(e,t=null,n){for(let r=0;r<hh.length;r++){let o=hh[r];o(e,t,n)}};function sI(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=og(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function ag(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Ds(e,t,n){cg(e,t,3,n)}function _s(e,t,n,r){(e[T]&3)===n&&cg(e,t,n,r)}function Su(e,t){let n=e[T];(n&3)===t&&(n&=16383,n+=1,e[T]=n)}function cg(e,t,n,r){let o=r!==void 0?e[Mn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Mn]+=65536),(a<i||i==-1)&&(aI(e,n,t,c),e[Mn]=(e[Mn]&**********)+c+2),c++}function gh(e,t){L(4,e,t);let n=S(null);try{t.call(e)}finally{S(n),L(5,e,t)}}function aI(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[T]>>14<e[Mn]>>16&&(e[T]&3)===t&&(e[T]+=16384,gh(a,i)):gh(a,i)}var gr=-1,Ln=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r,o){this.factory=t,this.name=o,this.canSeeViewProviders=n,this.injectImpl=r}};function cI(e){return(e.flags&8)!==0}function uI(e){return(e.flags&16)!==0}function lI(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];dI(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function ug(e){return e===3||e===4||e===6}function dI(e){return e.charCodeAt(0)===64}function vr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?mh(e,n,o,null,t[++r]):mh(e,n,o,null,null))}}return e}function mh(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function lg(e){return e!==gr}function Ss(e){return e&32767}function fI(e){return e>>16}function Ns(e,t){let n=fI(e),r=t;for(;n>0;)r=r[Tn],n--;return r}var Hu=!0;function As(e){let t=Hu;return Hu=e,t}var pI=256,dg=pI-1,fg=5,hI=0,mt={};function gI(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(bn)&&(r=n[bn]),r==null&&(r=n[bn]=hI++);let o=r&dg,i=1<<o;t.data[e+(o>>fg)]|=i}function Rs(e,t){let n=pg(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,Nu(r.data,e),Nu(t,null),Nu(r.blueprint,null));let o=Ml(e,t),i=e.injectorIndex;if(lg(o)){let s=Ss(o),a=Ns(o,t),c=a[I].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Nu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function pg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ml(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=vg(o),r===null)return gr;if(n++,o=o[Tn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return gr}function $u(e,t,n){gI(e,t,n)}function mI(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(ug(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function hg(e,t,n){if(n&8||e!==void 0)return e;Ji(t,"NodeInjector")}function gg(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[Ye],i=ve(void 0);try{return o?o.get(t,r,n&8):Gc(t,r,n&8)}finally{ve(i)}}return hg(r,t,n)}function mg(e,t,n,r=0,o){if(e!==null){if(t[T]&2048&&!(r&2)){let s=_I(e,t,n,r,mt);if(s!==mt)return s}let i=yg(e,t,n,r,mt);if(i!==mt)return i}return gg(t,n,r,o)}function yg(e,t,n,r,o){let i=vI(n);if(typeof i=="function"){if(!Du(t,e,r))return r&1?hg(o,n,r):gg(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Ji(n);else return s}finally{_u()}}else if(typeof i=="number"){let s=null,a=pg(e,t),c=gr,u=r&1?t[fe][De]:null;for((a===-1||r&4)&&(c=a===-1?Ml(e,t):t[a+8],c===gr||!vh(r,!1)?a=-1:(s=t[I],a=Ss(c),t=Ns(c,t)));a!==-1;){let l=t[I];if(yh(i,a,l.data)){let d=yI(a,t,n,s,r,u);if(d!==mt)return d}c=t[a+8],c!==gr&&vh(r,t[I].data[a+8]===u)&&yh(i,a,t)?(s=l,a=Ss(c),t=Ns(c,t)):a=-1}}return o}function yI(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],c=r==null?St(a)&&Hu:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Is(a,s,n,c,u);return l!==null?Eo(t,s,l,a,o):mt}function Is(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:u;for(let f=d;f<p;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&pt(f)&&f.type===n)return c}return null}function Eo(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof Ln){let a=i;if(a.resolving){let f=qi(s[n]);throw zc(f)}let c=As(a.canSeeViewProviders);a.resolving=!0;let u=s[n].type||s[n],l,d=a.injectImpl?ve(a.injectImpl):null,p=Du(e,r,0);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&sI(n,s[n],t)}finally{d!==null&&ve(d),As(c),a.resolving=!1,_u()}}return i}function vI(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(bn)?e[bn]:void 0;return typeof t=="number"?t>=0?t&dg:EI:t}function yh(e,t,n){let r=1<<e;return!!(n[t+(e>>fg)]&r)}function vh(e,t){return!(e&2)&&!(e&1&&t)}var Pn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return mg(this._tNode,this._lView,t,In(r),n)}};function EI(){return new Pn(se(),b())}function DI(e){return Mo(()=>{let t=e.prototype.constructor,n=t[no]||zu(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[no]||zu(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function zu(e){return Pc(e)?()=>{let t=zu(ce(e));return t&&t()}:$t(e)}function _I(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[T]&2048&&!An(s);){let a=yg(i,s,n,r|2,mt);if(a!==mt)return a;let c=i.parent;if(!c){let u=s[eu];if(u){let l=u.get(n,mt,r);if(l!==mt)return l}c=vg(s),s=s[Tn]}i=c}return o}function vg(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[De]:null}function Sl(e){return mI(se(),e)}function II(){return Cr(se(),b())}function Cr(e,t){return new he(Ve(e,t))}var he=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=II}return e})();function Eg(e){return e instanceof he?e.nativeElement:e}function CI(){return this._results[Symbol.iterator]()}var jn=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new $}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Ap(t);(this._changesDetected=!Np(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=CI},Dg="ngSkipHydration",wI="ngskiphydration";function _g(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===wI)return!0}return!1}function Ig(e){return e.hasAttribute(Dg)}function xs(e){return(e.flags&128)===128}function Cg(e){if(xs(e))return!0;let t=e.parent;for(;t;){if(xs(e)||_g(t))return!0;t=t.parent}return!1}var Nl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Nl||{}),wg=new Map,bI=0;function TI(){return bI++}function MI(e){wg.set(e[co],e)}function Gu(e){wg.delete(e[co])}var Eh="__ngContext__";function Er(e,t){Re(t)?(e[Eh]=t[co],MI(t)):e[Eh]=t}function bg(e){return Mg(e[cr])}function Tg(e){return Mg(e[Be])}function Mg(e){for(;e!==null&&!xe(e);)e=e[Be];return e}var Wu;function Al(e){Wu=e}function wr(){if(Wu!==void 0)return Wu;if(typeof document<"u")return document;throw new _(210,!1)}var be=new E("",{providedIn:"root",factory:()=>SI}),SI="ng",Zs=new E(""),Jt=new E("",{providedIn:"platform",factory:()=>"unknown"});var NI=new E(""),br=new E("",{providedIn:"root",factory:()=>wr().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function AI(){let e=new $n;return e.store=RI(wr(),h(be)),e}var $n=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:AI});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();function RI(e,t){let n=e.getElementById(t+"-state");if(n?.textContent)try{return JSON.parse(n.textContent)}catch(r){console.warn("Exception while restoring TransferState for app "+t,r)}return{}}var Sg="h",Ng="b",xI="f",OI="n",Ag="e",Rg="t",Ys="c",Rl="x",Do="r",xg="i",Og="n",xl="d";var Fg="di",kg="s",Pg="p";var So=new E(""),Lg=!1,Ol=new E("",{providedIn:"root",factory:()=>Lg});var Fl=new E(""),jg=!1,Bg=new E(""),kl=new E("",{providedIn:"root",factory:()=>new Map});var No="ngb";var Vg=(e,t,n)=>{let r=e,o=r.__jsaction_fns??new Map,i=o.get(t)??[];i.push(n),o.set(t,i),r.__jsaction_fns=o},Ug=(e,t)=>{let n=e,r=n.getAttribute(No)??"",o=t.get(r)??new Set;o.has(n)||o.add(n),t.set(r,o)};var Hg=e=>{e.removeAttribute(gs.JSACTION),e.removeAttribute(No),e.__jsaction_fns=void 0},$g=new E("",{providedIn:"root",factory:()=>({})});function Pl(e,t){let n=t?.__jsaction_fns?.get(e.type);if(!(!n||!t?.isConnected))for(let r of n)r(e)}var qu=new Map;function zg(e,t){return qu.set(e,t),()=>qu.delete(e)}var Dh=!1,Gg=(e,t,n,r)=>{};function FI(e,t,n,r){Gg(e,t,n,r)}function Wg(){Dh||(Gg=(e,t,n,r)=>{let o=e[Ye].get(be);qu.get(o)?.(t,n,r)},Dh=!0)}var Ks=new E("");function Ao(e){return(e.flags&32)===32}var kI="__nghData__",Ll=kI,PI="__nghDeferData__",qg=PI,Cs="ngh",Zg="nghm",Yg=()=>null;function LI(e,t,n=!1){let r=e.getAttribute(Cs);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,c={};if(r!==""){let l=t.get($n,null,{optional:!0});l!==null&&(c=l.get(Ll,[])[Number(r)])}let u={data:c,firstChild:e.firstChild??null};return n&&(u.firstChild=e,Qs(u,0,e.nextSibling)),a?e.setAttribute(Cs,a):e.removeAttribute(Cs),u}function Kg(){Yg=LI}function Qg(e,t,n=!1){return Yg(e,t,n)}function Xg(e){let t=e._lView;return t[I].type===2?null:(An(t)&&(t=t[P]),t)}function jI(e){return e.textContent?.replace(/\s/gm,"")}function BI(e){let t=wr(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=jI(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function Qs(e,t,n){e.segmentHeads??={},e.segmentHeads[t]=n}function Zu(e,t){return e.segmentHeads?.[t]??null}function Jg(e){return e.get(Bg,!1,{optional:!0})}function VI(e,t){let n=e.data,r=n[Ag]?.[t]??null;return r===null&&n[Ys]?.[t]&&(r=jl(e,t)),r}function em(e,t){return e.data[Ys]?.[t]??null}function jl(e,t){let n=em(e,t)??[],r=0;for(let o of n)r+=o[Do]*(o[Rl]??1);return r}function UI(e){if(typeof e.disconnectedNodes>"u"){let t=e.data[xl];e.disconnectedNodes=t?new Set(t):null}return e.disconnectedNodes}function tm(e,t){if(typeof e.disconnectedNodes>"u"){let n=e.data[xl];e.disconnectedNodes=n?new Set(n):null}return!!UI(e)?.has(t)}function Xs(e,t){let n=e[_e];return n!==null&&!cs()&&!Ao(t)&&!tm(n,t.index-P)}function HI(e,t){let n=t.get(Ks),o=t.get($n).get(qg,{}),i=!1,s=e,a=null,c=[];for(;!i&&s;){i=n.has(s);let u=n.hydrating.get(s);if(a===null&&u!=null){a=u.promise;break}c.unshift(s),s=o[s][Pg]}return{parentBlockPromise:a,hydrationQueue:c}}function Au(e){return!!e&&e.nodeType===Node.COMMENT_NODE&&e.textContent?.trim()===Zg}function _h(e){for(;e&&e.nodeType===Node.TEXT_NODE;)e=e.previousSibling;return e}function nm(e){for(let r of e.body.childNodes)if(Au(r))return;let t=_h(e.body.previousSibling);if(Au(t))return;let n=_h(e.head.lastChild);if(!Au(n))throw new _(-507,!1)}function rm(e,t){let n=e.contentQueries;if(n!==null){let r=S(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];ho(i),a.contentQueries(2,t[s],s)}}}finally{S(r)}}}function Yu(e,t,n){ho(0);let r=S(null);try{t(e,n)}finally{S(r)}}function om(e,t,n){if(tu(t)){let r=S(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{S(r)}}}var Rt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Rt||{}),ms;function $I(){if(ms===void 0&&(ms=null,je.trustedTypes))try{ms=je.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ms}function Js(e){return $I()?.createHTML(e)||e}var ys;function im(){if(ys===void 0&&(ys=null,je.trustedTypes))try{ys=je.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ys}function Ih(e){return im()?.createHTML(e)||e}function Ch(e){return im()?.createScriptURL(e)||e}var xt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Yi})`}},Ku=class extends xt{getTypeName(){return"HTML"}},Qu=class extends xt{getTypeName(){return"Style"}},Xu=class extends xt{getTypeName(){return"Script"}},Ju=class extends xt{getTypeName(){return"URL"}},el=class extends xt{getTypeName(){return"ResourceURL"}};function He(e){return e instanceof xt?e.changingThisBreaksApplicationSecurity:e}function vt(e,t){let n=sm(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Yi})`)}return n===t}function sm(e){return e instanceof xt&&e.getTypeName()||null}function Bl(e){return new Ku(e)}function Vl(e){return new Qu(e)}function Ul(e){return new Xu(e)}function Hl(e){return new Ju(e)}function $l(e){return new el(e)}function zI(e){let t=new nl(e);return GI()?new tl(t):t}var tl=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Js(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},nl=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Js(t),n}};function GI(){try{return!!new window.DOMParser().parseFromString(Js(""),"text/html")}catch{return!1}}var WI=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ro(e){return e=String(e),e.match(WI)?e:"unsafe:"+e}function Ft(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function xo(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var am=Ft("area,br,col,hr,img,wbr"),cm=Ft("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),um=Ft("rp,rt"),qI=xo(um,cm),ZI=xo(cm,Ft("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),YI=xo(um,Ft("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),wh=xo(am,ZI,YI,qI),lm=Ft("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),KI=Ft("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),QI=Ft("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),XI=xo(lm,KI,QI),JI=Ft("script,style,template"),rl=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=nC(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=tC(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=bh(t).toLowerCase();if(!wh.hasOwnProperty(n))return this.sanitizedSomething=!0,!JI.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!XI.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;lm[a]&&(c=Ro(c)),this.buf.push(" ",s,'="',Th(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=bh(t).toLowerCase();wh.hasOwnProperty(n)&&!am.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Th(t))}};function eC(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function tC(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw dm(t);return t}function nC(e){let t=e.firstChild;if(t&&eC(e,t))throw dm(t);return t}function bh(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function dm(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var rC=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,oC=/([^\#-~ |!])/g;function Th(e){return e.replace(/&/g,"&amp;").replace(rC,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(oC,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var vs;function ea(e,t){let n=null;try{vs=vs||zI(e);let r=t?String(t):"";n=vs.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=vs.getInertBodyElement(r)}while(r!==i);let a=new rl().sanitizeChildren(Mh(n)||n);return Js(a)}finally{if(n){let r=Mh(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Mh(e){return"content"in e&&iC(e)?e.content:null}function iC(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ae=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ae||{});function sC(e){let t=zl();return t?Ih(t.sanitize(ae.HTML,e)||""):vt(e,"HTML")?Ih(He(e)):ea(wr(),Gt(e))}function fm(e){let t=zl();return t?t.sanitize(ae.URL,e)||"":vt(e,"URL")?He(e):Ro(Gt(e))}function pm(e){let t=zl();if(t)return Ch(t.sanitize(ae.RESOURCE_URL,e)||"");if(vt(e,"ResourceURL"))return Ch(He(e));throw new _(904,!1)}function aC(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?pm:fm}function cC(e,t,n){return aC(t,n)(e)}function zl(){let e=b();return e&&e[lt].sanitizer}var uC=/^>|^->|<!--|-->|--!>|<!-$/g,lC=/(<|>)/g,dC="\u200B$1\u200B";function fC(e){return e.replace(uC,t=>t.replace(lC,dC))}function hm(e){return e.ownerDocument.body}function gm(e){return e instanceof Function?e():e}function pC(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var mm="ng-template";function hC(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&pC(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Gl(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Gl(e){return e.type===4&&e.value!==mm}function gC(e,t,n){let r=e.type===4&&!n?mm:e.value;return t===r}function mC(e,t,n){let r=4,o=e.attrs,i=o!==null?EC(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Je(r)&&!Je(c))return!1;if(s&&Je(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!gC(e,c,n)||c===""&&t.length===1){if(Je(r))return!1;s=!0}}else if(r&8){if(o===null||!hC(e,o,c,n)){if(Je(r))return!1;s=!0}}else{let u=t[++a],l=yC(c,o,Gl(e),n);if(l===-1){if(Je(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Je(r))return!1;s=!0}}}}return Je(r)||s}function Je(e){return(e&1)===0}function yC(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return DC(t,e)}function ym(e,t,n=!1){for(let r=0;r<t.length;r++)if(mC(e,t[r],n))return!0;return!1}function vC(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function EC(e){for(let t=0;t<e.length;t++){let n=e[t];if(ug(n))return t}return e.length}function DC(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function _C(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Sh(e,t){return e?":not("+t.trim()+")":t}function IC(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Je(s)&&(t+=Sh(i,o),o=""),r=s,i=i||!Je(r);n++}return o!==""&&(t+=Sh(i,o)),t}function CC(e){return e.map(IC).join(",")}function wC(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Je(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Te={};function vm(e,t){return e.createText(t)}function bC(e,t,n){e.setValue(t,n)}function Em(e,t){return e.createComment(fC(t))}function Wl(e,t,n){return e.createElement(t,n)}function Os(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Dm(e,t,n){e.appendChild(t,n)}function Nh(e,t,n,r,o){r!==null?Os(e,t,n,r,o):Dm(e,t,n)}function ql(e,t,n){e.removeChild(null,t,n)}function _m(e){e.textContent=""}function TC(e,t,n){e.setAttribute(t,"style",n)}function MC(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Im(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&lI(e,t,r),o!==null&&MC(e,t,o),i!==null&&TC(e,t,i)}function Zl(e,t,n,r,o,i,s,a,c,u,l){let d=P+r,p=d+o,f=SC(d,p),g=typeof u=="function"?u():u;return f[I]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function SC(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Te);return n}function NC(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Zl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Yl(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[Ee]=o,d[T]=r|4|128|8|64|1024,(u!==null||e&&e[T]&2048)&&(d[T]|=2048),su(d),d[Y]=d[Tn]=e,d[K]=n,d[lt]=s||e&&e[lt],d[F]=a||e&&e[F],d[Ye]=c||e&&e[Ye]||null,d[De]=i,d[co]=TI(),d[_e]=l,d[eu]=u,d[fe]=t.type==2?e[fe]:d,d}function AC(e,t,n){let r=Ve(t,e),o=NC(n),i=e[lt].rendererFactory,s=Kl(e,Yl(e,o,null,Cm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Cm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function wm(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Kl(e,t){return e[cr]?e[Jc][Be]=t:e[cr]=t,e[Jc]=t,t}function RC(e=1){bm(q(),b(),ht()+e,!1)}function bm(e,t,n,r){if(!r)if((t[T]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ds(t,i,n)}else{let i=e.preOrderHooks;i!==null&&_s(t,i,0,n)}Qt(n)}var ta=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ta||{});function ol(e,t,n,r){let o=S(null);try{let[i,s,a]=e.inputs[n],c=null;(s&ta.SignalBased)!==0&&(c=t[i][re]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):rg(t,c,i,r)}finally{S(o)}}var yt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(yt||{}),xC;function Ql(e,t){return xC(e,t)}function hr(e,t,n,r,o){if(r!=null){let i,s=!1;xe(r)?i=r:Re(r)&&(s=!0,r=r[Ee]);let a=Oe(r);e===0&&n!==null?o==null?Dm(t,n,a):Os(t,n,a,o||null,!0):e===1&&n!==null?Os(t,n,a,o||null,!0):e===2?ql(t,a,s):e===3&&t.destroyNode(a),i!=null&&HC(t,e,i,n,o)}}function OC(e,t){Tm(e,t),t[Ee]=null,t[De]=null}function FC(e,t,n,r,o,i){r[Ee]=o,r[De]=t,ra(e,r,n,1,o,i)}function Tm(e,t){t[lt].changeDetectionScheduler?.notify(9),ra(e,t,t[F],2,null,null)}function kC(e){let t=e[cr];if(!t)return Ru(e[I],e);for(;t;){let n=null;if(Re(t))n=t[cr];else{let r=t[ie];r&&(n=r)}if(!n){for(;t&&!t[Be]&&t!==e;)Re(t)&&Ru(t[I],t),t=t[Y];t===null&&(t=e),Re(t)&&Ru(t[I],t),n=t&&t[Be]}t=n}}function Xl(e,t){let n=e[Nn],r=n.indexOf(t);n.splice(r,1)}function na(e,t){if(Zt(t))return;let n=t[F];n.destroyNode&&ra(e,t,n,3,null,null),kC(t)}function Ru(e,t){if(Zt(t))return;let n=S(null);try{t[T]&=-129,t[T]|=256,t[Ae]&&tr(t[Ae]),LC(e,t),PC(e,t),t[I].type===1&&t[F].destroy();let r=t[qt];if(r!==null&&xe(t[Y])){r!==t[Y]&&Xl(r,t);let o=t[dt];o!==null&&o.detachView(e)}Gu(t)}finally{S(n)}}function PC(e,t){let n=e.cleanup,r=t[ar];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[ar]=null);let o=t[wt];if(o!==null){t[wt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Mt];if(i!==null){t[Mt]=null;for(let s of i)s.destroy()}}function LC(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Ln)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];L(4,a,c);try{c.call(a)}finally{L(5,a,c)}}else{L(4,o,i);try{i.call(o)}finally{L(5,o,i)}}}}}function Mm(e,t,n){return jC(e,t.parent,n)}function jC(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Ee];if(St(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Rt.None||o===Rt.Emulated)return null}return Ve(r,n)}function Sm(e,t,n){return VC(e,t,n)}function BC(e,t,n){return e.type&40?Ve(e,n):null}var VC=BC,Ah;function Jl(e,t,n,r){let o=Mm(e,r,t),i=t[F],s=r.parent||t[De],a=Sm(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Nh(i,o,n[c],a,!1);else Nh(i,o,n,a,!1);Ah!==void 0&&Ah(i,r,t,n,o)}function yo(e,t){if(t!==null){let n=t.type;if(n&3)return Ve(t,e);if(n&4)return il(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return yo(e,r);{let o=e[t.index];return xe(o)?il(-1,o):Oe(o)}}else{if(n&128)return yo(e,t.next);if(n&32)return Ql(t,e)()||Oe(e[t.index]);{let r=Nm(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=zt(e[fe]);return yo(o,r)}else return yo(e,t.next)}}}return null}function Nm(e,t){if(t!==null){let r=e[fe][De],o=t.projection;return r.projection[o]}return null}function il(e,t){let n=ie+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return yo(r,o)}return t[ft]}function ed(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Er(Oe(a),r),n.flags|=2),!Ao(n))if(c&8)ed(e,t,n.child,r,o,i,!1),hr(t,e,o,a,i);else if(c&32){let u=Ql(n,r),l;for(;l=u();)hr(t,e,o,l,i);hr(t,e,o,a,i)}else c&16?Am(e,t,r,n,o,i):hr(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ra(e,t,n,r,o,i){ed(n,r,e.firstChild,t,o,i,!1)}function UC(e,t,n){let r=t[F],o=Mm(e,n,t),i=n.parent||t[De],s=Sm(i,n,t);Am(r,0,t,n,o,s)}function Am(e,t,n,r,o,i){let s=n[fe],c=s[De].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];hr(t,e,o,l,i)}else{let u=c,l=s[Y];xs(r)&&(u.flags|=128),ed(e,t,u,l,o,i,!0)}}function HC(e,t,n,r,o){let i=n[ft],s=Oe(n);i!==s&&hr(t,e,r,i,o);for(let a=ie;a<n.length;a++){let c=n[a];ra(c[I],c,e,t,r,i)}}function $C(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:yt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=yt.Important),e.setStyle(n,r,o,i))}}function Rm(e,t,n,r,o){let i=ht(),s=r&2;try{Qt(-1),s&&t.length>P&&bm(e,t,P,!1),L(s?2:0,o,n),n(r,o)}finally{Qt(i),L(s?3:1,o,n)}}function td(e,t,n){YC(e,t,n),(n.flags&64)===64&&KC(e,t,n)}function oa(e,t,n=Ve){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function zC(e,t,n,r){let i=r.get(Ol,Lg)||n===Rt.ShadowDom,s=e.selectRootElement(t,i);return GC(s),s}function GC(e){xm(e)}var xm=()=>null;function WC(e){Ig(e)?_m(e):BI(e)}function Om(){xm=WC}function qC(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Fm(e,t,n,r,o,i){let s=t[I];if(rd(e,s,t,n,r)){St(e)&&ZC(t,e.index);return}e.type&3&&(n=qC(n)),km(e,t,n,r,o,i)}function km(e,t,n,r,o,i){if(e.type&3){let s=Ve(e,t);r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function ZC(e,t){let n=Ue(t,e);n[T]&16||(n[T]|=64)}function YC(e,t,n){let r=n.directiveStart,o=n.directiveEnd;St(n)&&AC(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Rs(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Eo(t,e,s,n);if(Er(c,t),i!==null&&ew(t,s-r,c,a,n,i),pt(a)){let u=Ue(n.index,t);u[K]=Eo(t,e,s,n)}}}function KC(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=oh();try{Qt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];ls(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&QC(c,u)}}finally{Qt(-1),ls(s)}}function QC(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Pm(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];ym(t,i.selectors,!1)&&(r??=[],pt(i)?r.unshift(i):r.push(i))}return r}function XC(e,t,n,r,o,i){let s=Ve(e,t);JC(t[F],s,i,e.value,n,r,o)}function JC(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Gt(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function ew(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];ol(r,n,c,u)}}function Lm(e,t,n,r,o){let i=P+n,s=t[I],a=o(s,t,e,r,n);t[i]=a,ur(e,!0);let c=e.type===2;return c?(Im(t[F],a,e),(Wp()===0||lo(e))&&Er(a,t),qp()):Er(a,t),hs()&&(!c||!Ao(e))&&Jl(s,t,a,e),e}function jm(e){let t=e;return mu()?yu():(t=t.parent,ur(t,!1)),t}function nd(e,t){let n=e[Ye];if(!n)return;n.get(Xe,null)?.(t)}function rd(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];ol(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];ol(l,u,r,o),a=!0}return a}function tw(e,t){let n=Ue(t,e),r=n[I];nw(r,n);let o=n[Ee];o!==null&&n[_e]===null&&(n[_e]=Qg(o,n[Ye])),L(18),od(r,n,n[K]),L(19,n[K])}function nw(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function od(e,t,n){fs(t);try{let r=e.viewQuery;r!==null&&Yu(1,r,n);let o=e.template;o!==null&&Rm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[dt]?.finishViewCreation(e),e.staticContentQueries&&rm(e,t),e.staticViewQueries&&Yu(2,e.viewQuery,n);let i=e.components;i!==null&&rw(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[T]&=-5,ps()}}function rw(e,t){for(let n=0;n<t.length;n++)tw(e,t[n])}function Tr(e,t,n,r){let o=S(null);try{let i=t.tView,a=e[T]&4096?4096:16,c=Yl(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[qt]=u;let l=e[dt];return l!==null&&(c[dt]=l.createEmbeddedView(i)),od(i,c,n),c}finally{S(o)}}function Bn(e,t){return!t||t.firstChild===null||xs(e)}var Rh=!1,ow=new E("");function _o(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Oe(i)),xe(i)&&Bm(i,r);let s=n.type;if(s&8)_o(e,t,n.child,r);else if(s&32){let a=Ql(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Nm(t,n);if(Array.isArray(a))r.push(...a);else{let c=zt(t[fe]);_o(c[I],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Bm(e,t){for(let n=ie;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&_o(r[I],r,o,t)}e[ft]!==e[Ee]&&t.push(e[ft])}function Vm(e){if(e[Sn]!==null){for(let t of e[Sn])t.impl.addSequence(t);e[Sn].length=0}}var Um=[];function iw(e){return e[Ae]??sw(e)}function sw(e){let t=Um.pop()??Object.create(cw);return t.lView=e,t}function aw(e){e.lView[Ae]!==e&&(e.lView=null,Um.push(e))}var cw=te(B({},Ut),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Yt(e.lView)},consumerOnSignalRead(){this.lView[Ae]=this}});function uw(e){let t=e[Ae]??Object.create(lw);return t.lView=e,t}var lw=te(B({},Ut),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=zt(e.lView);for(;t&&!Hm(t[I]);)t=zt(t);t&&au(t)},consumerOnSignalRead(){this.lView[Ae]=this}});function Hm(e){return e.type!==2}function $m(e){if(e[Mt]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Mt])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[T]&8192)}}var dw=100;function id(e,t=0){let r=e[lt].rendererFactory,o=!1;o||r.begin?.();try{fw(e,t)}finally{o||r.end?.()}}function fw(e,t){let n=Eu();try{lr(!0),sl(e,t);let r=0;for(;fo(e);){if(r===dw)throw new _(103,!1);r++,sl(e,1)}}finally{lr(n)}}function zm(e,t){vu(t?po.Exhaustive:po.OnlyDirtyViews);try{id(e)}finally{vu(po.Off)}}function pw(e,t,n,r){if(Zt(t))return;let o=t[T],i=!1,s=!1;fs(t);let a=!0,c=null,u=null;i||(Hm(e)?(u=iw(t),c=Ct(u)):Vi()===null?(a=!1,u=uw(t),c=Ct(u)):t[Ae]&&(tr(t[Ae]),t[Ae]=null));try{su(t),th(e.bindingStartIndex),n!==null&&Rm(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Ds(t,f,null)}else{let f=e.preOrderHooks;f!==null&&_s(t,f,0,null),Su(t,0)}if(s||hw(t),$m(t),Gm(t,0),e.contentQueries!==null&&rm(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Ds(t,f)}else{let f=e.contentHooks;f!==null&&_s(t,f,1),Su(t,1)}mw(e,t);let d=e.components;d!==null&&qm(t,d,0);let p=e.viewQuery;if(p!==null&&Yu(2,p,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Ds(t,f)}else{let f=e.viewHooks;f!==null&&_s(t,f,2),Su(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[is]){for(let f of t[is])f();t[is]=null}i||(Vm(t),t[T]&=-73)}catch(l){throw i||Yt(t),l}finally{u!==null&&(Ht(u,c),a&&aw(u)),ps()}}function Gm(e,t){for(let n=bg(e);n!==null;n=Tg(n))for(let r=ie;r<n.length;r++){let o=n[r];Wm(o,t)}}function hw(e){for(let t=bg(e);t!==null;t=Tg(t)){if(!(t[T]&2))continue;let n=t[Nn];for(let r=0;r<n.length;r++){let o=n[r];au(o)}}}function gw(e,t,n){L(18);let r=Ue(t,e);Wm(r,n),L(19,r[K])}function Wm(e,t){ss(e)&&sl(e,t)}function sl(e,t){let r=e[I],o=e[T],i=e[Ae],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Dn(i)),s||=!1,i&&(i.dirty=!1),e[T]&=-9217,s)pw(r,e,r.template,e[K]);else if(o&8192){let a=S(null);try{$m(e),Gm(e,1);let c=r.components;c!==null&&qm(e,c,1),Vm(e)}finally{S(a)}}}function qm(e,t,n){for(let r=0;r<t.length;r++)gw(e,t[r],n)}function mw(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Qt(~o);else{let i=o,s=n[++r],a=n[++r];rh(s,i);let c=t[i];L(24,c),a(2,c),L(25,c)}}}finally{Qt(-1)}}function ia(e,t){let n=Eu()?64:1088;for(e[lt].changeDetectionScheduler?.notify(t);e;){e[T]|=n;let r=zt(e);if(An(e)&&!r)return e;e=r}return null}function Zm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Ym(e,t){let n=ie+t;if(n<e.length)return e[n]}function Mr(e,t,n,r=!0){let o=t[I];if(yw(o,t,e,n),r){let s=il(n,e),a=t[F],c=a.parentNode(e[ft]);c!==null&&FC(o,e[De],a,t,c,s)}let i=t[_e];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function sd(e,t){let n=Io(e,t);return n!==void 0&&na(n[I],n),n}function Io(e,t){if(e.length<=ie)return;let n=ie+t,r=e[n];if(r){let o=r[qt];o!==null&&o!==e&&Xl(o,r),t>0&&(e[n-1][Be]=r[Be]);let i=so(e,ie+t);OC(r[I],r);let s=i[dt];s!==null&&s.detachView(i[I]),r[Y]=null,r[Be]=null,r[T]&=-129}return r}function yw(e,t,n,r){let o=ie+r,i=n.length;r>0&&(n[o-1][Be]=t),r<i-ie?(t[Be]=n[o],Wc(n,ie+r,t)):(n.push(t),t[Be]=null),t[Y]=n;let s=t[qt];s!==null&&n!==s&&Km(s,t);let a=t[dt];a!==null&&a.insertView(e),as(t),t[T]|=128}function Km(e,t){let n=e[Nn],r=t[Y];if(Re(r))e[T]|=2;else{let o=r[Y][fe];t[fe]!==o&&(e[T]|=2)}n===null?e[Nn]=[t]:n.push(t)}var Xt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[I];return _o(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[K]}set context(t){this._lView[K]=t}get destroyed(){return Zt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Y];if(xe(t)){let n=t[uo],r=n?n.indexOf(this):-1;r>-1&&(Io(t,r),so(n,r))}this._attachedToViewContainer=!1}na(this._lView[I],this._lView)}onDestroy(t){cu(this._lView,t)}markForCheck(){ia(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[T]&=-129}reattach(){as(this._lView),this._lView[T]|=128}detectChanges(){this._lView[T]|=1024,id(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Ye].get(ow,Rh)}catch{this.exhaustive=Rh}}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=An(this._lView),n=this._lView[qt];n!==null&&!t&&Xl(n,this._lView),Tm(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=An(this._lView),r=this._lView[qt];r!==null&&!n&&Km(r,this._lView),as(this._lView)}};var Dr=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=vw;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Tr(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new Xt(i)}}return e})();function vw(){return sa(se(),b())}function sa(e,t){return e.type&4?new Dr(t,e,Cr(e,t)):null}function Sr(e,t,n,r,o){let i=e.data[t];if(i===null)i=Ew(e,t,n,r,o),nh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Qp();i.injectorIndex=s===null?-1:s.injectorIndex}return ur(i,!0),i}function Ew(e,t,n,r,o){let i=gu(),s=mu(),a=s?i:i&&i.parent,c=e.data[t]=_w(e,a,n,t,r,o);return Dw(e,c,i,s),c}function Dw(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function _w(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return cs()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var Iw=new RegExp(`^(\\d+)*(${Ng}|${Sg})*(.*)`);function Cw(e){let t=e.match(Iw),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[c,u,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(u,d)}return[s,...a]}function ww(e){return!e.prev&&e.parent?.type===8}function xu(e){return e.index-P}function bw(e,t){let n=e.i18nNodes;if(n)return n.get(t)}function aa(e,t,n,r){let o=xu(r),i=bw(e,o);if(i===void 0){let s=e.data[Og];if(s?.[o])i=Mw(s[o],n);else if(t.firstChild===r)i=e.firstChild;else{let a=r.prev===null,c=r.prev??r.parent;if(ww(r)){let u=xu(r.parent);i=Zu(e,u)}else{let u=Ve(c,n);if(a)i=u.firstChild;else{let l=xu(c),d=Zu(e,l);if(c.type===2&&d){let f=jl(e,l)+1;i=ca(f,d)}else i=u.nextSibling}}}}return i}function ca(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function Tw(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case xI:n=n.firstChild;break;case OI:n=n.nextSibling;break}}return n}function Mw(e,t){let[n,...r]=Cw(e),o;if(n===Sg)o=t[fe][Ee];else if(n===Ng)o=hm(t[fe][Ee]);else{let i=Number(n);o=Oe(t[i+P])}return Tw(o,r)}var Sw=!1;function Qm(e){Sw=e}function Nw(e){let t=e[_e];if(t){let{i18nNodes:n,dehydratedIcuData:r}=t;if(n&&r){let o=e[F];for(let i of r.values())Aw(o,n,i)}t.i18nNodes=void 0,t.dehydratedIcuData=void 0}}function Aw(e,t,n){for(let r of n.node.cases[n.case]){let o=t.get(r.index-P);o&&ql(e,o,!1)}}function ua(e){let t=e[Ke]??[],r=e[Y][F],o=[];for(let i of t)i.data[Fg]!==void 0?o.push(i):Xm(i,r);e[Ke]=o}function Rw(e){let{lContainer:t}=e,n=t[Ke];if(n===null)return;let o=t[Y][F];for(let i of n)Xm(i,o)}function Xm(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[Do];for(;n<o;){let i=r.nextSibling;ql(t,r,!1),r=i,n++}}}function la(e){ua(e);let t=e[Ee];Re(t)&&Fs(t);for(let n=ie;n<e.length;n++)Fs(e[n])}function Fs(e){Nw(e);let t=e[I];for(let n=P;n<t.bindingStartIndex;n++)if(xe(e[n])){let r=e[n];la(r)}else Re(e[n])&&Fs(e[n])}function ad(e){let t=e._views;for(let n of t){let r=Xg(n);r!==null&&r[Ee]!==null&&(Re(r)?Fs(r):la(r))}}function xw(e,t,n,r){e!==null&&(n.cleanup(t),la(e.lContainer),ad(r))}function Ow(e,t){let n=[];for(let r of t)for(let o=0;o<(r[Rl]??1);o++){let i={data:r,firstChild:null};r[Do]>0&&(i.firstChild=e,e=ca(r[Do],e)),n.push(i)}return[e,n]}var Jm=()=>null,ey=()=>null;function ty(){Jm=Fw,ey=kw}function Fw(e,t){return ry(e,t)?e[Ke].shift():(ua(e),null)}function Co(e,t){return Jm(e,t)}function kw(e,t,n){if(t.tView.ssrId===null)return null;let r=Co(e,t.tView.ssrId);return n[I].firstUpdatePass&&r===null&&Pw(n,t),r}function ny(e,t,n){return ey(e,t,n)}function Pw(e,t){let n=t;for(;n;){if(xh(e,n))return;if((n.flags&256)===256)break;n=n.prev}for(n=t.next;n&&(n.flags&512)===512;){if(xh(e,n))return;n=n.next}}function ry(e,t){let n=e[Ke];return!t||n===null||n.length===0?!1:n[0].data[xg]===t}function xh(e,t){let n=t.tView?.ssrId;if(n==null)return!1;let r=e[t.index];return xe(r)&&ry(r,n)?(ua(r),!0):!1}var oy=class{},da=class{},al=class{resolveComponentFactory(t){throw new _(917,!1)}},Oo=class{static NULL=new al},Ot=class{},cd=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Lw()}return e})();function Lw(){let e=b(),t=se(),n=Ue(t.index,e);return(Re(n)?n:e)[F]}var iy=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var ws={},mr=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,ws,r);return o!==ws||n===ws?o:this.parentInjector.get(t,n,r)}};function ks(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ki(o,a);else if(i==2){let c=a,u=t[++s];r=Ki(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function $e(e,t=0){let n=b();if(n===null)return C(e,t);let r=se();return mg(r,n,ce(e),t)}function jw(){let e="invalid";throw new Error(e)}function sy(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}Uw(e,t,n,a,i,c,u)}i!==null&&r!==null&&Bw(n,r,i)}function Bw(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function Vw(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Uw(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&pt(f)&&(c=!0,Vw(e,n,p)),$u(Rs(n,t),e,f.type)}qw(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=wm(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=vr(n.mergedAttrs,f.hostAttrs),$w(e,n,t,d,f),Ww(d,f,o),s!==null&&s.has(f)){let[y,m]=s.get(f);n.directiveToIndex.set(f.type,[d,y+n.directiveStart,m+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}Hw(e,n,i)}function Hw(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Oh(0,t,o,r),Oh(1,t,o,r),kh(t,r,!1);else{let i=n.get(o);Fh(0,t,i,r),Fh(1,t,i,r),kh(t,r,!0)}}}function Oh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),ay(t,i)}}function Fh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),ay(t,s)}}function ay(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function kh(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Gl(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function $w(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=$t(o.type,!0)),s=new Ln(i,pt(o),$e,null);e.blueprint[r]=s,n[r]=s,zw(e,t,r,wm(e,n,o.hostVars,Te),o)}function zw(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Gw(s)!=a&&s.push(a),s.push(n,r,i)}}function Gw(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Ww(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;pt(t)&&(n[""]=e)}}function qw(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function cy(e,t,n,r,o,i,s,a){let c=t[I],u=c.consts,l=Fe(u,s),d=Sr(c,e,n,r,l);return i&&sy(c,t,d,Fe(u,a),o),d.mergedAttrs=vr(d.mergedAttrs,d.attrs),d.attrs!==null&&ks(d,d.attrs,!1),d.mergedAttrs!==null&&ks(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function uy(e,t){ag(e,t),tu(t)&&e.queries.elementEnd(t)}function Zw(e,t,n,r,o,i){let s=t.consts,a=Fe(s,o),c=Sr(t,e,n,r,a);if(c.mergedAttrs=vr(c.mergedAttrs,c.attrs),i!=null){let u=Fe(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&ks(c,c.attrs,!1),c.mergedAttrs!==null&&ks(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function Yw(e,t,n){return e[t]=n}function tt(e,t,n){if(n===Te)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function ly(e,t,n,r){let o=tt(e,t,n);return tt(e,t+1,r)||o}function bs(e,t,n){return function r(o){let i=St(e)?Ue(e.index,t):t;ia(i,5);let s=t[K],a=Ph(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Ph(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Ph(e,t,n,r){let o=S(null);try{return L(6,t,n),n(r)!==!1}catch(i){return nd(e,i),!1}finally{L(7,t,n),S(o)}}function dy(e,t,n,r,o,i,s,a){let c=lo(e),u=!1,l=null;if(!r&&c&&(l=Kw(t,n,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Ve(e,n),p=r?r(d):d;FI(n,p,i,a);let f=o.listen(p,i,a),g=r?y=>r(Oe(y[e.index])):e.index;fy(g,t,n,i,a,f,!1)}return u}function Kw(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[ar],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function fy(e,t,n,r,o,i,s){let a=t.firstCreatePass?lu(t):null,c=uu(n),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Lh(e,t,n,r,o,i){let s=t[n],a=t[I],u=a.data[n].outputs[r],d=s[u].subscribe(i);fy(e.index,a,t,o,i,d,!0)}var cl=Symbol("BINDING");var Ps=class extends Oo{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=ct(t);return new Vn(n,this.ngModule)}};function Qw(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&ta.SignalBased)!==0};return o&&(i.transform=o),i})}function Xw(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Jw(e,t,n){let r=t instanceof ue?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new mr(n,r):n}function eb(e){let t=e.get(Ot,null);if(t===null)throw new _(407,!1);let n=e.get(iy,null),r=e.get(Le,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function tb(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Wl(t,n,n==="svg"?nu:n==="math"?Vp:null)}var Vn=class extends da{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Qw(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Xw(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=CC(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){L(22);let a=S(null);try{let c=this.componentDef,u=nb(r,c,s,i),l=Jw(c,o||this.ngModule,t),d=eb(l),p=d.rendererFactory.createRenderer(null,c),f=r?zC(p,r,c.encapsulation,l):tb(c,p),g=s?.some(jh)||i?.some(v=>typeof v!="function"&&v.bindings.some(jh)),y=Yl(null,u,null,512|Cm(c),null,null,d,p,l,null,Qg(f,l,!0));y[P]=f,fs(y);let m=null;try{let v=cy(P,y,2,"#host",()=>u.directiveRegistry,!0,0);f&&(Im(p,f,v),Er(f,y)),td(u,y,v),om(u,v,y),uy(u,v),n!==void 0&&ob(v,this.ngContentSelectors,n),m=Ue(v.index,y),y[K]=m[K],od(u,y,null)}catch(v){throw m!==null&&Gu(m),Gu(y),v}finally{L(23),ps()}return new Ls(this.componentType,y,!!g)}finally{S(a)}}};function nb(e,t,n,r){let o=e?["ng-version","20.1.2"]:wC(t.selectors[0]),i=null,s=null,a=0;if(n)for(let l of n)a+=l[cl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let p of d.bindings){a+=p[cl].requiredVars;let f=l+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,p=rs(d);c.push(p)}return Zl(0,null,rb(i,s),1,a,c,null,null,null,[o],null)}function rb(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function jh(e){let t=e[cl].kind;return t==="input"||t==="twoWay"}var Ls=class extends oy{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=Rn(n[I],P),this.location=Cr(this._tNode,n),this.instance=Ue(this._tNode.index,n)[K],this.hostView=this.changeDetectorRef=new Xt(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=rd(r,o[I],o,t,n);this.previousInputValues.set(t,n);let s=Ue(r.index,o);ia(s,1)}get injector(){return new Pn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function ob(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Nr=(()=>{class e{static __NG_ELEMENT_ID__=ib}return e})();function ib(){let e=se();return hy(e,b())}var sb=Nr,py=class extends sb{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Cr(this._hostTNode,this._hostLView)}get injector(){return new Pn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ml(this._hostTNode,this._hostLView);if(lg(t)){let n=Ns(t,this._hostLView),r=Ss(t),o=n[I].data[r+8];return new Pn(o,n)}else return new Pn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Bh(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ie}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Co(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Bn(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!nI(t),u;if(c)u=n;else{let m=n||{};u=m.index,r=m.injector,o=m.projectableNodes,i=m.environmentInjector||m.ngModuleRef,s=m.directives,a=m.bindings}let l=c?t:new Vn(ct(t)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let v=(c?d:this.parentInjector).get(ue,null);v&&(i=v)}let p=ct(l.componentType??{}),f=Co(this._lContainer,p?.id??null),g=f?.firstChild??null,y=l.create(d,o,g,i,s,a);return this.insertImpl(y.hostView,u,Bn(this._hostTNode,f)),y}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Hp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[Y],u=new py(c,c[De],c[Y]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Mr(s,o,i,r),t.attachToViewContainerRef(),Wc(Ou(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Bh(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Io(this._lContainer,n);r&&(so(Ou(this._lContainer),n),na(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=Io(this._lContainer,n);return r&&so(Ou(this._lContainer),n)!=null?new Xt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Bh(e){return e[uo]}function Ou(e){return e[uo]||(e[uo]=[])}function hy(e,t){let n,r=t[e.index];return xe(r)?n=r:(n=Zm(r,t,null,e),t[e.index]=n,Kl(t,n)),gy(n,t,e,r),new py(n,e,t)}function ab(e,t){let n=e[F],r=n.createComment(""),o=Ve(t,e),i=n.parentNode(o);return Os(n,i,r,n.nextSibling(o),!1),r}var gy=my,ud=()=>!1;function cb(e,t,n){return ud(e,t,n)}function my(e,t,n,r){if(e[ft])return;let o;n.type&8?o=Oe(r):o=ab(t,n),e[ft]=o}function ub(e,t,n){if(e[ft]&&e[Ke])return!0;let r=n[_e],o=t.index-P;if(!r||Cg(t)||tm(r,o))return!1;let s=Zu(r,o),a=r.data[Ys]?.[o],[c,u]=Ow(s,a);return e[ft]=c,e[Ke]=u,!0}function lb(e,t,n,r){ud(e,n,t)||my(e,t,n,r)}function yy(){gy=lb,ud=ub}var ul=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ll=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)dd(t,n).matches!==null&&this.queries[n].setDirty()}},js=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=gb(t):this.predicate=t}},dl=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},fl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,db(n,i)),this.matchTNodeWithReadOption(t,n,Is(n,t,i,!1,!1))}else r===Dr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Is(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===he||o===Nr||o===Dr&&n.type&4)this.addMatch(n.index,-2);else{let i=Is(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function db(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function fb(e,t){return e.type&11?Cr(e,t):e.type&4?sa(e,t):null}function pb(e,t,n,r){return n===-1?fb(t,e):n===-2?hb(e,t,r):Eo(e,e[I],n,t)}function hb(e,t,n){if(n===he)return Cr(t,e);if(n===Dr)return sa(t,e);if(n===Nr)return hy(t,e)}function vy(e,t,n,r){let o=t[dt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(pb(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function pl(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=vy(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=ie;d<l.length;d++){let p=l[d];p[qt]===p[Y]&&pl(p[I],p,u,r)}if(l[Nn]!==null){let d=l[Nn];for(let p=0;p<d.length;p++){let f=d[p];pl(f[I],f,u,r)}}}}}return r}function ld(e,t){return e[dt].queries[t].queryList}function Ey(e,t,n){let r=new jn((n&4)===4);return Gp(e,t,r,r.destroy),(t[dt]??=new ll).queries.push(new ul(r))-1}function Dy(e,t,n){let r=q();return r.firstCreatePass&&(Iy(r,new js(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Ey(r,b(),t)}function _y(e,t,n,r){let o=q();if(o.firstCreatePass){let i=se();Iy(o,new js(t,n,r),i.index),mb(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Ey(o,b(),n)}function gb(e){return e.split(",").map(t=>t.trim())}function Iy(e,t,n){e.queries===null&&(e.queries=new dl),e.queries.track(new fl(t,n))}function mb(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function dd(e,t){return e.queries.getByIndex(t)}function Cy(e,t){let n=e[I],r=dd(n,t);return r.crossesNgTemplate?pl(n,e,t,[]):vy(n,e,r,t)}function wy(e,t,n){let r,o=Xr(()=>{r._dirtyCounter();let i=yb(r,e);if(t&&i===void 0)throw new _(-951,!1);return i});return r=o[re],r._dirtyCounter=gt(0),r._flatValue=void 0,o}function fd(e){return wy(!0,!1,e)}function pd(e){return wy(!0,!0,e)}function by(e,t){let n=e[re];n._lView=b(),n._queryIndex=t,n._queryList=ld(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function yb(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[T]&4)return t?void 0:pe;let o=ld(n,r),i=Cy(n,r);return o.reset(i,Eg),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}var Vh=new Set;function ze(e){Vh.has(e)||(Vh.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Un=class{},Ty=class{};var Bs=class extends Un{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ps(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Yc(t);this._bootstrapComponents=gm(i.bootstrap),this._r3Injector=Cu(t,n,[{provide:Un,useValue:this},{provide:Oo,useValue:this.componentFactoryResolver},...r],bt(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Vs=class extends Ty{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Bs(this.moduleType,t,[])}};var wo=class extends Un{injector;componentFactoryResolver=new Ps(this);instance=null;constructor(t){super();let n=new wn([...t.providers,{provide:Un,useValue:this},{provide:Oo,useValue:this.componentFactoryResolver}],t.parent||ir(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function hd(e,t,n=null){return new wo({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var vb=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=os(!1,n.type),o=r.length>0?hd([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(C(ue))})}return e})();function Fo(e){return Mo(()=>{let t=My(e),n=te(B({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Nl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(vb).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Rt.Emulated,styles:e.styles||pe,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&ze("NgStandalone"),Sy(n);let r=e.dependencies;return n.directiveDefs=Uh(r,Eb),n.pipeDefs=Uh(r,Kc),n.id=Ib(n),n})}function Eb(e){return ct(e)||rs(e)}function ge(e){return Mo(()=>({type:e.type,bootstrap:e.bootstrap||pe,declarations:e.declarations||pe,imports:e.imports||pe,exports:e.exports||pe,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Db(e,t){if(e==null)return Wt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ta.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function _b(e){if(e==null)return Wt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function Et(e){return Mo(()=>{let t=My(e);return Sy(t),t})}function gd(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function My(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Wt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||pe,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Db(e.inputs,t),outputs:_b(e.outputs),debugInfo:null}}function Sy(e){e.features?.forEach(t=>t(e))}function Uh(e,t){return e?()=>{let n=typeof e=="function"?e():e,r=[];for(let o of n){let i=t(o);i!==null&&r.push(i)}return r}:null}function Ib(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Cb(e){return Object.getPrototypeOf(e.prototype).constructor}function Ny(e){let t=Cb(e.type),n=!0,r=[e];for(;t;){let o;if(pt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Fu(e.inputs),s.declaredInputs=Fu(e.declaredInputs),s.outputs=Fu(e.outputs);let a=o.hostBindings;a&&Sb(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Tb(e,c),u&&Mb(e,u),wb(e,o),wp(e.outputs,o.outputs),pt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ny&&(n=!1)}}t=Object.getPrototypeOf(t)}bb(r)}function wb(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function bb(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=vr(o.hostAttrs,n=vr(n,o.hostAttrs))}}function Fu(e){return e===Wt?{}:e===pe?[]:e}function Tb(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Mb(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Sb(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Ay(e,t,n,r,o,i,s,a){if(n.firstCreatePass){e.mergedAttrs=vr(e.mergedAttrs,e.attrs);let l=e.tView=Zl(2,e,o,i,s,n.directiveRegistry,n.pipeRegistry,null,n.schemas,n.consts,null);n.queries!==null&&(n.queries.template(n,e),l.queries=n.queries.embeddedTView(e))}a&&(e.flags|=a),ur(e,!1);let c=Oy(n,t,e,r);hs()&&Jl(n,t,c,e),Er(c,t);let u=Zm(c,t,c,e);t[r+P]=u,Kl(t,u),cb(u,e,t)}function Nb(e,t,n,r,o,i,s,a,c,u,l){let d=n+P,p;return t.firstCreatePass?(p=Sr(t,d,4,s||null,a||null),fu()&&sy(t,e,p,Fe(t.consts,u),Pm),ag(t,p)):p=t.data[d],Ay(p,e,t,n,r,o,i,c),lo(p)&&td(t,e,p),u!=null&&oa(e,p,l),p}function _r(e,t,n,r,o,i,s,a,c,u,l){let d=n+P,p;if(t.firstCreatePass){if(p=Sr(t,d,4,s||null,a||null),u!=null){let f=Fe(t.consts,u);p.localNames=[];for(let g=0;g<f.length;g+=2)p.localNames.push(f[g],-1)}}else p=t.data[d];return Ay(p,e,t,n,r,o,i,c),u!=null&&oa(e,p,l),p}function Ry(e,t,n,r,o,i,s,a){let c=b(),u=q(),l=Fe(u.consts,i);return Nb(c,u,e,t,n,r,o,l,void 0,s,a),Ry}function xy(e,t,n,r,o,i,s,a){let c=b(),u=q(),l=Fe(u.consts,i);return _r(c,u,e,t,n,r,o,l,void 0,s,a),xy}var Oy=Fy;function Fy(e,t,n,r){return Nt(!0),t[F].createComment("")}function Ab(e,t,n,r){let o=!Xs(t,n);Nt(o);let i=t[_e]?.data[Rg]?.[r]??null;if(i!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=i),o)return Fy(e,t);let s=t[_e],a=aa(s,e,t,n);Qs(s,r,a);let c=jl(s,r);return ca(c,a)}function ky(){Oy=Ab}var ke=function(e){return e[e.NOT_STARTED=0]="NOT_STARTED",e[e.IN_PROGRESS=1]="IN_PROGRESS",e[e.COMPLETE=2]="COMPLETE",e[e.FAILED=3]="FAILED",e}(ke||{}),Hh=0,Rb=1,X=function(e){return e[e.Placeholder=0]="Placeholder",e[e.Loading=1]="Loading",e[e.Complete=2]="Complete",e[e.Error=3]="Error",e}(X||{});var xb=0,ko=1;var Ob=4,Fb=5;var kb=7,yr=8,Pb=9,md=function(e){return e[e.Manual=0]="Manual",e[e.Playthrough=1]="Playthrough",e}(md||{});function Ts(e,t){let n=jb(e),r=t[n];if(r!==null){for(let o of r)o();t[n]=null}}function Lb(e){Ts(1,e),Ts(0,e),Ts(2,e)}function jb(e){let t=Ob;return e===1?t=Fb:e===2&&(t=Pb),t}var fa=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(fa||{}),en=new E(""),Py=!1,hl=class extends ${__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,jp()&&(this.destroyRef=h(Qe,{optional:!0})??void 0,this.pendingTasks=h(At,{optional:!0})??void 0)}emit(t){let n=S(null);try{super.next(t)}finally{S(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Z&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Ie=hl;function Ly(e){let t,n;function r(){e=On;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function $h(e){return queueMicrotask(()=>e()),()=>{e=On}}var yd="isAngularZone",Us=yd+"_ID",Bb=0,k=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Ie(!1);onMicrotaskEmpty=new Ie(!1);onStable=new Ie(!1);onError=new Ie(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Py}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Hb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(yd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Vb,On,On);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Vb={};function vd(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ub(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Ly(()=>{e.callbackScheduled=!1,gl(e),e.isCheckStableRunning=!0,vd(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),gl(e)}function Hb(e){let t=()=>{Ub(e)},n=Bb++;e._inner=e._inner.fork({name:"angular",properties:{[yd]:!0,[Us]:n,[Us+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if($b(c))return r.invokeTask(i,s,a,c);try{return zh(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Gh(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return zh(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!zb(c)&&t(),Gh(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,gl(e),vd(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function gl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function zh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Gh(e){e._nesting--,vd(e)}var Hs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Ie;onMicrotaskEmpty=new Ie;onStable=new Ie;onError=new Ie;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function $b(e){return jy(e,"__ignore_ng_zone__")}function zb(e){return jy(e,"__scheduler_tick__")}function jy(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var pa=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ed=[0,1,2,3],Dd=(()=>{class e{ngZone=h(k);scheduler=h(Le);errorHandler=h(le,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){h(en,{optional:!0})}execute(){let n=this.sequences.size>0;n&&L(16),this.executing=!0;for(let r of Ed)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&L(17)}register(n){let{view:r}=n;r!==void 0?((r[Sn]??=[]).push(n),Yt(r),r[T]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(fa.AFTER_NEXT_RENDER,n):n()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),bo=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Sn];t&&(this.view[Sn]=t.filter(n=>n!==this))}};function ha(e,t){let n=t?.injector??h(oe);return ze("NgAfterNextRender"),Wb(e,n,t,!0)}function Gb(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Wb(e,t,n,r){let o=t.get(pa);o.impl??=t.get(Dd);let i=t.get(en,null,{optional:!0}),s=n?.manualCleanup!==!0?t.get(Qe):null,a=t.get(xn,null,{optional:!0}),c=new bo(o.impl,Gb(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}function By(e){return e+1}function Ar(e,t){let n=e[I],r=By(t.index);return e[r]}function Po(e,t){let n=By(t.index);return e.data[n]}function qb(e,t,n){let r=t[I],o=Po(r,n);switch(e){case X.Complete:return o.primaryTmplIndex;case X.Loading:return o.loadingTmplIndex;case X.Error:return o.errorTmplIndex;case X.Placeholder:return o.placeholderTmplIndex;default:return null}}function Wh(e,t){return t===X.Placeholder?e.placeholderBlockConfig?.[Hh]??null:t===X.Loading?e.loadingBlockConfig?.[Hh]??null:null}function Zb(e){return e.loadingBlockConfig?.[Rb]??null}function qh(e,t){if(!e||e.length===0)return t;let n=new Set(e);for(let r of t)n.add(r);return e.length===n.size?e:Array.from(n)}function Yb(e,t){let n=t.primaryTmplIndex+P;return Rn(e,n)}var Kb=(()=>{class e{cachedInjectors=new Map;getOrCreateInjector(n,r,o,i){if(!this.cachedInjectors.has(n)){let s=o.length>0?hd(o,r,i):null;this.cachedInjectors.set(n,s)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e})}return e})();var Vy=new E("");function ku(e,t,n){return e.get(Kb).getOrCreateInjector(t,e,n,"")}function Qb(e,t,n){if(e instanceof mr){let o=e.injector,i=e.parentInjector,s=ku(i,t,n);return new mr(o,s)}let r=e.get(ue);if(r!==e){let o=ku(r,t,n);return new mr(e,o)}return ku(e,t,n)}function kn(e,t,n,r=!1){let o=n[Y],i=o[I];if(Zt(o))return;let s=Ar(o,t),a=s[ko],c=s[kb];if(!(c!==null&&e<c)&&Zh(a,e)&&Zh(s[xb]??-1,e)){let u=Po(i,t),d=!r&&!0&&(Zb(u)!==null||Wh(u,X.Loading)!==null||Wh(u,X.Placeholder))?eT:Jb;try{d(e,s,n,t,o)}catch(p){nd(o,p)}}}function Xb(e,t){let n=e[Ke]?.findIndex(o=>o.data[kg]===t[ko])??-1;return{dehydratedView:n>-1?e[Ke][n]:null,dehydratedViewIx:n}}function Jb(e,t,n,r,o){L(20);let i=qb(e,o,r);if(i!==null){t[ko]=e;let s=o[I],a=i+P,c=Rn(s,a),u=0;sd(n,u);let l;if(e===X.Complete){let g=Po(s,r),y=g.providers;y&&y.length>0&&(l=Qb(o[Ye],g,y))}let{dehydratedView:d,dehydratedViewIx:p}=Xb(n,t),f=Tr(o,c,null,{injector:l,dehydratedView:d});if(Mr(n,f,u,Bn(c,d)),ia(f,2),p>-1&&n[Ke]?.splice(p,1),(e===X.Complete||e===X.Error)&&Array.isArray(t[yr])){for(let g of t[yr])g();t[yr]=null}}L(21)}function Zh(e,t){return e<t}function Yh(e,t,n){e.loadingPromise.then(()=>{e.loadingState===ke.COMPLETE?kn(X.Complete,t,n):e.loadingState===ke.FAILED&&kn(X.Error,t,n)})}var eT=null;var Uy=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var _d=new E("");function ga(e){return!!e&&typeof e.then=="function"}function Id(e){return!!e&&typeof e.subscribe=="function"}var Hy=new E("");var Cd=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=h(Hy,{optional:!0})??[];injector=h(oe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=sr(this.injector,o);if(ga(i))n.push(i);else if(Id(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Rr=new E("");function $y(){Cc(()=>{let e="";throw new _(600,e)})}function zy(e){return e.isBoundToModule}var tT=10;var Me=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=h(Xe);afterRenderManager=h(pa);zonelessEnabled=h(fr);rootEffectScheduler=h(mo);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new $;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=h(At);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(W(n=>!n))}constructor(){h(en,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=h(ue);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=oe.NULL){return this._injector.get(k).run(()=>{L(10);let s=n instanceof da;if(!this._injector.get(Cd).done){let g="";throw new _(405,g)}let c;s?c=n:c=this._injector.get(Oo).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let u=zy(c)?void 0:this._injector.get(Un),l=r||c.selector,d=c.create(o,[],l,u),p=d.location.nativeElement,f=d.injector.get(_d,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),vo(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),L(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){L(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(fa.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=S(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,S(n),this.afterTick.next(),L(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Ot,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<tT;)L(14),this.synchronizeOnce(),L(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!fo(o))continue;let i=r&&!this.zonelessEnabled?0:1;id(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>fo(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;vo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Rr,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>vo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function vo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Gy(e,t,n){let r=t[Ye],o=t[I];if(e.loadingState!==ke.NOT_STARTED)return e.loadingPromise??Promise.resolve();let i=Ar(t,n),s=Yb(o,e);e.loadingState=ke.IN_PROGRESS,Ts(1,i);let a=e.dependencyResolverFn,c=r.get(pr).add();return a?(e.loadingPromise=Promise.allSettled(a()).then(u=>{let l=!1,d=[],p=[];for(let f of u)if(f.status==="fulfilled"){let g=f.value,y=ct(g)||rs(g);if(y)d.push(y);else{let m=Kc(g);m&&p.push(m)}}else{l=!0;break}if(l){if(e.loadingState=ke.FAILED,e.errorTmplIndex===null){let f="",g=new _(-750,!1);nd(t,g)}}else{e.loadingState=ke.COMPLETE;let f=s.tView;if(d.length>0){f.directiveRegistry=qh(f.directiveRegistry,d);let g=d.map(m=>m.type),y=os(!1,...g);e.providers=y}p.length>0&&(f.pipeRegistry=qh(f.pipeRegistry,p))}}),e.loadingPromise.finally(()=>{e.loadingPromise=null,c()})):(e.loadingPromise=Promise.resolve().then(()=>{e.loadingPromise=null,e.loadingState=ke.COMPLETE,c()}),e.loadingPromise)}function nT(e,t){return t[Ye].get(Vy,null,{optional:!0})?.behavior!==md.Manual}function rT(e,t,n){let r=t[I],o=t[n.index];if(!nT(e,t))return;let i=Ar(t,n),s=Po(r,n);switch(Lb(i),s.loadingState){case ke.NOT_STARTED:kn(X.Loading,n,o),Gy(s,t,n),s.loadingState===ke.IN_PROGRESS&&Yh(s,n,o);break;case ke.IN_PROGRESS:kn(X.Loading,n,o),Yh(s,n,o);break;case ke.COMPLETE:kn(X.Complete,n,o);break;case ke.FAILED:kn(X.Error,n,o);break;default:}}function Wy(e,t,n){return ot(this,null,function*(){let r=e.get(Ks);if(r.hydrating.has(t))return;let{parentBlockPromise:i,hydrationQueue:s}=HI(t,e);if(s.length===0)return;i!==null&&s.shift(),sT(r,s),i!==null&&(yield i);let a=s[0];r.has(a)?yield Kh(e,s,n):r.awaitParentBlock(a,()=>ot(null,null,function*(){return yield Kh(e,s,n)}))})}function Kh(e,t,n){return ot(this,null,function*(){let r=e.get(Ks),o=r.hydrating,i=e.get(At),s=i.add();for(let c=0;c<t.length;c++){let u=t[c],l=r.get(u);if(l!=null){if(yield cT(l),yield aT(e),oT(l)){Rw(l),Qh(t.slice(c),r);break}o.get(u).resolve()}else{iT(c,t,r),Qh(t.slice(c),r);break}}let a=t[t.length-1];yield o.get(a)?.promise,i.remove(s),n&&n(t),xw(r.get(a),t,r,e.get(Me))})}function oT(e){return Ar(e.lView,e.tNode)[ko]===X.Error}function iT(e,t,n){let r=e-1,o=r>-1?n.get(t[r]):null;o&&la(o.lContainer)}function Qh(e,t){let n=t.hydrating;for(let r in e)n.get(r)?.reject();t.cleanup(e)}function sT(e,t){for(let n of t)e.hydrating.set(n,Promise.withResolvers())}function aT(e){return new Promise(t=>ha(t,{injector:e}))}function cT(e){return ot(this,null,function*(){let{tNode:t,lView:n}=e,r=Ar(n,t);return new Promise(o=>{uT(r,o),rT(2,n,t)})})}function uT(e,t){Array.isArray(e[yr])||(e[yr]=[]),e[yr].push(t)}function Lo(e,t,n,r){let o=b(),i=Kt();if(tt(o,i,t)){let s=q(),a=go();XC(a,o,e,t,n,r)}return Lo}var ml=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Pu(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function lT(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=Pu(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let p=e.at(s),f=t[c],g=Pu(s,p,c,f,n);if(g!==0){g<0&&e.updateValue(s,f),s--,c--;continue}let y=n(i,u),m=n(s,p),v=n(i,l);if(Object.is(v,m)){let U=n(c,f);Object.is(U,y)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new $s,o??=Jh(e,i,s,n),yl(e,r,i,v))e.updateValue(i,l),i++,s++;else if(o.has(v))r.set(y,e.detach(i)),s--;else{let U=e.create(i,t[i]);e.attach(i,U),i++,s++}}for(;i<=c;)Xh(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,p=Pu(i,l,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new $s,o??=Jh(e,i,s,n);let f=n(i,d);if(yl(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let g=n(i,l);r.set(g,e.detach(i)),s--}}}for(;!u.done;)Xh(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function yl(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Xh(e,t,n,r,o){if(yl(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Jh(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var $s=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function dT(e,t,n,r,o,i,s,a){ze("NgControlFlow");let c=b(),u=q(),l=Fe(u.consts,i);return _r(c,u,e,t,n,r,o,l,256,s,a),wd}function wd(e,t,n,r,o,i,s,a){ze("NgControlFlow");let c=b(),u=q(),l=Fe(u.consts,i);return _r(c,u,e,t,n,r,o,l,512,s,a),wd}function fT(e,t){ze("NgControlFlow");let n=b(),r=Kt(),o=n[r]!==Te?n[r]:-1,i=o!==-1?zs(n,P+o):void 0,s=0;if(tt(n,r,e)){let a=S(null);try{if(i!==void 0&&sd(i,s),e!==-1){let c=P+e,u=zs(n,c),l=_l(n[I],c),d=ny(u,l,n),p=Tr(n,l,t,{dehydratedView:d});Mr(u,p,s,Bn(l,d))}}finally{S(a)}}else if(i!==void 0){let a=Ym(i,s);a!==void 0&&(a[K]=t)}}var vl=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-ie}};function pT(e){return e}function hT(e,t){return t}var El=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function gT(e,t,n,r,o,i,s,a,c,u,l,d,p){ze("NgControlFlow");let f=b(),g=q(),y=c!==void 0,m=b(),v=a?s.bind(m[fe][K]):s,U=new El(y,v);m[P+e]=U,_r(f,g,e+1,t,n,r,o,Fe(g.consts,i),256),y&&_r(f,g,e+2,c,u,l,d,Fe(g.consts,p),512)}var Dl=class extends ml{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-ie}at(t){return this.getLView(t)[K].$implicit}attach(t,n){let r=n[_e];this.needsIndexUpdate||=t!==this.length,Mr(this.lContainer,n,t,Bn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,yT(this.lContainer,t)}create(t,n){let r=Co(this.lContainer,this.templateTNode.tView.ssrId),o=Tr(this.hostLView,this.templateTNode,new vl(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){na(t[I],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[K].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[K].$index=t}getLView(t){return vT(this.lContainer,t)}};function mT(e){let t=S(null),n=ht();try{let r=b(),o=r[I],i=r[n],s=n+1,a=zs(r,s);if(i.liveCollection===void 0){let u=_l(o,s);i.liveCollection=new Dl(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(lT(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Kt(),l=c.length===0;if(tt(r,u,l)){let d=n+2,p=zs(r,d);if(l){let f=_l(o,d),g=ny(p,f,r),y=Tr(r,f,void 0,{dehydratedView:g});Mr(p,y,0,Bn(f,g))}else o.firstUpdatePass&&ua(p),sd(p,0)}}}finally{S(t)}}function zs(e,t){return e[t]}function yT(e,t){return Io(e,t)}function vT(e,t){return Ym(e,t)}function _l(e,t){return Rn(e,t)}function qy(e,t,n){let r=b(),o=Kt();if(tt(r,o,t)){let i=q(),s=go();Fm(s,r,e,t,r[F],n)}return qy}function Il(e,t,n,r,o){rd(t,e,n,o?"class":"style",r)}function bd(e,t,n,r){let o=b(),i=o[I],s=e+P,a=i.firstCreatePass?cy(s,o,2,t,Pm,fu(),n,r):i.data[s];if(Lm(a,o,e,t,Nd),lo(a)){let c=o[I];td(c,o,a),om(c,a,o)}return r!=null&&oa(o,a),bd}function Td(){let e=q(),t=se(),n=jm(t);return e.firstCreatePass&&uy(e,n),pu(n)&&hu(),du(),n.classesWithoutHost!=null&&cI(n)&&Il(e,n,b(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&uI(n)&&Il(e,n,b(),n.stylesWithoutHost,!1),Td}function Zy(e,t,n,r){return bd(e,t,n,r),Td(),Zy}function Md(e,t,n,r){let o=b(),i=o[I],s=e+P,a=i.firstCreatePass?Zw(s,i,2,t,n,r):i.data[s];return Lm(a,o,e,t,Nd),r!=null&&oa(o,a),Md}function Sd(){let e=se(),t=jm(e);return pu(t)&&hu(),du(),Sd}function Yy(e,t,n,r){return Md(e,t,n,r),Sd(),Yy}var Nd=(e,t,n,r,o)=>(Nt(!0),Wl(t[F],r,Iu()));function ET(e,t,n,r,o){let i=!Xs(t,n);if(Nt(i),i)return Wl(t[F],r,Iu());let s=t[_e],a=aa(s,e,t,n);return em(s,o)&&Qs(s,o,a.nextSibling),s&&(_g(n)||Ig(a))&&St(n)&&(Zp(n),_m(a)),a}function Ky(){Nd=ET}var DT=(e,t,n,r,o)=>(Nt(!0),Em(t[F],""));function _T(e,t,n,r,o){let i,s=!Xs(t,n);if(Nt(s),s)return Em(t[F],"");let a=t[_e],c=aa(a,e,t,n),u=VI(a,o);return Qs(a,o,c),i=ca(u,c),i}function Qy(){DT=_T}function IT(){return b()}function Xy(e,t,n){let r=b(),o=Kt();if(tt(r,o,t)){let i=q(),s=go();km(s,r,e,t,r[F],n)}return Xy}var Fn=void 0;function CT(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var wT=["en",[["a","p"],["AM","PM"],Fn],[["AM","PM"],Fn,Fn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Fn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Fn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Fn,"{1} 'at' {0}",Fn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",CT],Lu={};function Pe(e){let t=bT(e),n=eg(t);if(n)return n;let r=t.split("-")[0];if(n=eg(r),n)return n;if(r==="en")return wT;throw new _(701,!1)}function eg(e){return e in Lu||(Lu[e]=je.ng&&je.ng.common&&je.ng.common.locales&&je.ng.common.locales[e]),Lu[e]}var Q=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(Q||{});function bT(e){return e.toLowerCase().replace(/_/g,"-")}var jo="en-US";var TT=jo;function Jy(e){typeof e=="string"&&(TT=e.toLowerCase().replace(/_/g,"-"))}function ev(e,t,n){let r=b(),o=q(),i=se();return nv(o,r,r[F],i,e,t,n),ev}function tv(e,t,n){let r=b(),o=q(),i=se();return(i.type&3||n)&&dy(i,o,r,n,r[F],e,t,bs(i,r,t)),tv}function nv(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=bs(r,t,i),dy(r,e,t,s,n,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let p=l[d],f=l[d+1];c??=bs(r,t,i),Lh(r,t,p,f,o,c)}if(u&&u.length)for(let d of u)c??=bs(r,t,i),Lh(r,t,d,o,o,c)}}function MT(e=1){return uh(e)}function ST(e,t){let n=null,r=vC(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?ym(e,i,!0):_C(r,i))return o}return n}function Ad(e){let t=b()[fe][De];if(!t.projection){let n=e?e.length:1,r=t.projection=Rp(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?ST(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Rd(e,t=0,n,r,o,i){let s=b(),a=q(),c=r?e+1:null;c!==null&&_r(s,a,c,r,o,i,null,n);let u=Sr(a,P+e,16,null,n||null);u.projection===null&&(u.projection=t),yu();let d=!s[_e]||cs();s[fe][De].projection[u.projection]===null&&c!==null?NT(s,a,c):d&&!Ao(u)&&UC(a,s,u)}function NT(e,t,n){let r=P+n,o=t.data[r],i=e[r],s=Co(i,o.tView.ssrId),a=Tr(e,o,void 0,{dehydratedView:s});Mr(i,a,0,Bn(o,s))}function AT(e,t,n,r){_y(e,t,n,r)}function RT(e,t,n){Dy(e,t,n)}function xT(e){let t=b(),n=q(),r=ds();ho(r+1);let o=dd(n,r);if(e.dirty&&Up(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Cy(t,r);e.reset(i,Eg),e.notifyOnChanges()}return!0}return!1}function OT(){return ld(b(),ds())}function FT(e,t,n,r,o){by(t,_y(e,n,r,o))}function kT(e,t,n,r){by(e,Dy(t,n,r))}function PT(e=1){ho(ds()+e)}function LT(e){let t=Xp();return ou(t,P+e)}function Es(e,t){return e<<17|t<<2}function Hn(e){return e>>17&32767}function jT(e){return(e&2)==2}function BT(e,t){return e&131071|t<<17}function Cl(e){return e|2}function Ir(e){return(e&131068)>>2}function ju(e,t){return e&-131069|t<<2}function VT(e){return(e&1)===1}function wl(e){return e|1}function UT(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Hn(s),c=Ir(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||or(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let p=Hn(e[a+1]);e[r+1]=Es(p,a),p!==0&&(e[p+1]=ju(e[p+1],r)),e[a+1]=BT(e[a+1],r)}else e[r+1]=Es(a,0),a!==0&&(e[a+1]=ju(e[a+1],r)),a=r;else e[r+1]=Es(c,0),a===0?a=r:e[c+1]=ju(e[c+1],r),c=r;u&&(e[r+1]=Cl(e[r+1])),tg(e,l,r,!0),tg(e,l,r,!1),HT(t,l,e,r,i),s=Es(a,c),i?t.classBindings=s:t.styleBindings=s}function HT(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&or(i,t)>=0&&(n[r+1]=wl(n[r+1]))}function tg(e,t,n,r){let o=e[n+1],i=t===null,s=r?Hn(o):Ir(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];$T(c,t)&&(a=!0,e[s+1]=r?wl(u):Cl(u)),s=r?Hn(u):Ir(u)}a&&(e[n+1]=r?Cl(o):wl(o))}function $T(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?or(e,t)>=0:!1}var et={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function zT(e){return e.substring(et.key,et.keyEnd)}function GT(e){return WT(e),rv(e,ov(e,0,et.textEnd))}function rv(e,t){let n=et.textEnd;return n===t?-1:(t=et.keyEnd=qT(e,et.key=t,n),ov(e,t,n))}function WT(e){et.key=0,et.keyEnd=0,et.value=0,et.valueEnd=0,et.textEnd=e.length}function ov(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function qT(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function iv(e,t,n){return sv(e,t,n,!1),iv}function ma(e,t){return sv(e,t,null,!0),ma}function xd(e){YT(tM,ZT,e,!0)}function ZT(e,t){for(let n=GT(t);n>=0;n=rv(t,n))ts(e,zT(t),!0)}function sv(e,t,n,r){let o=b(),i=q(),s=us(2);if(i.firstUpdatePass&&cv(i,e,s,r),t!==Te&&tt(o,s,t)){let a=i.data[ht()];uv(i,a,o,o[F],e,o[s+1]=rM(t,n),r,s)}}function YT(e,t,n,r){let o=q(),i=us(2);o.firstUpdatePass&&cv(o,null,i,r);let s=b();if(n!==Te&&tt(s,i,n)){let a=o.data[ht()];if(lv(a,r)&&!av(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Ki(c,n||"")),Il(o,a,s,n,r)}else nM(o,a,s,s[F],s[i+1],s[i+1]=eM(e,t,n),r,i)}}function av(e,t){return t>=e.expandoStartIndex}function cv(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[ht()],s=av(e,n);lv(i,r)&&t===null&&!s&&(t=!1),t=KT(o,i,t,r),UT(o,i,t,n,s,r)}}function KT(e,t,n,r){let o=ih(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Bu(null,e,t,n,r),n=To(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Bu(o,e,t,n,r),i===null){let c=QT(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Bu(null,e,t,c[1],r),c=To(c,t.attrs,r),XT(e,t,r,c))}else i=JT(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function QT(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Ir(r)!==0)return e[Hn(r)]}function XT(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Hn(o)]=r}function JT(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=To(r,s,n)}return To(r,t.attrs,n)}function Bu(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=To(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function To(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ts(e,s,n?!0:t[++i]))}return e===void 0?null:e}function eM(e,t,n){if(n==null||n==="")return pe;let r=[],o=He(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function tM(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ts(e,r,n)}function nM(e,t,n,r,o,i,s,a){o===Te&&(o=pe);let c=0,u=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=u<i.length?i[u+1]:void 0,g=null,y;l===d?(c+=2,u+=2,p!==f&&(g=d,y=f)):d===null||l!==null&&l<d?(c+=2,g=l):(u+=2,g=d,y=f),g!==null&&uv(e,t,n,r,g,y,s,a),l=c<o.length?o[c]:null,d=u<i.length?i[u]:null}}function uv(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=VT(u)?ng(c,t,n,o,Ir(u),s):void 0;if(!Gs(l)){Gs(i)||jT(u)&&(i=ng(c,null,n,o,a,s));let d=ru(ht(),n);$C(r,s,d,o,i)}}function ng(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,p=n[o+1];p===Te&&(p=d?pe:void 0);let f=d?ns(p,r):l===r?p:void 0;if(u&&!Gs(f)&&(f=ns(c,r)),Gs(f)&&(a=f,s))return a;let g=e[o+1];o=s?Hn(g):Ir(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ns(c,r))}return a}function Gs(e){return e!==void 0}function rM(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=bt(He(e)))),e}function lv(e,t){return(e.flags&(t?8:16))!==0}function oM(e,t=""){let n=b(),r=q(),o=e+P,i=r.firstCreatePass?Sr(r,o,1,t,null):r.data[o],s=dv(r,n,i,t,e);n[o]=s,hs()&&Jl(r,n,s,i),ur(i,!1)}var dv=(e,t,n,r,o)=>(Nt(!0),vm(t[F],r));function iM(e,t,n,r,o){let i=!Xs(t,n);if(Nt(i),i)return vm(t[F],r);let s=t[_e];return aa(s,e,t,n)}function fv(){dv=iM}function pv(e,t,n,r=""){return tt(e,Kt(),n)?t+Gt(n)+r:Te}function sM(e,t,n,r,o,i=""){let s=eh(),a=ly(e,s,n,o);return us(2),a?t+Gt(n)+r+Gt(o)+i:Te}function hv(e){return Od("",e),hv}function Od(e,t,n){let r=b(),o=pv(r,e,t,n);return o!==Te&&mv(r,ht(),o),Od}function gv(e,t,n,r,o){let i=b(),s=sM(i,e,t,n,r,o);return s!==Te&&mv(i,ht(),s),gv}function mv(e,t,n){let r=ru(t,e);bC(e[F],r,n)}function yv(e,t,n){bu(t)&&(t=t());let r=b(),o=Kt();if(tt(r,o,t)){let i=q(),s=go();Fm(s,r,e,t,r[F],n)}return yv}function aM(e,t){let n=bu(e);return n&&e.set(t),n}function vv(e,t){let n=b(),r=q(),o=se();return nv(r,n,n[F],o,e,t),vv}function cM(e,t,n=""){return pv(b(),e,t,n)}function uM(e,t,n){let r=q();if(r.firstCreatePass){let o=pt(e);bl(n,r.data,r.blueprint,o,!0),bl(t,r.data,r.blueprint,o,!1)}}function bl(e,t,n,r,o){if(e=ce(e),Array.isArray(e))for(let i=0;i<e.length;i++)bl(e[i],t,n,r,o);else{let i=q(),s=b(),a=se(),c=Cn(e)?e:ce(e.provide),u=Xc(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Cn(e)||!e.multi){let f=new Ln(u,o,$e,null),g=Uu(c,t,o?l:l+p,d);g===-1?($u(Rs(a,s),i,c),Vu(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[g]=f,s[g]=f)}else{let f=Uu(c,t,l+p,d),g=Uu(c,t,l,l+p),y=f>=0&&n[f],m=g>=0&&n[g];if(o&&!m||!o&&!y){$u(Rs(a,s),i,c);let v=fM(o?dM:lM,n.length,o,r,u,e);!o&&m&&(n[g].providerFactory=v),Vu(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(v),s.push(v)}else{let v=Ev(n[o?g:f],u,!o&&r);Vu(i,e,f>-1?f:g,v)}!o&&r&&m&&n[g].componentProviders++}}}function Vu(e,t,n,r){let o=Cn(t),i=Lp(t);if(o||i){let c=(i?ce(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function Ev(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Uu(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function lM(e,t,n,r,o){return Tl(this.multi,[])}function dM(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Eo(r,r[I],this.providerFactory.index,o);s=c.slice(0,a),Tl(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Tl(i,s);return s}function Tl(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function fM(e,t,n,r,o,i){let s=new Ln(e,n,$e,null);return s.multi=[],s.index=t,s.componentProviders=0,Ev(s,o,r&&!n),s}function Dv(e,t=[]){return n=>{n.providersResolver=(r,o)=>uM(r,o?o(e):e,t)}}function pM(e,t){let n=e[t];return n===Te?void 0:n}function hM(e,t,n,r,o,i,s){let a=t+n;return ly(e,a,o,i)?Yw(e,a+2,s?r.call(s,o,i):r(o,i)):pM(e,a+2)}function gM(e,t){let n=q(),r,o=e+P;n.firstCreatePass?(r=mM(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=$t(r.type,!0)),s,a=ve($e);try{let c=As(!1),u=i();return As(c),iu(n,b(),o,u),u}finally{ve(a)}}function mM(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function yM(e,t,n,r){let o=e+P,i=b(),s=ou(i,o);return vM(i,o)?hM(i,Jp(),t,s.transform,n,r,s):s.transform(n,r)}function vM(e,t){return e[I].data[t].pure}function EM(e,t){return sa(e,t)}var Ws=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},DM=(()=>{class e{compileModuleSync(n){return new Vs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Yc(n),i=gm(o.declarations).reduce((s,a)=>{let c=ct(a);return c&&s.push(new Vn(c)),s},[]);return new Ws(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _M=(()=>{class e{zone=h(k);changeDetectionScheduler=h(Le);applicationRef=h(Me);applicationErrorHandler=h(Xe);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_v=new E("",{factory:()=>!1});function Fd({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new k(te(B({},kd()),{scheduleInRootZone:n})),[{provide:k,useFactory:e},{provide:Ze,multi:!0,useFactory:()=>{let r=h(_M,{optional:!0});return()=>r.initialize()}},{provide:Ze,multi:!0,useFactory:()=>{let r=h(CM);return()=>{r.initialize()}}},t===!0?{provide:Tu,useValue:!0}:[],{provide:Mu,useValue:n??Py},{provide:Xe,useFactory:()=>{let r=h(k),o=h(ue),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(le),i.handleError(s))})}}}]}function IM(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Fd({ngZoneFactory:()=>{let o=kd(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&ze("NgZone_CoalesceEvent"),new k(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return ut([{provide:_v,useValue:!0},{provide:fr,useValue:!1},r])}function kd(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var CM=(()=>{class e{subscription=new Z;initialized=!1;zone=h(k);pendingTasks=h(At);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{k.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{k.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Iv=(()=>{class e{applicationErrorHandler=h(Xe);appRef=h(Me);taskService=h(At);ngZone=h(k);zonelessEnabled=h(fr);tracing=h(en,{optional:!0});disableScheduling=h(Tu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Z;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Us):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(h(Mu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Hs||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?$h:Ly;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Us+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,$h(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function wM(){return typeof $localize<"u"&&$localize.locale||jo}var Bo=new E("",{providedIn:"root",factory:()=>h(Bo,{optional:!0,skipSelf:!0})||wM()});function Cv(e){return _p(e)}function wv(e,t){return Xr(e,t?.equal)}var Pd=class{[re];constructor(t){this[re]=t}destroy(){this[re].destroy()}};function Ld(e,t){let n=t?.injector??h(oe),r=t?.manualCleanup!==!0?n.get(Qe):null,o,i=n.get(xn,null,{optional:!0}),s=n.get(Le);return i!==null?(o=MM(i.view,s,e),r instanceof oo&&r._lView===i.view&&(r=null)):o=SM(e,n.get(mo),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Pd(o)}var bv=te(B({},Ut),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:On,run(){if(this.dirty=!1,this.hasRun&&!Dn(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Ct(this),n=lr(!1);try{this.maybeCleanup(),this.fn(e)}finally{lr(n),Ht(this,t)}},maybeCleanup(){if(!this.cleanupFns?.length)return;let e=S(null);try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[],S(e)}}}),bM=te(B({},bv),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){tr(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),TM=te(B({},bv),{consumerMarkedDirty(){this.view[T]|=8192,Yt(this.view),this.notifier.notify(13)},destroy(){tr(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Mt]?.delete(this)}});function MM(e,t,n){let r=Object.create(TM);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Mt]??=new Set,e[Mt].add(r),r.consumerMarkedDirty(r),r}function SM(e,t,n){let r=Object.create(bM);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.add(r),r.notifier.notify(12),r}var Hd={JSACTION:"__jsaction",OWNER:"__owner"},Nv={};function NM(e){return e[Hd.JSACTION]}function Tv(e,t){e[Hd.JSACTION]=t}function AM(e){return Nv[e]}function RM(e,t){Nv[e]=t}var w={AUXCLICK:"auxclick",CHANGE:"change",CLICK:"click",CLICKMOD:"clickmod",CLICKONLY:"clickonly",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEUP:"mouseup",MOUSEDOWN:"mousedown",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",MOUSEMOVE:"mousemove",POINTERUP:"pointerup",POINTERDOWN:"pointerdown",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",POINTERMOVE:"pointermove",POINTERCANCEL:"pointercancel",GOTPOINTERCAPTURE:"gotpointercapture",LOSTPOINTERCAPTURE:"lostpointercapture",ERROR:"error",LOAD:"load",UNLOAD:"unload",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",INPUT:"input",SCROLL:"scroll",TOGGLE:"toggle",CUSTOM:"_custom"},xM=[w.MOUSEENTER,w.MOUSELEAVE,"pointerenter","pointerleave"],rj=[w.CLICK,w.DBLCLICK,w.FOCUSIN,w.FOCUSOUT,w.KEYDOWN,w.KEYUP,w.KEYPRESS,w.MOUSEOVER,w.MOUSEOUT,w.SUBMIT,w.TOUCHSTART,w.TOUCHEND,w.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],OM=[w.FOCUS,w.BLUR,w.ERROR,w.LOAD,w.TOGGLE],$d=e=>OM.indexOf(e)>=0;function FM(e){return e===w.MOUSEENTER?w.MOUSEOVER:e===w.MOUSELEAVE?w.MOUSEOUT:e===w.POINTERENTER?w.POINTEROVER:e===w.POINTERLEAVE?w.POINTEROUT:e}function kM(e,t,n,r){let o=!1;$d(t)&&(o=!0);let i=typeof r=="boolean"?{capture:o,passive:r}:o;return e.addEventListener(t,n,i),{eventType:t,handler:n,capture:o,passive:r}}function PM(e,t){if(e.removeEventListener){let n=typeof t.passive=="boolean"?{capture:t.capture}:t.capture;e.removeEventListener(t.eventType,t.handler,n)}else e.detachEvent&&e.detachEvent(`on${t.eventType}`,t.handler)}function LM(e){e.preventDefault?e.preventDefault():e.returnValue=!1}var Mv=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);function jM(e){return e.which===2||e.which==null&&e.button===4}function BM(e){return Mv&&e.metaKey||!Mv&&e.ctrlKey||jM(e)||e.shiftKey}function VM(e,t,n){let r=e.relatedTarget;return(e.type===w.MOUSEOVER&&t===w.MOUSEENTER||e.type===w.MOUSEOUT&&t===w.MOUSELEAVE||e.type===w.POINTEROVER&&t===w.POINTERENTER||e.type===w.POINTEROUT&&t===w.POINTERLEAVE)&&(!r||r!==n&&!n.contains(r))}function UM(e,t){let n={};for(let r in e){if(r==="srcElement"||r==="target")continue;let o=r,i=e[o];typeof i!="function"&&(n[o]=i)}return e.type===w.MOUSEOVER?n.type=w.MOUSEENTER:e.type===w.MOUSEOUT?n.type=w.MOUSELEAVE:e.type===w.POINTEROVER?n.type=w.POINTERENTER:n.type=w.POINTERLEAVE,n.target=n.srcElement=t,n.bubbles=!1,n._originalEvent=e,n}var HM=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent),Da=class{element;handlerInfos=[];constructor(t){this.element=t}addEventListener(t,n,r){HM&&(this.element.style.cursor="pointer"),this.handlerInfos.push(kM(this.element,t,n(this.element),r))}cleanUp(){for(let t=0;t<this.handlerInfos.length;t++)PM(this.element,this.handlerInfos[t]);this.handlerInfos=[]}},$M={NAMESPACE_ACTION_SEPARATOR:".",EVENT_ACTION_SEPARATOR:":"};function tn(e){return e.eventType}function zd(e,t){e.eventType=t}function va(e){return e.event}function Av(e,t){e.event=t}function Rv(e){return e.targetElement}function xv(e,t){e.targetElement=t}function Ov(e){return e.eic}function zM(e,t){e.eic=t}function GM(e){return e.timeStamp}function WM(e,t){e.timeStamp=t}function Ea(e){return e.eia}function Fv(e,t,n){e.eia=[t,n]}function jd(e){e.eia=void 0}function ya(e){return e[1]}function qM(e){return e.eirp}function kv(e,t){e.eirp=t}function Pv(e){return e.eir}function Lv(e,t){e.eir=t}function jv(e){return{eventType:e.eventType,event:e.event,targetElement:e.targetElement,eic:e.eic,eia:e.eia,timeStamp:e.timeStamp,eirp:e.eirp,eiack:e.eiack,eir:e.eir}}function ZM(e,t,n,r,o,i,s,a){return{eventType:e,event:t,targetElement:n,eic:r,timeStamp:o,eia:i,eirp:s,eiack:a}}var Bd=class e{eventInfo;constructor(t){this.eventInfo=t}getEventType(){return tn(this.eventInfo)}setEventType(t){zd(this.eventInfo,t)}getEvent(){return va(this.eventInfo)}setEvent(t){Av(this.eventInfo,t)}getTargetElement(){return Rv(this.eventInfo)}setTargetElement(t){xv(this.eventInfo,t)}getContainer(){return Ov(this.eventInfo)}setContainer(t){zM(this.eventInfo,t)}getTimestamp(){return GM(this.eventInfo)}setTimestamp(t){WM(this.eventInfo,t)}getAction(){let t=Ea(this.eventInfo);if(t)return{name:t[0],element:t[1]}}setAction(t){if(!t){jd(this.eventInfo);return}Fv(this.eventInfo,t.name,t.element)}getIsReplay(){return qM(this.eventInfo)}setIsReplay(t){kv(this.eventInfo,t)}getResolved(){return Pv(this.eventInfo)}setResolved(t){Lv(this.eventInfo,t)}clone(){return new e(jv(this.eventInfo))}},YM={},KM=/\s*;\s*/,QM=w.CLICK,Vd=class{a11yClickSupport=!1;clickModSupport=!0;syntheticMouseEventSupport;updateEventInfoForA11yClick=void 0;preventDefaultForA11yClick=void 0;populateClickOnlyAction=void 0;constructor({syntheticMouseEventSupport:t=!1,clickModSupport:n=!0}={}){this.syntheticMouseEventSupport=t,this.clickModSupport=n}resolveEventType(t){this.clickModSupport&&tn(t)===w.CLICK&&BM(va(t))?zd(t,w.CLICKMOD):this.a11yClickSupport&&this.updateEventInfoForA11yClick(t)}resolveAction(t){Pv(t)||(this.populateAction(t,Rv(t)),Lv(t,!0))}resolveParentAction(t){let n=Ea(t),r=n&&ya(n);jd(t);let o=r&&this.getParentNode(r);o&&this.populateAction(t,o)}populateAction(t,n){let r=n;for(;r&&r!==Ov(t)&&(r.nodeType===Node.ELEMENT_NODE&&this.populateActionOnElement(r,t),!Ea(t));)r=this.getParentNode(r);let o=Ea(t);if(o&&(this.a11yClickSupport&&this.preventDefaultForA11yClick(t),this.syntheticMouseEventSupport&&(tn(t)===w.MOUSEENTER||tn(t)===w.MOUSELEAVE||tn(t)===w.POINTERENTER||tn(t)===w.POINTERLEAVE)))if(VM(va(t),tn(t),ya(o))){let i=UM(va(t),ya(o));Av(t,i),xv(t,ya(o))}else jd(t)}getParentNode(t){let n=t[Hd.OWNER];if(n)return n;let r=t.parentNode;return r?.nodeName==="#document-fragment"?r?.host??null:r}populateActionOnElement(t,n){let r=this.parseActions(t),o=r[tn(n)];o!==void 0&&Fv(n,o,t),this.a11yClickSupport&&this.populateClickOnlyAction(t,n,r)}parseActions(t){let n=NM(t);if(!n){let r=t.getAttribute(gs.JSACTION);if(!r)n=YM,Tv(t,n);else{if(n=AM(r),!n){n={};let o=r.split(KM);for(let i=0;i<o.length;i++){let s=o[i];if(!s)continue;let a=s.indexOf($M.EVENT_ACTION_SEPARATOR),c=a!==-1,u=c?s.substr(0,a).trim():QM,l=c?s.substr(a+1).trim():s;n[u]=l}RM(r,n)}Tv(t,n)}}return n}addA11yClickSupport(t,n,r){this.a11yClickSupport=!0,this.updateEventInfoForA11yClick=t,this.preventDefaultForA11yClick=n,this.populateClickOnlyAction=r}},Bv=function(e){return e[e.I_AM_THE_JSACTION_FRAMEWORK=0]="I_AM_THE_JSACTION_FRAMEWORK",e}(Bv||{}),Ud=class{dispatchDelegate;actionResolver;eventReplayer;eventReplayScheduled=!1;replayEventInfoWrappers=[];constructor(t,{actionResolver:n,eventReplayer:r}={}){this.dispatchDelegate=t,this.actionResolver=n,this.eventReplayer=r}dispatch(t){let n=new Bd(t);this.actionResolver?.resolveEventType(t),this.actionResolver?.resolveAction(t);let r=n.getAction();if(r&&XM(r.element,n)&&LM(n.getEvent()),this.eventReplayer&&n.getIsReplay()){this.scheduleEventInfoWrapperReplay(n);return}this.dispatchDelegate(n)}scheduleEventInfoWrapperReplay(t){this.replayEventInfoWrappers.push(t),!this.eventReplayScheduled&&(this.eventReplayScheduled=!0,Promise.resolve().then(()=>{this.eventReplayScheduled=!1,this.eventReplayer(this.replayEventInfoWrappers)}))}};function XM(e,t){return e.tagName==="A"&&(t.getEventType()===w.CLICK||t.getEventType()===w.CLICKMOD)}var Vv=Symbol.for("propagationStopped"),Gd={REPLAY:101};var JM="`preventDefault` called during event replay.";var eS="`composedPath` called during event replay.",_a=class{dispatchDelegate;clickModSupport;actionResolver;dispatcher;constructor(t,n=!0){this.dispatchDelegate=t,this.clickModSupport=n,this.actionResolver=new Vd({clickModSupport:n}),this.dispatcher=new Ud(r=>{this.dispatchToDelegate(r)},{actionResolver:this.actionResolver})}dispatch(t){this.dispatcher.dispatch(t)}dispatchToDelegate(t){for(t.getIsReplay()&&rS(t),tS(t);t.getAction();){if(oS(t),$d(t.getEventType())&&t.getAction().element!==t.getTargetElement()||(this.dispatchDelegate(t.getEvent(),t.getAction().name),nS(t)))return;this.actionResolver.resolveParentAction(t.eventInfo)}}};function tS(e){let t=e.getEvent(),n=e.getEvent().stopPropagation.bind(t),r=()=>{t[Vv]=!0,n()};zn(t,"stopPropagation",r),zn(t,"stopImmediatePropagation",r)}function nS(e){return!!e.getEvent()[Vv]}function rS(e){let t=e.getEvent(),n=e.getTargetElement(),r=t.preventDefault.bind(t);zn(t,"target",n),zn(t,"eventPhase",Gd.REPLAY),zn(t,"preventDefault",()=>{throw r(),new Error(JM+"")}),zn(t,"composedPath",()=>{throw new Error(eS+"")})}function oS(e){let t=e.getEvent(),n=e.getAction()?.element;n&&zn(t,"currentTarget",n,{configurable:!0})}function zn(e,t,n,{configurable:r=!1}={}){Object.defineProperty(e,t,{value:n,configurable:r})}function Uv(e,t){e.ecrd(n=>{t.dispatch(n)},Bv.I_AM_THE_JSACTION_FRAMEWORK)}function iS(e){return e?.q??[]}function sS(e){e&&(Sv(e.c,e.et,e.h),Sv(e.c,e.etc,e.h,!0))}function Sv(e,t,n,r){for(let o=0;o<t.length;o++)e.removeEventListener(t[o],n,r)}var aS=!1,Hv=(()=>{class e{static MOUSE_SPECIAL_SUPPORT=aS;containerManager;eventHandlers={};browserEventTypeToExtraEventTypes={};dispatcher=null;queuedEventInfos=[];constructor(n){this.containerManager=n}handleEvent(n,r,o){let i=ZM(n,r,r.target,o,Date.now());this.handleEventInfo(i)}handleEventInfo(n){if(!this.dispatcher){kv(n,!0),this.queuedEventInfos?.push(n);return}this.dispatcher(n)}addEvent(n,r,o){if(n in this.eventHandlers||!this.containerManager||!e.MOUSE_SPECIAL_SUPPORT&&xM.indexOf(n)>=0)return;let i=(a,c,u)=>{this.handleEvent(a,c,u)};this.eventHandlers[n]=i;let s=FM(r||n);if(s!==n){let a=this.browserEventTypeToExtraEventTypes[s]||[];a.push(n),this.browserEventTypeToExtraEventTypes[s]=a}this.containerManager.addEventListener(s,a=>c=>{i(n,c,a)},o)}replayEarlyEvents(n=window._ejsa){n&&(this.replayEarlyEventInfos(n.q),sS(n),delete window._ejsa)}replayEarlyEventInfos(n){for(let r=0;r<n.length;r++){let o=n[r],i=this.getEventTypesForBrowserEventType(o.eventType);for(let s=0;s<i.length;s++){let a=jv(o);zd(a,i[s]),this.handleEventInfo(a)}}}getEventTypesForBrowserEventType(n){let r=[];return this.eventHandlers[n]&&r.push(n),this.browserEventTypeToExtraEventTypes[n]&&r.push(...this.browserEventTypeToExtraEventTypes[n]),r}handler(n){return this.eventHandlers[n]}cleanUp(){this.containerManager?.cleanUp(),this.containerManager=null,this.eventHandlers={},this.browserEventTypeToExtraEventTypes={},this.dispatcher=null,this.queuedEventInfos=[]}registerDispatcher(n,r){this.ecrd(n,r)}ecrd(n,r){if(this.dispatcher=n,this.queuedEventInfos?.length){for(let o=0;o<this.queuedEventInfos.length;o++)this.handleEventInfo(this.queuedEventInfos[o]);this.queuedEventInfos=null}}}return e})();function $v(e,t=window){return iS(t._ejsas?.[e])}function Wd(e,t=window){t._ejsas&&(t._ejsas[e]=void 0)}var Qv=Symbol("InputSignalNode#UNSET"),CS=te(B({},Jr),{transformFn:void 0,applyValueToInputSignal(e,t){nr(e,t)}});function Xv(e,t){let n=Object.create(CS);n.value=e,n.transformFn=t?.transform;function r(){if(En(n),n.value===Qv){let o=null;throw new _(-950,o)}return n.value}return r[re]=n,r}var ba=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>Sl(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},wS=new E("");wS.__NG_ELEMENT_ID__=e=>{let t=se();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new _(204,!1)};function zv(e,t){return Xv(e,t)}function bS(e){return Xv(Qv,e)}var MH=(zv.required=bS,zv);function Gv(e,t){return fd(t)}function TS(e,t){return pd(t)}var SH=(Gv.required=TS,Gv);function Wv(e,t){return fd(t)}function MS(e,t){return pd(t)}var NH=(Wv.required=MS,Wv);var Zd=new E(""),SS=new E("");function Vo(e){return!e.moduleRef}function NS(e){let t=Vo(e)?e.r3Injector:e.moduleRef.injector,n=t.get(k);return n.run(()=>{Vo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Xe),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Vo(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Zd);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Zd);s.add(i),e.moduleRef.onDestroy(()=>{vo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return RS(r,n,()=>{let i=t.get(At),s=i.add(),a=t.get(Cd);return a.runInitializers(),a.donePromise.then(()=>{try{let c=t.get(Bo,jo);if(Jy(c||jo),!t.get(SS,!0))return Vo(e)?t.get(Me):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Vo(e)){let l=t.get(Me);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return AS?.(e.moduleRef,e.allPlatformModules),e.moduleRef}finally{i.remove(s)}})})})}var AS;function RS(e,t,n){try{let r=n();return ga(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Ca=null;function xS(e=[],t){return oe.create({name:t,providers:[{provide:ao,useValue:"platform"},{provide:Zd,useValue:new Set([()=>Ca=null])},...e]})}function OS(e=[]){if(Ca)return Ca;let t=xS(e);return Ca=t,$y(),FS(t),t}function FS(e){let t=e.get(Zs,null);sr(e,()=>{t?.forEach(n=>n())})}var kS=(()=>{class e{static __NG_ELEMENT_ID__=PS}return e})();function PS(e){return LS(se(),b(),(e&16)===16)}function LS(e,t,n){if(St(e)&&!n){let r=Ue(e.index,t);return new Xt(r,r)}else if(e.type&175){let r=t[fe];return new Xt(r,t)}return null}function Jv(e){L(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=OS(r),i=[Fd({}),{provide:Le,useExisting:Iv},ph,...n||[]],s=new wo({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return NS({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{L(9)}}var Ia=new WeakSet,qv="",wa=[];function Zv(e){return e.get(Fl,jg)}function eE(){let e=[{provide:Fl,useFactory:()=>{let t=!0;{let n=h(be);t=!!window._ejsas?.[n]}return t&&ze("NgEventReplay"),t}}];return e.push({provide:Ze,useValue:()=>{let t=h(Me),{injector:n}=t;if(!Ia.has(t)){let r=h(kl);if(Zv(n)){Wg();let o=n.get(be),i=zg(o,(s,a,c)=>{s.nodeType===Node.ELEMENT_NODE&&(Vg(s,a,c),Ug(s,r))});t.onDestroy(i)}}},multi:!0},{provide:Rr,useFactory:()=>{let t=h(Me),{injector:n}=t;return()=>{if(!Zv(n)||Ia.has(t))return;Ia.add(t);let r=n.get(be);t.onDestroy(()=>{Ia.delete(t),Wd(r)}),t.whenStable().then(()=>{if(t.destroyed)return;let o=n.get($g);jS(o,n);let i=n.get(kl);i.get(qv)?.forEach(Hg),i.delete(qv);let s=o.instance;Jg(n)?t.onDestroy(()=>s.cleanUp()):s.cleanUp()})}},multi:!0}),e}var jS=(e,t)=>{let n=t.get(be),r=window._ejsas[n],o=e.instance=new Hv(new Da(r.c));for(let a of r.et)o.addEvent(a);for(let a of r.etc)o.addEvent(a);let i=$v(n);o.replayEarlyEventInfos(i),Wd(n);let s=new _a(a=>{BS(t,a,a.currentTarget)});Uv(o,s)};function BS(e,t,n){let r=(n&&n.getAttribute(No))??"";/d\d+/.test(r)?VS(r,e,t,n):t.eventPhase===Gd.REPLAY&&Pl(t,n)}function VS(e,t,n,r){wa.push({event:n,currentTarget:r}),Wy(t,e,US)}function US(e){let t=[...wa],n=new Set(e);wa=[];for(let{event:r,currentTarget:o}of t){let i=o.getAttribute(No);n.has(i)?Pl(r,o):wa.push({event:r,currentTarget:o})}}var Yv=!1;function HS(){Yv||(Yv=!0,Kg(),Ky(),fv(),Qy(),ky(),yy(),ty(),Om())}function $S(e){return e.whenStable()}function tE(){let e=[{provide:So,useFactory:()=>{let t=!0;return t=!!h($n,{optional:!0})?.get(Ll,null),t&&ze("NgHydration"),t}},{provide:Ze,useValue:()=>{Qm(!1),h(So)&&(nm(wr()),HS())},multi:!0}];return e.push({provide:Ol,useFactory:()=>h(So)},{provide:Rr,useFactory:()=>{if(h(So)){let t=h(Me);return()=>{$S(t).then(()=>{t.destroyed||ad(t)})}}return()=>{}},multi:!0}),ut(e)}function Uo(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function AH(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var qd=Symbol("NOT_SET"),nE=new Set,zS=te(B({},Jr),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,value:qd,cleanup:null,consumerMarkedDirty(){if(this.sequence.impl.executing){if(this.sequence.lastPhase===null||this.sequence.lastPhase<this.phase)return;this.sequence.erroredOrDestroyed=!0}this.sequence.scheduler.notify(7)},phaseFn(e){if(this.sequence.lastPhase=this.phase,!this.dirty)return this.signal;if(this.dirty=!1,this.value!==qd&&!Dn(this))return this.signal;try{for(let o of this.cleanup??nE)o()}finally{this.cleanup?.clear()}let t=[];e!==void 0&&t.push(e),t.push(this.registerCleanupFn);let n=Ct(this),r;try{r=this.userFn.apply(null,t)}finally{Ht(this,n)}return(this.value===qd||!this.equal(this.value,r))&&(this.value=r,this.version++),this.signal}}),Yd=class extends bo{scheduler;lastPhase=null;nodes=[void 0,void 0,void 0,void 0];constructor(t,n,r,o,i,s=null){super(t,[void 0,void 0,void 0,void 0],r,!1,i,s),this.scheduler=o;for(let a of Ed){let c=n[a];if(c===void 0)continue;let u=Object.create(zS);u.sequence=this,u.phase=a,u.userFn=c,u.dirty=!0,u.signal=()=>(En(u),u.value),u.signal[re]=u,u.registerCleanupFn=l=>(u.cleanup??=new Set).add(l),this.nodes[a]=u,this.hooks[a]=l=>u.phaseFn(l)}}afterRun(){super.afterRun(),this.lastPhase=null}destroy(){super.destroy();for(let t of this.nodes)for(let n of t?.cleanup??nE)n()}};function RH(e,t){let n=t?.injector??h(oe),r=n.get(Le),o=n.get(pa),i=n.get(en,null,{optional:!0});o.impl??=n.get(Dd);let s=e;typeof s=="function"&&(s={mixedReadWrite:e});let a=n.get(xn,null,{optional:!0}),c=new Yd(o.impl,[s.earlyRead,s.write,s.mixedReadWrite,s.read],a?.view,r,n.get(Qe),i?.snapshot(null));return o.impl.register(c),c}function rE(e,t){let n=ct(e),r=t.elementInjector||ir();return new Vn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}var sE=null;function kt(){return sE}function Kd(e){sE??=e}var Ho=class{},Qd=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>h(aE),providedIn:"platform"})}return e})();var aE=(()=>{class e extends Qd{_location;_history;_doc=h(O);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return kt().getBaseHref(this._doc)}onPopState(n){let r=kt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=kt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function cE(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function oE(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function nn(e){return e&&e[0]!=="?"?`?${e}`:e}var Ta=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>h(lE),providedIn:"root"})}return e})(),uE=new E(""),lE=(()=>{class e extends Ta{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??h(O).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return cE(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+nn(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+nn(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+nn(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(Qd),C(uE,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dE=(()=>{class e{_subject=new $;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=qS(oE(iE(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+nn(r))}normalize(n){return e.stripTrailingSlash(WS(this._basePath,iE(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nn(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=nn;static joinWithSlash=cE;static stripTrailingSlash=oE;static \u0275fac=function(r){return new(r||e)(C(Ta))};static \u0275prov=D({token:e,factory:()=>GS(),providedIn:"root"})}return e})();function GS(){return new dE(C(Ta))}function WS(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function iE(e){return e.replace(/\/index.html$/,"")}function qS(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var me=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(me||{}),V=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(V||{}),Se=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Se||{}),Lt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function hE(e){return Pe(e)[Q.LocaleId]}function gE(e,t,n){let r=Pe(e),o=[r[Q.DayPeriodsFormat],r[Q.DayPeriodsStandalone]],i=Ge(o,t);return Ge(i,n)}function mE(e,t,n){let r=Pe(e),o=[r[Q.DaysFormat],r[Q.DaysStandalone]],i=Ge(o,t);return Ge(i,n)}function yE(e,t,n){let r=Pe(e),o=[r[Q.MonthsFormat],r[Q.MonthsStandalone]],i=Ge(o,t);return Ge(i,n)}function vE(e,t){let r=Pe(e)[Q.Eras];return Ge(r,t)}function $o(e,t){let n=Pe(e);return Ge(n[Q.DateFormat],t)}function zo(e,t){let n=Pe(e);return Ge(n[Q.TimeFormat],t)}function Go(e,t){let r=Pe(e)[Q.DateTimeFormat];return Ge(r,t)}function Wo(e,t){let n=Pe(e),r=n[Q.NumberSymbols][t];if(typeof r>"u"){if(t===Lt.CurrencyDecimal)return n[Q.NumberSymbols][Lt.Decimal];if(t===Lt.CurrencyGroup)return n[Q.NumberSymbols][Lt.Group]}return r}function EE(e){if(!e[Q.ExtraData])throw new _(2303,!1)}function DE(e){let t=Pe(e);return EE(t),(t[Q.ExtraData][2]||[]).map(r=>typeof r=="string"?Xd(r):[Xd(r[0]),Xd(r[1])])}function _E(e,t,n){let r=Pe(e);EE(r);let o=[r[Q.ExtraData][0],r[Q.ExtraData][1]],i=Ge(o,t)||[];return Ge(i,n)||[]}function Ge(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new _(2304,!1)}function Xd(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var ZS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ma={},YS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function IE(e,t,n,r){let o=oN(e);t=Pt(n,t)||t;let s=[],a;for(;t;)if(a=YS.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=wE(r,c),o=rN(o,r));let u="";return s.forEach(l=>{let d=tN(l);u+=d?d(o,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function xa(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Pt(e,t){let n=hE(e);if(Ma[n]??={},Ma[n][t])return Ma[n][t];let r="";switch(t){case"shortDate":r=$o(e,Se.Short);break;case"mediumDate":r=$o(e,Se.Medium);break;case"longDate":r=$o(e,Se.Long);break;case"fullDate":r=$o(e,Se.Full);break;case"shortTime":r=zo(e,Se.Short);break;case"mediumTime":r=zo(e,Se.Medium);break;case"longTime":r=zo(e,Se.Long);break;case"fullTime":r=zo(e,Se.Full);break;case"short":let o=Pt(e,"shortTime"),i=Pt(e,"shortDate");r=Sa(Go(e,Se.Short),[o,i]);break;case"medium":let s=Pt(e,"mediumTime"),a=Pt(e,"mediumDate");r=Sa(Go(e,Se.Medium),[s,a]);break;case"long":let c=Pt(e,"longTime"),u=Pt(e,"longDate");r=Sa(Go(e,Se.Long),[c,u]);break;case"full":let l=Pt(e,"fullTime"),d=Pt(e,"fullDate");r=Sa(Go(e,Se.Full),[l,d]);break}return r&&(Ma[n][t]=r),r}function Sa(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function nt(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function KS(e,t){return nt(e,3).substring(0,t)}function ee(e,t,n=0,r=!1,o=!1){return function(i,s){let a=QS(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return KS(a,t);let c=Wo(s,Lt.MinusSign);return nt(a,t,c,r,o)}}function QS(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new _(2301,!1)}}function G(e,t,n=me.Format,r=!1){return function(o,i){return XS(o,i,e,t,n,r)}}function XS(e,t,n,r,o,i){switch(n){case 2:return yE(t,o,r)[e.getMonth()];case 1:return mE(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=DE(t),l=_E(t,o,r),d=u.findIndex(p=>{if(Array.isArray(p)){let[f,g]=p,y=s>=f.hours&&a>=f.minutes,m=s<g.hours||s===g.hours&&a<g.minutes;if(f.hours<g.hours){if(y&&m)return!0}else if(y||m)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return gE(t,o,r)[s<12?0:1];case 3:return vE(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new _(2302,!1)}}function Na(e){return function(t,n,r){let o=-1*r,i=Wo(n,Lt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+nt(s,2,i)+nt(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+nt(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+nt(s,2,i)+":"+nt(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+nt(s,2,i)+":"+nt(Math.abs(o%60),2,i);default:throw new _(2302,!1)}}}var JS=0,Ra=4;function eN(e){let t=xa(e,JS,1).getDay();return xa(e,0,1+(t<=Ra?Ra:Ra+7)-t)}function CE(e){let t=e.getDay(),n=t===0?-3:Ra-t;return xa(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Jd(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=CE(n),s=eN(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return nt(o,e,Wo(r,Lt.MinusSign))}}function Aa(e,t=!1){return function(n,r){let i=CE(n).getFullYear();return nt(i,e,Wo(r,Lt.MinusSign),t)}}var ef={};function tN(e){if(ef[e])return ef[e];let t;switch(e){case"G":case"GG":case"GGG":t=G(3,V.Abbreviated);break;case"GGGG":t=G(3,V.Wide);break;case"GGGGG":t=G(3,V.Narrow);break;case"y":t=ee(0,1,0,!1,!0);break;case"yy":t=ee(0,2,0,!0,!0);break;case"yyy":t=ee(0,3,0,!1,!0);break;case"yyyy":t=ee(0,4,0,!1,!0);break;case"Y":t=Aa(1);break;case"YY":t=Aa(2,!0);break;case"YYY":t=Aa(3);break;case"YYYY":t=Aa(4);break;case"M":case"L":t=ee(1,1,1);break;case"MM":case"LL":t=ee(1,2,1);break;case"MMM":t=G(2,V.Abbreviated);break;case"MMMM":t=G(2,V.Wide);break;case"MMMMM":t=G(2,V.Narrow);break;case"LLL":t=G(2,V.Abbreviated,me.Standalone);break;case"LLLL":t=G(2,V.Wide,me.Standalone);break;case"LLLLL":t=G(2,V.Narrow,me.Standalone);break;case"w":t=Jd(1);break;case"ww":t=Jd(2);break;case"W":t=Jd(1,!0);break;case"d":t=ee(2,1);break;case"dd":t=ee(2,2);break;case"c":case"cc":t=ee(7,1);break;case"ccc":t=G(1,V.Abbreviated,me.Standalone);break;case"cccc":t=G(1,V.Wide,me.Standalone);break;case"ccccc":t=G(1,V.Narrow,me.Standalone);break;case"cccccc":t=G(1,V.Short,me.Standalone);break;case"E":case"EE":case"EEE":t=G(1,V.Abbreviated);break;case"EEEE":t=G(1,V.Wide);break;case"EEEEE":t=G(1,V.Narrow);break;case"EEEEEE":t=G(1,V.Short);break;case"a":case"aa":case"aaa":t=G(0,V.Abbreviated);break;case"aaaa":t=G(0,V.Wide);break;case"aaaaa":t=G(0,V.Narrow);break;case"b":case"bb":case"bbb":t=G(0,V.Abbreviated,me.Standalone,!0);break;case"bbbb":t=G(0,V.Wide,me.Standalone,!0);break;case"bbbbb":t=G(0,V.Narrow,me.Standalone,!0);break;case"B":case"BB":case"BBB":t=G(0,V.Abbreviated,me.Format,!0);break;case"BBBB":t=G(0,V.Wide,me.Format,!0);break;case"BBBBB":t=G(0,V.Narrow,me.Format,!0);break;case"h":t=ee(3,1,-12);break;case"hh":t=ee(3,2,-12);break;case"H":t=ee(3,1);break;case"HH":t=ee(3,2);break;case"m":t=ee(4,1);break;case"mm":t=ee(4,2);break;case"s":t=ee(5,1);break;case"ss":t=ee(5,2);break;case"S":t=ee(6,1);break;case"SS":t=ee(6,2);break;case"SSS":t=ee(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Na(0);break;case"ZZZZZ":t=Na(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Na(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Na(2);break;default:return null}return ef[e]=t,t}function wE(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function nN(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function rN(e,t,n){let o=e.getTimezoneOffset(),i=wE(t,o);return nN(e,-1*(i-o))}function oN(e){if(fE(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return xa(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(ZS))return iN(r)}let t=new Date(e);if(!fE(t))throw new _(2302,!1);return t}function iN(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,u),t}function fE(e){return e instanceof Date&&!isNaN(e.valueOf())}var tf=/\s+/,pE=[],sN=(()=>{class e{_ngEl;_renderer;initialClasses=pE;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(tf):pE}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(tf):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(tf).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)($e(he),$e(cd))};static \u0275dir=Et({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var aN=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)($e(Nr))};static \u0275dir=Et({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[qs]})}return e})();function cN(e,t){return new _(2100,!1)}var uN="mediumDate",bE=new E(""),TE=new E(""),lN=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??uN,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return IE(n,s,i||this.locale,a)}catch(s){throw cN(e,s.message)}}static \u0275fac=function(r){return new(r||e)($e(Bo,16),$e(bE,24),$e(TE,24))};static \u0275pipe=gd({name:"date",type:e,pure:!0})}return e})();var ME=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({})}return e})();function qo(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Gn=class{};var nf="browser";function SE(e){return e===nf}var Fa=new E(""),cf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new _(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(Fa),C(k))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Zo=class{_doc;constructor(t){this._doc=t}manager},rf="ng-app-id";function NE(e){for(let t of e)t.remove()}function AE(e,t){let n=t.createElement("style");return n.textContent=e,n}function fN(e,t,n,r){let o=e.head?.querySelectorAll(`style[${rf}="${t}"],link[${rf}="${t}"]`);if(o)for(let i of o)i.removeAttribute(rf),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function sf(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var uf=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,fN(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,AE);r?.forEach(o=>this.addUsage(o,this.external,sf))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(NE(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])NE(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,AE(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,sf(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(O),C(be),C(br,8),C(Jt))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),of={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},lf=/%COMP%/g;var xE="%COMP%",pN=`_nghost-${xE}`,hN=`_ngcontent-${xE}`,gN=!0,mN=new E("",{providedIn:"root",factory:()=>gN});function yN(e){return hN.replace(lf,e)}function vN(e){return pN.replace(lf,e)}function OE(e,t){return t.map(n=>n.replace(lf,e))}var df=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Yo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof Oa?o.applyToHost(n):o instanceof Ko&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case Rt.Emulated:i=new Oa(c,u,r,this.appId,l,s,a,d,p);break;case Rt.ShadowDom:return new af(c,u,n,r,s,a,this.nonce,d,p);default:i=new Ko(c,u,r,l,s,a,d,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(cf),C(uf),C(be),C(mN),C(O),C(Jt),C(k),C(br),C(en,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Yo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(of[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(RE(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(RE(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new _(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=of[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=of[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(yt.DashCase|yt.Important)?t.style.setProperty(n,r,o&yt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&yt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=kt().getGlobalEventTarget(this.doc,t),!t))throw new _(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function RE(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var af=class extends Yo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=OE(o.id,l);for(let p of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let p of d){let f=sf(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ko=class extends Yo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?OE(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Oa=class extends Ko{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=yN(l),this.hostAttr=vN(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var ka=class e extends Ho{supportsDOMEvents=!0;static makeCurrent(){Kd(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=EN();return n==null?null:DN(n)}resetBaseElement(){Qo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return qo(document.cookie,t)}},Qo=null;function EN(){return Qo=Qo||document.head.querySelector("base"),Qo?Qo.getAttribute("href"):null}function DN(e){return new URL(e,document.baseURI).pathname}var _N=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),kE=(()=>{class e extends Zo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),FE=["alt","control","meta","shift"],IN={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},CN={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},PE=(()=>{class e extends Zo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>kt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),FE.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=IN[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),FE.forEach(s=>{if(s!==o){let a=CN[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function wN(e,t){return Jv(B({rootComponent:e},bN(t)))}function bN(e){return{appProviders:[...AN,...e?.providers??[]],platformProviders:NN}}function TN(){ka.makeCurrent()}function MN(){return new le}function SN(){return Al(document),document}var NN=[{provide:Jt,useValue:nf},{provide:Zs,useValue:TN,multi:!0},{provide:O,useFactory:SN}];var AN=[{provide:ao,useValue:"root"},{provide:le,useFactory:MN},{provide:Fa,useClass:kE,multi:!0,deps:[O]},{provide:Fa,useClass:PE,multi:!0,deps:[O]},df,uf,cf,{provide:Ot,useExisting:df},{provide:Gn,useClass:_N},[]];var Or=class{},Fr=class{},rt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var ja=class{encodeKey(t){return LE(t)}encodeValue(t){return LE(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function RN(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var xN=/%(\d[a-f0-9])/gi,ON={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function LE(e){return encodeURIComponent(e).replace(xN,(t,n)=>ON[n]??t)}function Pa(e){return`${e}`}var jt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new ja,t.fromString){if(t.fromObject)throw new _(2805,!1);this.map=RN(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Pa):[Pa(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Pa(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Pa(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ba=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function FN(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function jE(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function BE(e){return typeof Blob<"u"&&e instanceof Blob}function VE(e){return typeof FormData<"u"&&e instanceof FormData}function kN(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Xo="Content-Type",Va="Accept",mf="X-Request-URL",HE="text/plain",$E="application/json",zE=`${$E}, ${HE}, */*`,xr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;credentials;keepalive=!1;cache;priority;mode;redirect;responseType="json";method;params;urlWithParams;transferCache;timeout;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(FN(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i){if(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),i.priority&&(this.priority=i.priority),i.cache&&(this.cache=i.cache),i.credentials&&(this.credentials=i.credentials),typeof i.timeout=="number"){if(i.timeout<1||!Number.isInteger(i.timeout))throw new Error("");this.timeout=i.timeout}i.mode&&(this.mode=i.mode),i.redirect&&(this.redirect=i.redirect),this.transferCache=i.transferCache}if(this.headers??=new rt,this.context??=new Ba,!this.params)this.params=new jt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||jE(this.body)||BE(this.body)||VE(this.body)||kN(this.body)?this.body:this.body instanceof jt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||VE(this.body)?null:BE(this.body)?this.body.type||null:jE(this.body)?null:typeof this.body=="string"?HE:this.body instanceof jt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?$E:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.keepalive??this.keepalive,s=t.priority||this.priority,a=t.cache||this.cache,c=t.mode||this.mode,u=t.redirect||this.redirect,l=t.credentials||this.credentials,d=t.transferCache??this.transferCache,p=t.timeout??this.timeout,f=t.body!==void 0?t.body:this.body,g=t.withCredentials??this.withCredentials,y=t.reportProgress??this.reportProgress,m=t.headers||this.headers,v=t.params||this.params,U=t.context??this.context;return t.setHeaders!==void 0&&(m=Object.keys(t.setHeaders).reduce((We,H)=>We.set(H,t.setHeaders[H]),m)),t.setParams&&(v=Object.keys(t.setParams).reduce((We,H)=>We.set(H,t.setParams[H]),v)),new e(n,r,f,{params:v,headers:m,context:U,reportProgress:y,responseType:o,withCredentials:g,transferCache:d,keepalive:i,cache:a,priority:s,timeout:p,mode:c,redirect:u,credentials:l})}},Bt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Bt||{}),kr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new rt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Jo=class e extends kr{constructor(t={}){super(t)}type=Bt.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Wn=class e extends kr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Bt.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Dt=class extends kr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},GE=200,PN=204;function ff(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive,priority:e.priority,cache:e.cache,mode:e.mode,redirect:e.redirect}}var Ha=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof xr)i=n;else{let c;o.headers instanceof rt?c=o.headers:c=new rt(o.headers);let u;o.params&&(o.params instanceof jt?u=o.params:u=new jt({fromObject:o.params})),i=new xr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive,priority:o.priority,cache:o.cache,mode:o.mode,redirect:o.redirect,credentials:o.credentials})}let s=we(i).pipe(pc(c=>this.handler.handle(c)));if(n instanceof xr||o.observe==="events")return s;let a=s.pipe(Ne(c=>c instanceof Wn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(W(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new _(2806,!1);return c.body}));case"blob":return a.pipe(W(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new _(2807,!1);return c.body}));case"text":return a.pipe(W(c=>{if(c.body!==null&&typeof c.body!="string")throw new _(2808,!1);return c.body}));case"json":default:return a.pipe(W(c=>c.body))}case"response":return a;default:throw new _(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new jt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,ff(o,r))}post(n,r,o={}){return this.request("POST",n,ff(o,r))}put(n,r,o={}){return this.request("PUT",n,ff(o,r))}static \u0275fac=function(r){return new(r||e)(C(Or))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),LN=/^\)\]\}',?\n/;function UE(e){if(e.url)return e.url;let t=mf.toLocaleLowerCase();return e.headers.get(t)}var WE=new E(""),La=(()=>{class e{fetchImpl=h(pf,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=h(k);destroyRef=h(Qe);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new x(r=>{let o=new AbortController;this.doRequest(n,o.signal,r).then(hf,s=>r.error(new Dt({error:s})));let i;return n.timeout&&(i=this.ngZone.runOutsideAngular(()=>setTimeout(()=>{o.signal.aborted||o.abort(new DOMException("signal timed out","TimeoutError"))},n.timeout))),()=>{i!==void 0&&clearTimeout(i),o.abort()}})}doRequest(n,r,o){return ot(this,null,function*(){let i=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,B({signal:r},i)));jN(f),o.next({type:Bt.Sent}),s=yield f}catch(f){o.error(new Dt({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new rt(s.headers),c=s.statusText,u=UE(s)??n.urlWithParams,l=s.status,d=null;if(n.reportProgress&&o.next(new Jo({headers:a,status:l,statusText:c,url:u})),s.body){let f=s.headers.get("content-length"),g=[],y=s.body.getReader(),m=0,v,U,We=typeof Zone<"u"&&Zone.current,H=!1;if(yield this.ngZone.runOutsideAngular(()=>ot(this,null,function*(){for(;;){if(this.destroyed){yield y.cancel(),H=!0;break}let{done:sn,value:Br}=yield y.read();if(sn)break;if(g.push(Br),m+=Br.length,n.reportProgress){U=n.responseType==="text"?(U??"")+(v??=new TextDecoder).decode(Br,{stream:!0}):void 0;let Pf=()=>o.next({type:Bt.DownloadProgress,total:f?+f:void 0,loaded:m,partialText:U});We?We.run(Pf):Pf()}}})),H){o.complete();return}let on=this.concatChunks(g,m);try{let sn=s.headers.get(Xo)??"";d=this.parseBody(n,on,sn)}catch(sn){o.error(new Dt({error:sn,headers:new rt(s.headers),status:s.status,statusText:s.statusText,url:UE(s)??n.urlWithParams}));return}}l===0&&(l=d?GE:0),l>=200&&l<300?(o.next(new Wn({body:d,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new Dt({error:d,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(LN,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o;if(o=n.credentials,n.withCredentials&&(o="include"),n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(Va)||(r[Va]=zE),!n.headers.has(Xo)){let i=n.detectContentTypeHeader();i!==null&&(r[Xo]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o,keepalive:n.keepalive,cache:n.cache,priority:n.priority,mode:n.mode,redirect:n.redirect}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),pf=class{};function hf(){}function jN(e){e.then(hf,hf)}function BN(e,t){return t(e)}function VN(e,t,n){return(r,o)=>sr(n,()=>t(r,i=>e(i,o)))}var qE=new E(""),yf=new E(""),ZE=new E("",{providedIn:"root",factory:()=>!0});var Ua=(()=>{class e extends Or{backend;injector;chain=null;pendingTasks=h(pr);contributeToStability=h(ZE);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(qE),...this.injector.get(yf,[])]));this.chain=r.reduceRight((o,i)=>VN(o,i,this.injector),BN)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Wr(r))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(C(Fr),C(ue))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var UN=/^\)\]\}',?\n/,HN=RegExp(`^${mf}:`,"m");function $N(e){return"responseURL"in e&&e.responseURL?e.responseURL:HN.test(e.getAllResponseHeaders())?e.getResponseHeader(mf):null}var gf=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new _(-2800,!1);let r=this.xhrFactory;return we(null).pipe(vc(()=>new x(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,v)=>s.setRequestHeader(m,v.join(","))),n.headers.has(Va)||s.setRequestHeader(Va,zE),!n.headers.has(Xo)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(Xo,m)}if(n.timeout&&(s.timeout=n.timeout),n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",v=new rt(s.getAllResponseHeaders()),U=$N(s)||n.url;return c=new Jo({headers:v,status:s.status,statusText:m,url:U}),c},l=()=>{let{headers:m,status:v,statusText:U,url:We}=u(),H=null;v!==PN&&(H=typeof s.response>"u"?s.responseText:s.response),v===0&&(v=H?GE:0);let on=v>=200&&v<300;if(n.responseType==="json"&&typeof H=="string"){let sn=H;H=H.replace(UN,"");try{H=H!==""?JSON.parse(H):null}catch(Br){H=sn,on&&(on=!1,H={error:Br,text:H})}}on?(i.next(new Wn({body:H,headers:m,status:v,statusText:U,url:We||void 0})),i.complete()):i.error(new Dt({error:H,headers:m,status:v,statusText:U,url:We||void 0}))},d=m=>{let{url:v}=u(),U=new Dt({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:v||void 0});i.error(U)},p=d;n.timeout&&(p=m=>{let{url:v}=u(),U=new Dt({error:new DOMException("Request timed out","TimeoutError"),status:s.status||0,statusText:s.statusText||"Request timeout",url:v||void 0});i.error(U)});let f=!1,g=m=>{f||(i.next(u()),f=!0);let v={type:Bt.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(v.total=m.total),n.responseType==="text"&&s.responseText&&(v.partialText=s.responseText),i.next(v)},y=m=>{let v={type:Bt.UploadProgress,loaded:m.loaded};m.lengthComputable&&(v.total=m.total),i.next(v)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",p),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",g),a!==null&&s.upload&&s.upload.addEventListener("progress",y)),s.send(a),i.next({type:Bt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",p),n.reportProgress&&(s.removeEventListener("progress",g),a!==null&&s.upload&&s.upload.removeEventListener("progress",y)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(C(Gn))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),YE=new E(""),zN="XSRF-TOKEN",GN=new E("",{providedIn:"root",factory:()=>zN}),WN="X-XSRF-TOKEN",qN=new E("",{providedIn:"root",factory:()=>WN}),ei=class{},ZN=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=qo(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(C(O),C(GN))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function YN(e,t){let n=e.url.toLowerCase();if(!h(YE)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=h(ei).getToken(),o=h(qN);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var vf=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(vf||{});function KN(e,t){return{\u0275kind:e,\u0275providers:t}}function QN(...e){let t=[Ha,gf,Ua,{provide:Or,useExisting:Ua},{provide:Fr,useFactory:()=>h(WE,{optional:!0})??h(gf)},{provide:qE,useValue:YN,multi:!0},{provide:YE,useValue:!0},{provide:ei,useClass:ZN}];for(let n of e)t.push(...n.\u0275providers);return ut(t)}function XN(){return KN(vf.Fetch,[La,{provide:WE,useExisting:La},{provide:Fr,useExisting:La}])}var JN=new E(""),eA="b",tA="h",nA="s",rA="st",oA="u",iA="rt",Ef=new E(""),sA=["GET","HEAD"];function aA(e,t){let f=h(Ef),{isCacheActive:n}=f,r=Vf(f,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||o===!1||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!sA.includes(i)||!r.includeRequestsWithAuthHeaders&&cA(e)||r.filter?.(e)===!1)return t(e);let s=h($n);if(h(JN,{optional:!0}))throw new _(2803,!1);let c=e.url,u=uA(e,c),l=s.get(u,null),d=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(d=o.includeHeaders),l){let{[eA]:g,[iA]:y,[tA]:m,[nA]:v,[rA]:U,[oA]:We}=l,H=g;switch(y){case"arraybuffer":H=new TextEncoder().encode(g).buffer;break;case"blob":H=new Blob([g]);break}let on=new rt(m);return we(new Wn({body:H,headers:on,status:v,statusText:U,url:We}))}return t(e)}function cA(e){return e.headers.has("authorization")||e.headers.has("proxy-authorization")}function KE(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function uA(e,t){let{params:n,method:r,responseType:o}=e,i=KE(n),s=e.serializeBody();s instanceof URLSearchParams?s=KE(s):typeof s!="string"&&(s="");let a=[r,o,t,s,i].join("|"),c=lA(a);return c}function lA(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=2147483648,t.toString()}function QE(e){return[{provide:Ef,useFactory:()=>(ze("NgHttpTransferCache"),B({isCacheActive:!0},e))},{provide:yf,useValue:aA,multi:!0},{provide:Rr,multi:!0,useFactory:()=>{let t=h(Me),n=h(Ef);return()=>{t.whenStable().then(()=>{n.isCacheActive=!1})}}}]}var u8=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Df=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=C(fA),o},providedIn:"root"})}return e})(),fA=(()=>{class e extends Df{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case ae.NONE:return r;case ae.HTML:return vt(r,"HTML")?He(r):ea(this._doc,String(r)).toString();case ae.STYLE:return vt(r,"Style")?He(r):r;case ae.SCRIPT:if(vt(r,"Script"))return He(r);throw new _(5200,!1);case ae.URL:return vt(r,"URL")?He(r):Ro(String(r));case ae.RESOURCE_URL:if(vt(r,"ResourceURL"))return He(r);throw new _(5201,!1);default:throw new _(5202,!1)}}bypassSecurityTrustHtml(n){return Bl(n)}bypassSecurityTrustStyle(n){return Vl(n)}bypassSecurityTrustScript(n){return Ul(n)}bypassSecurityTrustUrl(n){return Hl(n)}bypassSecurityTrustResourceUrl(n){return $l(n)}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$a=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e[e.I18nSupport=2]="I18nSupport",e[e.EventReplay=3]="EventReplay",e[e.IncrementalHydration=4]="IncrementalHydration",e}($a||{});function pA(e,t=[],n={}){return{\u0275kind:e,\u0275providers:t}}function l8(){return pA($a.EventReplay,eE())}function d8(...e){let t=[],n=new Set;for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);let r=n.has($a.HttpTransferCacheOptions);return ut([[],tE(),n.has($a.NoHttpTransferCache)||r?[]:QE({}),t])}var za;function gA(){if(za===void 0&&(za=null,typeof window<"u")){let e=window;e.trustedTypes!==void 0&&(za=e.trustedTypes.createPolicy("angular#components",{createHTML:t=>t}))}return za}function ti(e){return gA()?.createHTML(e)||e}function XE(e){return Error(`Unable to find icon with the name "${e}"`)}function mA(){return Error("Could not find HttpClient for use with Angular Material icons. Please add provideHttpClient() to your providers.")}function JE(e){return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL via Angular's DomSanitizer. Attempted URL was "${e}".`)}function eD(e){return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by Angular's DomSanitizer. Attempted literal was "${e}".`)}var Vt=class{url;svgText;options;svgElement;constructor(t,n,r){this.url=t,this.svgText=n,this.options=r}},nD=(()=>{class e{_httpClient;_sanitizer;_errorHandler;_document;_svgIconConfigs=new Map;_iconSetConfigs=new Map;_cachedIconsByUrl=new Map;_inProgressUrlFetches=new Map;_fontCssClassesByAlias=new Map;_resolvers=[];_defaultFontSetClass=["material-icons","mat-ligature-font"];constructor(n,r,o,i){this._httpClient=n,this._sanitizer=r,this._errorHandler=i,this._document=o}addSvgIcon(n,r,o){return this.addSvgIconInNamespace("",n,r,o)}addSvgIconLiteral(n,r,o){return this.addSvgIconLiteralInNamespace("",n,r,o)}addSvgIconInNamespace(n,r,o,i){return this._addSvgIconConfig(n,r,new Vt(o,null,i))}addSvgIconResolver(n){return this._resolvers.push(n),this}addSvgIconLiteralInNamespace(n,r,o,i){let s=this._sanitizer.sanitize(ae.HTML,o);if(!s)throw eD(o);let a=ti(s);return this._addSvgIconConfig(n,r,new Vt("",a,i))}addSvgIconSet(n,r){return this.addSvgIconSetInNamespace("",n,r)}addSvgIconSetLiteral(n,r){return this.addSvgIconSetLiteralInNamespace("",n,r)}addSvgIconSetInNamespace(n,r,o){return this._addSvgIconSetConfig(n,new Vt(r,null,o))}addSvgIconSetLiteralInNamespace(n,r,o){let i=this._sanitizer.sanitize(ae.HTML,r);if(!i)throw eD(r);let s=ti(i);return this._addSvgIconSetConfig(n,new Vt("",s,o))}registerFontClassAlias(n,r=n){return this._fontCssClassesByAlias.set(n,r),this}classNameForFontAlias(n){return this._fontCssClassesByAlias.get(n)||n}setDefaultFontSetClass(...n){return this._defaultFontSetClass=n,this}getDefaultFontSetClass(){return this._defaultFontSetClass}getSvgIconFromUrl(n){let r=this._sanitizer.sanitize(ae.RESOURCE_URL,n);if(!r)throw JE(n);let o=this._cachedIconsByUrl.get(r);return o?we(Ga(o)):this._loadSvgIconFromConfig(new Vt(n,null)).pipe(vn(i=>this._cachedIconsByUrl.set(r,i)),W(i=>Ga(i)))}getNamedSvgIcon(n,r=""){let o=tD(r,n),i=this._svgIconConfigs.get(o);if(i)return this._getSvgFromConfig(i);if(i=this._getIconConfigFromResolvers(r,n),i)return this._svgIconConfigs.set(o,i),this._getSvgFromConfig(i);let s=this._iconSetConfigs.get(r);return s?this._getSvgFromIconSetConfigs(n,s):lc(XE(o))}ngOnDestroy(){this._resolvers=[],this._svgIconConfigs.clear(),this._iconSetConfigs.clear(),this._cachedIconsByUrl.clear()}_getSvgFromConfig(n){return n.svgText?we(Ga(this._svgElementFromConfig(n))):this._loadSvgIconFromConfig(n).pipe(W(r=>Ga(r)))}_getSvgFromIconSetConfigs(n,r){let o=this._extractIconWithNameFromAnySet(n,r);if(o)return we(o);let i=r.filter(s=>!s.svgText).map(s=>this._loadSvgIconSetFromConfig(s).pipe(xi(a=>{let u=`Loading icon set URL: ${this._sanitizer.sanitize(ae.RESOURCE_URL,s.url)} failed: ${a.message}`;return this._errorHandler.handleError(new Error(u)),we(null)})));return fc(i).pipe(W(()=>{let s=this._extractIconWithNameFromAnySet(n,r);if(!s)throw XE(n);return s}))}_extractIconWithNameFromAnySet(n,r){for(let o=r.length-1;o>=0;o--){let i=r[o];if(i.svgText&&i.svgText.toString().indexOf(n)>-1){let s=this._svgElementFromConfig(i),a=this._extractSvgIconFromSet(s,n,i.options);if(a)return a}}return null}_loadSvgIconFromConfig(n){return this._fetchIcon(n).pipe(vn(r=>n.svgText=r),W(()=>this._svgElementFromConfig(n)))}_loadSvgIconSetFromConfig(n){return n.svgText?we(null):this._fetchIcon(n).pipe(vn(r=>n.svgText=r))}_extractSvgIconFromSet(n,r,o){let i=n.querySelector(`[id="${r}"]`);if(!i)return null;let s=i.cloneNode(!0);if(s.removeAttribute("id"),s.nodeName.toLowerCase()==="svg")return this._setSvgAttributes(s,o);if(s.nodeName.toLowerCase()==="symbol")return this._setSvgAttributes(this._toSvgElement(s),o);let a=this._svgElementFromString(ti("<svg></svg>"));return a.appendChild(s),this._setSvgAttributes(a,o)}_svgElementFromString(n){let r=this._document.createElement("DIV");r.innerHTML=n;let o=r.querySelector("svg");if(!o)throw Error("<svg> tag not found");return o}_toSvgElement(n){let r=this._svgElementFromString(ti("<svg></svg>")),o=n.attributes;for(let i=0;i<o.length;i++){let{name:s,value:a}=o[i];s!=="id"&&r.setAttribute(s,a)}for(let i=0;i<n.childNodes.length;i++)n.childNodes[i].nodeType===this._document.ELEMENT_NODE&&r.appendChild(n.childNodes[i].cloneNode(!0));return r}_setSvgAttributes(n,r){return n.setAttribute("fit",""),n.setAttribute("height","100%"),n.setAttribute("width","100%"),n.setAttribute("preserveAspectRatio","xMidYMid meet"),n.setAttribute("focusable","false"),r&&r.viewBox&&n.setAttribute("viewBox",r.viewBox),n}_fetchIcon(n){let{url:r,options:o}=n,i=o?.withCredentials??!1;if(!this._httpClient)throw mA();if(r==null)throw Error(`Cannot fetch icon from URL "${r}".`);let s=this._sanitizer.sanitize(ae.RESOURCE_URL,r);if(!s)throw JE(r);let a=this._inProgressUrlFetches.get(s);if(a)return a;let c=this._httpClient.get(s,{responseType:"text",withCredentials:i}).pipe(W(u=>ti(u)),Wr(()=>this._inProgressUrlFetches.delete(s)),qr());return this._inProgressUrlFetches.set(s,c),c}_addSvgIconConfig(n,r,o){return this._svgIconConfigs.set(tD(n,r),o),this}_addSvgIconSetConfig(n,r){let o=this._iconSetConfigs.get(n);return o?o.push(r):this._iconSetConfigs.set(n,[r]),this}_svgElementFromConfig(n){if(!n.svgElement){let r=this._svgElementFromString(n.svgText);this._setSvgAttributes(r,n.options),n.svgElement=r}return n.svgElement}_getIconConfigFromResolvers(n,r){for(let o=0;o<this._resolvers.length;o++){let i=this._resolvers[o](r,n);if(i)return yA(i)?new Vt(i.url,null,i.options):new Vt(i,null)}}static \u0275fac=function(r){return new(r||e)(C(Ha,8),C(Df),C(O,8),C(le))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ga(e){return e.cloneNode(!0)}function tD(e,t){return e+":"+t}function yA(e){return!!(e.url&&e.options)}function _f(e){return e.buttons===0||e.detail===0}function If(e){let t=e.touches&&e.touches[0]||e.changedTouches&&e.changedTouches[0];return!!t&&t.identifier===-1&&(t.radiusX==null||t.radiusX===1)&&(t.radiusY==null||t.radiusY===1)}var Cf;function vA(){if(Cf==null){let e=typeof document<"u"?document.head:null;Cf=!!(e&&(e.createShadowRoot||e.attachShadow))}return Cf}function rD(e){if(vA()){let t=e.getRootNode?e.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}function D8(){let e=typeof document<"u"&&document?document.activeElement:null;for(;e&&e.shadowRoot;){let t=e.shadowRoot.activeElement;if(t===e)break;e=t}return e}function Pr(e){return e.composedPath?e.composedPath()[0]:e.target}var wf;try{wf=typeof Intl<"u"&&Intl.v8BreakIterator}catch{wf=!1}var _t=(()=>{class e{_platformId=h(Jt);isBrowser=this._platformId?SE(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||wf)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ni;function EA(){if(ni==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>ni=!0}))}finally{ni=ni||!1}return ni}function oD(e){return EA()?e:!!e.capture}function iD(e,t=0){return DA(e)?Number(e):arguments.length===2?t:0}function DA(e){return!isNaN(parseFloat(e))&&!isNaN(Number(e))}function Lr(e){return e instanceof he?e.nativeElement:e}var sD=new E("cdk-input-modality-detector-options"),aD={ignoreKeys:[18,17,224,91,16]},cD=650,bf={passive:!0,capture:!0},uD=(()=>{class e{_platform=h(_t);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new dn(null);_options;_lastTouchMs=0;_onKeydown=n=>{this._options?.ignoreKeys?.some(r=>r===n.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=Pr(n))};_onMousedown=n=>{Date.now()-this._lastTouchMs<cD||(this._modality.next(_f(n)?"keyboard":"mouse"),this._mostRecentTarget=Pr(n))};_onTouchstart=n=>{if(If(n)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=Pr(n)};constructor(){let n=h(k),r=h(O),o=h(sD,{optional:!0});if(this._options=B(B({},aD),o),this.modalityDetected=this._modality.pipe(Zr(1)),this.modalityChanged=this.modalityDetected.pipe(hc()),this._platform.isBrowser){let i=h(Ot).createRenderer(null,null);this._listenerCleanups=n.runOutsideAngular(()=>[i.listen(r,"keydown",this._onKeydown,bf),i.listen(r,"mousedown",this._onMousedown,bf),i.listen(r,"touchstart",this._onTouchstart,bf)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(n=>n())}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ri=function(e){return e[e.IMMEDIATE=0]="IMMEDIATE",e[e.EVENTUAL=1]="EVENTUAL",e}(ri||{}),lD=new E("cdk-focus-monitor-default-options"),Wa=oD({passive:!0,capture:!0}),dD=(()=>{class e{_ngZone=h(k);_platform=h(_t);_inputModalityDetector=h(uD);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=h(O);_stopInputModalityDetector=new $;constructor(){let n=h(lD,{optional:!0});this._detectionMode=n?.detectionMode||ri.IMMEDIATE}_rootNodeFocusAndBlurListener=n=>{let r=Pr(n);for(let o=r;o;o=o.parentElement)n.type==="focus"?this._onFocus(n,o):this._onBlur(n,o)};monitor(n,r=!1){let o=Lr(n);if(!this._platform.isBrowser||o.nodeType!==1)return we();let i=rD(o)||this._document,s=this._elementInfo.get(o);if(s)return r&&(s.checkChildren=!0),s.subject;let a={checkChildren:r,subject:new $,rootNode:i};return this._elementInfo.set(o,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(n){let r=Lr(n),o=this._elementInfo.get(r);o&&(o.subject.complete(),this._setClasses(r),this._elementInfo.delete(r),this._removeGlobalListeners(o))}focusVia(n,r,o){let i=Lr(n),s=this._document.activeElement;i===s?this._getClosestElementsInfo(i).forEach(([a,c])=>this._originChanged(a,r,c)):(this._setOrigin(r),typeof i.focus=="function"&&i.focus(o))}ngOnDestroy(){this._elementInfo.forEach((n,r)=>this.stopMonitoring(r))}_getWindow(){return this._document.defaultView||window}_getFocusOrigin(n){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(n)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:n&&this._isLastInteractionFromInputLabel(n)?"mouse":"program"}_shouldBeAttributedToTouch(n){return this._detectionMode===ri.EVENTUAL||!!n?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(n,r){n.classList.toggle("cdk-focused",!!r),n.classList.toggle("cdk-touch-focused",r==="touch"),n.classList.toggle("cdk-keyboard-focused",r==="keyboard"),n.classList.toggle("cdk-mouse-focused",r==="mouse"),n.classList.toggle("cdk-program-focused",r==="program")}_setOrigin(n,r=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=n,this._originFromTouchInteraction=n==="touch"&&r,this._detectionMode===ri.IMMEDIATE){clearTimeout(this._originTimeoutId);let o=this._originFromTouchInteraction?cD:1;this._originTimeoutId=setTimeout(()=>this._origin=null,o)}})}_onFocus(n,r){let o=this._elementInfo.get(r),i=Pr(n);!o||!o.checkChildren&&r!==i||this._originChanged(r,this._getFocusOrigin(i),o)}_onBlur(n,r){let o=this._elementInfo.get(r);!o||o.checkChildren&&n.relatedTarget instanceof Node&&r.contains(n.relatedTarget)||(this._setClasses(r),this._emitOrigin(o,null))}_emitOrigin(n,r){n.subject.observers.length&&this._ngZone.run(()=>n.subject.next(r))}_registerGlobalListeners(n){if(!this._platform.isBrowser)return;let r=n.rootNode,o=this._rootNodeFocusListenerCount.get(r)||0;o||this._ngZone.runOutsideAngular(()=>{r.addEventListener("focus",this._rootNodeFocusAndBlurListener,Wa),r.addEventListener("blur",this._rootNodeFocusAndBlurListener,Wa)}),this._rootNodeFocusListenerCount.set(r,o+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Yr(this._stopInputModalityDetector)).subscribe(i=>{this._setOrigin(i,!0)}))}_removeGlobalListeners(n){let r=n.rootNode;if(this._rootNodeFocusListenerCount.has(r)){let o=this._rootNodeFocusListenerCount.get(r);o>1?this._rootNodeFocusListenerCount.set(r,o-1):(r.removeEventListener("focus",this._rootNodeFocusAndBlurListener,Wa),r.removeEventListener("blur",this._rootNodeFocusAndBlurListener,Wa),this._rootNodeFocusListenerCount.delete(r))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(n,r,o){this._setClasses(n,r),this._emitOrigin(o,r),this._lastFocusOrigin=r}_getClosestElementsInfo(n){let r=[];return this._elementInfo.forEach((o,i)=>{(i===n||o.checkChildren&&i.contains(n))&&r.push([i,o])}),r}_isLastInteractionFromInputLabel(n){let{_mostRecentTarget:r,mostRecentModality:o}=this._inputModalityDetector;if(o!=="mouse"||!r||r===n||n.nodeName!=="INPUT"&&n.nodeName!=="TEXTAREA"||n.disabled)return!1;let i=n.labels;if(i){for(let s=0;s<i.length;s++)if(i[s].contains(r))return!0}return!1}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_A=(()=>{class e{_elementRef=h(he);_focusMonitor=h(dD);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new Ie;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let n=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(n,n.nodeType===1&&n.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(r=>{this._focusOrigin=r,this.cdkFocusChange.emit(r)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Et({type:e,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return e})();var qa=new WeakMap,oi=(()=>{class e{_appRef;_injector=h(oe);_environmentInjector=h(ue);load(n){let r=this._appRef=this._appRef||this._injector.get(Me),o=qa.get(r);o||(o={loaders:new Set,refs:[]},qa.set(r,o),r.onDestroy(()=>{qa.get(r)?.refs.forEach(i=>i.destroy()),qa.delete(r)})),o.loaders.has(n)||(o.loaders.add(n),o.refs.push(rE(n,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Za=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Fo({type:e,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(r,o){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return e})();function Tf(e){return Array.isArray(e)?e:[e]}var fD=new Set,qn,IA=(()=>{class e{_platform=h(_t);_nonce=h(br,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):wA}matchMedia(n){return(this._platform.WEBKIT||this._platform.BLINK)&&CA(n,this._nonce),this._matchMedia(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function CA(e,t){if(!fD.has(e))try{qn||(qn=document.createElement("style"),t&&qn.setAttribute("nonce",t),qn.setAttribute("type","text/css"),document.head.appendChild(qn)),qn.sheet&&(qn.sheet.insertRule(`@media ${e} {body{ }}`,0),fD.add(e))}catch(n){console.error(n)}}function wA(e){return{matches:e==="all"||e==="",media:e,addListener:()=>{},removeListener:()=>{}}}var hD=(()=>{class e{_mediaMatcher=h(IA);_zone=h(k);_queries=new Map;_destroySubject=new $;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(n){return pD(Tf(n)).some(o=>this._registerQuery(o).mql.matches)}observe(n){let o=pD(Tf(n)).map(s=>this._registerQuery(s).observable),i=dc(o);return i=Jn(i.pipe(yn(1)),i.pipe(Zr(1),mn(0))),i.pipe(W(s=>{let a={matches:!1,breakpoints:{}};return s.forEach(({matches:c,query:u})=>{a.matches=a.matches||c,a.breakpoints[u]=c}),a}))}_registerQuery(n){if(this._queries.has(n))return this._queries.get(n);let r=this._mediaMatcher.matchMedia(n),i={observable:new x(s=>{let a=c=>this._zone.run(()=>s.next(c));return r.addListener(a),()=>{r.removeListener(a)}}).pipe(yc(r),W(({matches:s})=>({query:n,matches:s})),Yr(this._destroySubject)),mql:r};return this._queries.set(n,i),i}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function pD(e){return e.map(t=>t.split(",")).reduce((t,n)=>t.concat(n)).map(t=>t.trim())}function bA(e){if(e.type==="characterData"&&e.target instanceof Comment)return!0;if(e.type==="childList"){for(let t=0;t<e.addedNodes.length;t++)if(!(e.addedNodes[t]instanceof Comment))return!1;for(let t=0;t<e.removedNodes.length;t++)if(!(e.removedNodes[t]instanceof Comment))return!1;return!0}return!1}var gD=(()=>{class e{create(n){return typeof MutationObserver>"u"?null:new MutationObserver(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mD=(()=>{class e{_mutationObserverFactory=h(gD);_observedElements=new Map;_ngZone=h(k);constructor(){}ngOnDestroy(){this._observedElements.forEach((n,r)=>this._cleanupObserver(r))}observe(n){let r=Lr(n);return new x(o=>{let s=this._observeElement(r).pipe(W(a=>a.filter(c=>!bA(c))),Ne(a=>!!a.length)).subscribe(a=>{this._ngZone.run(()=>{o.next(a)})});return()=>{s.unsubscribe(),this._unobserveElement(r)}})}_observeElement(n){return this._ngZone.runOutsideAngular(()=>{if(this._observedElements.has(n))this._observedElements.get(n).count++;else{let r=new $,o=this._mutationObserverFactory.create(i=>r.next(i));o&&o.observe(n,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(n,{observer:o,stream:r,count:1})}return this._observedElements.get(n).stream})}_unobserveElement(n){this._observedElements.has(n)&&(this._observedElements.get(n).count--,this._observedElements.get(n).count||this._cleanupObserver(n))}_cleanupObserver(n){if(this._observedElements.has(n)){let{observer:r,stream:o}=this._observedElements.get(n);r&&r.disconnect(),o.complete(),this._observedElements.delete(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),gz=(()=>{class e{_contentObserver=h(mD);_elementRef=h(he);event=new Ie;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._disabled?this._unsubscribe():this._subscribe()}_disabled=!1;get debounce(){return this._debounce}set debounce(n){this._debounce=iD(n),this._subscribe()}_debounce;_currentSubscription=null;constructor(){}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let n=this._contentObserver.observe(this._elementRef);this._currentSubscription=(this.debounce?n.pipe(mn(this.debounce)):n).subscribe(this.event)}_unsubscribe(){this._currentSubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Et({type:e,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[2,"cdkObserveContentDisabled","disabled",Uo],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"]})}return e})(),yD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({providers:[gD]})}return e})();var TA=(()=>{class e{_platform=h(_t);constructor(){}isDisabled(n){return n.hasAttribute("disabled")}isVisible(n){return SA(n)&&getComputedStyle(n).visibility==="visible"}isTabbable(n){if(!this._platform.isBrowser)return!1;let r=MA(PA(n));if(r&&(vD(r)===-1||!this.isVisible(r)))return!1;let o=n.nodeName.toLowerCase(),i=vD(n);return n.hasAttribute("contenteditable")?i!==-1:o==="iframe"||o==="object"||this._platform.WEBKIT&&this._platform.IOS&&!FA(n)?!1:o==="audio"?n.hasAttribute("controls")?i!==-1:!1:o==="video"?i===-1?!1:i!==null?!0:this._platform.FIREFOX||n.hasAttribute("controls"):n.tabIndex>=0}isFocusable(n,r){return kA(n)&&!this.isDisabled(n)&&(r?.ignoreVisibility||this.isVisible(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function MA(e){try{return e.frameElement}catch{return null}}function SA(e){return!!(e.offsetWidth||e.offsetHeight||typeof e.getClientRects=="function"&&e.getClientRects().length)}function NA(e){let t=e.nodeName.toLowerCase();return t==="input"||t==="select"||t==="button"||t==="textarea"}function AA(e){return xA(e)&&e.type=="hidden"}function RA(e){return OA(e)&&e.hasAttribute("href")}function xA(e){return e.nodeName.toLowerCase()=="input"}function OA(e){return e.nodeName.toLowerCase()=="a"}function _D(e){if(!e.hasAttribute("tabindex")||e.tabIndex===void 0)return!1;let t=e.getAttribute("tabindex");return!!(t&&!isNaN(parseInt(t,10)))}function vD(e){if(!_D(e))return null;let t=parseInt(e.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}function FA(e){let t=e.nodeName.toLowerCase(),n=t==="input"&&e.type;return n==="text"||n==="password"||t==="select"||t==="textarea"}function kA(e){return AA(e)?!1:NA(e)||RA(e)||e.hasAttribute("contenteditable")||_D(e)}function PA(e){return e.ownerDocument&&e.ownerDocument.defaultView||window}var Sf=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(t){this._enabled=t,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_enabled=!0;constructor(t,n,r,o,i=!1,s){this._element=t,this._checker=n,this._ngZone=r,this._document=o,this._injector=s,i||this.attachAnchors()}destroy(){let t=this._startAnchor,n=this._endAnchor;t&&(t.removeEventListener("focus",this.startAnchorListener),t.remove()),n&&(n.removeEventListener("focus",this.endAnchorListener),n.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusInitialElement(t)))})}focusFirstTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusFirstTabbableElement(t)))})}focusLastTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusLastTabbableElement(t)))})}_getRegionBoundary(t){let n=this._element.querySelectorAll(`[cdk-focus-region-${t}], [cdkFocusRegion${t}], [cdk-focus-${t}]`);return t=="start"?n.length?n[0]:this._getFirstTabbableElement(this._element):n.length?n[n.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(t){let n=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(n){if(!this._checker.isFocusable(n)){let r=this._getFirstTabbableElement(n);return r?.focus(t),!!r}return n.focus(t),!0}return this.focusFirstTabbableElement(t)}focusFirstTabbableElement(t){let n=this._getRegionBoundary("start");return n&&n.focus(t),!!n}focusLastTabbableElement(t){let n=this._getRegionBoundary("end");return n&&n.focus(t),!!n}hasAttached(){return this._hasAttached}_getFirstTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=0;r<n.length;r++){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(n[r]):null;if(o)return o}return null}_getLastTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=n.length-1;r>=0;r--){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(n[r]):null;if(o)return o}return null}_createAnchor(){let t=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,t),t.classList.add("cdk-visually-hidden"),t.classList.add("cdk-focus-trap-anchor"),t.setAttribute("aria-hidden","true"),t}_toggleAnchorTabIndex(t,n){t?n.setAttribute("tabindex","0"):n.removeAttribute("tabindex")}toggleAnchors(t){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_executeOnStable(t){this._injector?ha(t,{injector:this._injector}):setTimeout(t)}},LA=(()=>{class e{_checker=h(TA);_ngZone=h(k);_document=h(O);_injector=h(oe);constructor(){h(oi).load(Za)}create(n,r=!1){return new Sf(n,this._checker,this._ngZone,this._document,r,this._injector)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ID=new E("liveAnnouncerElement",{providedIn:"root",factory:CD});function CD(){return null}var wD=new E("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),jA=0,BA=(()=>{class e{_ngZone=h(k);_defaultOptions=h(wD,{optional:!0});_liveElement;_document=h(O);_previousTimeout;_currentPromise;_currentResolve;constructor(){let n=h(ID,{optional:!0});this._liveElement=n||this._createLiveElement()}announce(n,...r){let o=this._defaultOptions,i,s;return r.length===1&&typeof r[0]=="number"?s=r[0]:[i,s]=r,this.clear(),clearTimeout(this._previousTimeout),i||(i=o&&o.politeness?o.politeness:"polite"),s==null&&o&&(s=o.duration),this._liveElement.setAttribute("aria-live",i),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(a=>this._currentResolve=a)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=n,typeof s=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),s)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let n="cdk-live-announcer-element",r=this._document.getElementsByClassName(n),o=this._document.createElement("div");for(let i=0;i<r.length;i++)r[i].remove();return o.classList.add(n),o.classList.add("cdk-visually-hidden"),o.setAttribute("aria-atomic","true"),o.setAttribute("aria-live","polite"),o.id=`cdk-live-announcer-${jA++}`,this._document.body.appendChild(o),o}_exposeAnnouncerToModals(n){let r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let o=0;o<r.length;o++){let i=r[o],s=i.getAttribute("aria-owns");s?s.indexOf(n)===-1&&i.setAttribute("aria-owns",s+" "+n):i.setAttribute("aria-owns",n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var rn=function(e){return e[e.NONE=0]="NONE",e[e.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",e[e.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",e}(rn||{}),ED="cdk-high-contrast-black-on-white",DD="cdk-high-contrast-white-on-black",Mf="cdk-high-contrast-active",Ya=(()=>{class e{_platform=h(_t);_hasCheckedHighContrastMode;_document=h(O);_breakpointSubscription;constructor(){this._breakpointSubscription=h(hD).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return rn.NONE;let n=this._document.createElement("div");n.style.backgroundColor="rgb(1,2,3)",n.style.position="absolute",this._document.body.appendChild(n);let r=this._document.defaultView||window,o=r&&r.getComputedStyle?r.getComputedStyle(n):null,i=(o&&o.backgroundColor||"").replace(/ /g,"");switch(n.remove(),i){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return rn.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return rn.BLACK_ON_WHITE}return rn.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let n=this._document.body.classList;n.remove(Mf,ED,DD),this._hasCheckedHighContrastMode=!0;let r=this.getHighContrastMode();r===rn.BLACK_ON_WHITE?n.add(Mf,ED):r===rn.WHITE_ON_BLACK&&n.add(Mf,DD)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),VA=(()=>{class e{constructor(){h(Ya)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({imports:[yD]})}return e})();var Nf={},UA=(()=>{class e{_appId=h(be);getId(n){return this._appId!=="ng"&&(n+=this._appId),Nf.hasOwnProperty(n)||(Nf[n]=0),`${n}${Nf[n]++}`}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var HA=200,Ka=class{_letterKeyStream=new $;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new $;selectedItem=this._selectedItem;constructor(t,n){let r=typeof n?.debounceInterval=="number"?n.debounceInterval:HA;n?.skipPredicate&&(this._skipPredicateFn=n.skipPredicate),this.setItems(t),this._setupKeyHandler(r)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){let n=t.keyCode;t.key&&t.key.length===1?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(n>=65&&n<=90||n>=48&&n<=57)&&this._letterKeyStream.next(String.fromCharCode(n))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe(vn(n=>this._pressedLetters.push(n)),mn(t),Ne(()=>this._pressedLetters.length>0),W(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(n=>{for(let r=1;r<this._items.length+1;r++){let o=(this._selectedItemIndex+r)%this._items.length,i=this._items[o];if(!this._skipPredicateFn?.(i)&&i.getLabel?.().toLocaleUpperCase().trim().indexOf(n)===0){this._selectedItem.next(i);break}}this._pressedLetters=[]})}};function bD(e,...t){return t.length?t.some(n=>e[n]):e.altKey||e.shiftKey||e.ctrlKey||e.metaKey}var jr=class{_items;_activeItemIndex=gt(-1);_activeItem=gt(null);_wrap=!1;_typeaheadSubscription=Z.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=t=>t.disabled;constructor(t,n){this._items=t,t instanceof jn?this._itemChangesSubscription=t.changes.subscribe(r=>this._itemsChanged(r.toArray())):dr(t)&&(this._effectRef=Ld(()=>this._itemsChanged(t()),{injector:n}))}tabOut=new $;change=new $;skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();let n=this._getItemsArray();return this._typeahead=new Ka(n,{debounceInterval:typeof t=="number"?t:void 0,skipPredicate:r=>this._skipPredicateFn(r)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(r=>{this.setActiveItem(r)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,n=10){return this._pageUpAndDown={enabled:t,delta:n},this}setActiveItem(t){let n=this._activeItem();this.updateActiveItem(t),this._activeItem()!==n&&this.change.next(this._activeItemIndex())}onKeydown(t){let n=t.keyCode,o=["altKey","ctrlKey","metaKey","shiftKey"].every(i=>!t[i]||this._allowedModifierKeys.indexOf(i)>-1);switch(n){case 9:this.tabOut.next();return;case 40:if(this._vertical&&o){this.setNextItemActive();break}else return;case 38:if(this._vertical&&o){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&o){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&o){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&o){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&o){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex()-this._pageUpAndDown.delta;this._setActiveItemByIndex(i>0?i:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex()+this._pageUpAndDown.delta,s=this._getItemsArray().length;this._setActiveItemByIndex(i<s?i:s-1,-1);break}else return;default:(o||bD(t,"shiftKey"))&&this._typeahead?.handleKey(t);return}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex()}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex()<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex()<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){let n=this._getItemsArray(),r=typeof t=="number"?t:n.indexOf(t),o=n[r];this._activeItem.set(o??null),this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){let n=this._getItemsArray();for(let r=1;r<=n.length;r++){let o=(this._activeItemIndex()+t*r+n.length)%n.length,i=n[o];if(!this._skipPredicateFn(i)){this.setActiveItem(o);return}}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex()+t,t)}_setActiveItemByIndex(t,n){let r=this._getItemsArray();if(r[t]){for(;this._skipPredicateFn(r[t]);)if(t+=n,!r[t])return;this.setActiveItem(t)}}_getItemsArray(){return dr(this._items)?this._items():this._items instanceof jn?this._items.toArray():this._items}_itemsChanged(t){this._typeahead?.setItems(t);let n=this._activeItem();if(n){let r=t.indexOf(n);r>-1&&r!==this._activeItemIndex()&&(this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r))}}};var Af=class extends jr{setActiveItem(t){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(t),this.activeItem&&this.activeItem.setActiveStyles()}};var Rf=class extends jr{_origin="program";setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}};var MD=" ";function $A(e,t,n){let r=Xa(e,t);n=n.trim(),!r.some(o=>o.trim()===n)&&(r.push(n),e.setAttribute(t,r.join(MD)))}function zA(e,t,n){let r=Xa(e,t);n=n.trim();let o=r.filter(i=>i!==n);o.length?e.setAttribute(t,o.join(MD)):e.removeAttribute(t)}function Xa(e,t){return e.getAttribute(t)?.match(/\S+/g)??[]}var SD="cdk-describedby-message",Qa="cdk-describedby-host",Of=0,dG=(()=>{class e{_platform=h(_t);_document=h(O);_messageRegistry=new Map;_messagesContainer=null;_id=`${Of++}`;constructor(){h(oi).load(Za),this._id=h(be)+"-"+Of++}describe(n,r,o){if(!this._canBeDescribed(n,r))return;let i=xf(r,o);typeof r!="string"?(TD(r,this._id),this._messageRegistry.set(i,{messageElement:r,referenceCount:0})):this._messageRegistry.has(i)||this._createMessageElement(r,o),this._isElementDescribedByMessage(n,i)||this._addMessageReference(n,i)}removeDescription(n,r,o){if(!r||!this._isElementNode(n))return;let i=xf(r,o);if(this._isElementDescribedByMessage(n,i)&&this._removeMessageReference(n,i),typeof r=="string"){let s=this._messageRegistry.get(i);s&&s.referenceCount===0&&this._deleteMessageElement(i)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let n=this._document.querySelectorAll(`[${Qa}="${this._id}"]`);for(let r=0;r<n.length;r++)this._removeCdkDescribedByReferenceIds(n[r]),n[r].removeAttribute(Qa);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(n,r){let o=this._document.createElement("div");TD(o,this._id),o.textContent=n,r&&o.setAttribute("role",r),this._createMessagesContainer(),this._messagesContainer.appendChild(o),this._messageRegistry.set(xf(n,r),{messageElement:o,referenceCount:0})}_deleteMessageElement(n){this._messageRegistry.get(n)?.messageElement?.remove(),this._messageRegistry.delete(n)}_createMessagesContainer(){if(this._messagesContainer)return;let n="cdk-describedby-message-container",r=this._document.querySelectorAll(`.${n}[platform="server"]`);for(let i=0;i<r.length;i++)r[i].remove();let o=this._document.createElement("div");o.style.visibility="hidden",o.classList.add(n),o.classList.add("cdk-visually-hidden"),this._platform.isBrowser||o.setAttribute("platform","server"),this._document.body.appendChild(o),this._messagesContainer=o}_removeCdkDescribedByReferenceIds(n){let r=Xa(n,"aria-describedby").filter(o=>o.indexOf(SD)!=0);n.setAttribute("aria-describedby",r.join(" "))}_addMessageReference(n,r){let o=this._messageRegistry.get(r);$A(n,"aria-describedby",o.messageElement.id),n.setAttribute(Qa,this._id),o.referenceCount++}_removeMessageReference(n,r){let o=this._messageRegistry.get(r);o.referenceCount--,zA(n,"aria-describedby",o.messageElement.id),n.removeAttribute(Qa)}_isElementDescribedByMessage(n,r){let o=Xa(n,"aria-describedby"),i=this._messageRegistry.get(r),s=i&&i.messageElement.id;return!!s&&o.indexOf(s)!=-1}_canBeDescribed(n,r){if(!this._isElementNode(n))return!1;if(r&&typeof r=="object")return!0;let o=r==null?"":`${r}`.trim(),i=n.getAttribute("aria-label");return o?!i||i.trim()!==o:!1}_isElementNode(n){return n.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function xf(e,t){return typeof e=="string"?`${t||""}/${e}`:e}function TD(e,t){e.id||(e.id=`${SD}-${t}-${Of++}`)}var GA=new E("cdk-dir-doc",{providedIn:"root",factory:WA});function WA(){return h(O)}var qA=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function ND(e){let t=e?.toLowerCase()||"";return t==="auto"&&typeof navigator<"u"&&navigator?.language?qA.test(navigator.language)?"rtl":"ltr":t==="rtl"?"rtl":"ltr"}var ZA=(()=>{class e{get value(){return this.valueSignal()}valueSignal=gt("ltr");change=new Ie;constructor(){let n=h(GA,{optional:!0});if(n){let r=n.body?n.body.dir:null,o=n.documentElement?n.documentElement.dir:null;this.valueSignal.set(ND(r||o||"ltr"))}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Ff=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({})}return e})();var kf=(()=>{class e{constructor(){h(Ya)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({imports:[Ff,Ff]})}return e})();var YA=["*"],KA=new E("MAT_ICON_DEFAULT_OPTIONS"),QA=new E("mat-icon-location",{providedIn:"root",factory:XA});function XA(){let e=h(O),t=e?e.location:null;return{getPathname:()=>t?t.pathname+t.search:""}}var AD=["clip-path","color-profile","src","cursor","fill","filter","marker","marker-start","marker-mid","marker-end","mask","stroke"],JA=AD.map(e=>`[${e}]`).join(", "),eR=/^url\(['"]?#(.*?)['"]?\)$/,BG=(()=>{class e{_elementRef=h(he);_iconRegistry=h(nD);_location=h(QA);_errorHandler=h(le);_defaultColor;get color(){return this._color||this._defaultColor}set color(n){this._color=n}_color;inline=!1;get svgIcon(){return this._svgIcon}set svgIcon(n){n!==this._svgIcon&&(n?this._updateSvgIcon(n):this._svgIcon&&this._clearSvgElement(),this._svgIcon=n)}_svgIcon;get fontSet(){return this._fontSet}set fontSet(n){let r=this._cleanupFontValue(n);r!==this._fontSet&&(this._fontSet=r,this._updateFontIconClasses())}_fontSet;get fontIcon(){return this._fontIcon}set fontIcon(n){let r=this._cleanupFontValue(n);r!==this._fontIcon&&(this._fontIcon=r,this._updateFontIconClasses())}_fontIcon;_previousFontSetClass=[];_previousFontIconClass;_svgName;_svgNamespace;_previousPath;_elementsWithExternalReferences;_currentIconFetch=Z.EMPTY;constructor(){let n=h(new ba("aria-hidden"),{optional:!0}),r=h(KA,{optional:!0});r&&(r.color&&(this.color=this._defaultColor=r.color),r.fontSet&&(this.fontSet=r.fontSet)),n||this._elementRef.nativeElement.setAttribute("aria-hidden","true")}_splitIconName(n){if(!n)return["",""];let r=n.split(":");switch(r.length){case 1:return["",r[0]];case 2:return r;default:throw Error(`Invalid icon name: "${n}"`)}}ngOnInit(){this._updateFontIconClasses()}ngAfterViewChecked(){let n=this._elementsWithExternalReferences;if(n&&n.size){let r=this._location.getPathname();r!==this._previousPath&&(this._previousPath=r,this._prependPathToReferences(r))}}ngOnDestroy(){this._currentIconFetch.unsubscribe(),this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear()}_usingFontIcon(){return!this.svgIcon}_setSvgElement(n){this._clearSvgElement();let r=this._location.getPathname();this._previousPath=r,this._cacheChildrenWithExternalReferences(n),this._prependPathToReferences(r),this._elementRef.nativeElement.appendChild(n)}_clearSvgElement(){let n=this._elementRef.nativeElement,r=n.childNodes.length;for(this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear();r--;){let o=n.childNodes[r];(o.nodeType!==1||o.nodeName.toLowerCase()==="svg")&&o.remove()}}_updateFontIconClasses(){if(!this._usingFontIcon())return;let n=this._elementRef.nativeElement,r=(this.fontSet?this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/):this._iconRegistry.getDefaultFontSetClass()).filter(o=>o.length>0);this._previousFontSetClass.forEach(o=>n.classList.remove(o)),r.forEach(o=>n.classList.add(o)),this._previousFontSetClass=r,this.fontIcon!==this._previousFontIconClass&&!r.includes("mat-ligature-font")&&(this._previousFontIconClass&&n.classList.remove(this._previousFontIconClass),this.fontIcon&&n.classList.add(this.fontIcon),this._previousFontIconClass=this.fontIcon)}_cleanupFontValue(n){return typeof n=="string"?n.trim().split(" ")[0]:n}_prependPathToReferences(n){let r=this._elementsWithExternalReferences;r&&r.forEach((o,i)=>{o.forEach(s=>{i.setAttribute(s.name,`url('${n}#${s.value}')`)})})}_cacheChildrenWithExternalReferences(n){let r=n.querySelectorAll(JA),o=this._elementsWithExternalReferences=this._elementsWithExternalReferences||new Map;for(let i=0;i<r.length;i++)AD.forEach(s=>{let a=r[i],c=a.getAttribute(s),u=c?c.match(eR):null;if(u){let l=o.get(a);l||(l=[],o.set(a,l)),l.push({name:s,value:u[1]})}})}_updateSvgIcon(n){if(this._svgNamespace=null,this._svgName=null,this._currentIconFetch.unsubscribe(),n){let[r,o]=this._splitIconName(n);r&&(this._svgNamespace=r),o&&(this._svgName=o),this._currentIconFetch=this._iconRegistry.getNamedSvgIcon(o,r).pipe(yn(1)).subscribe(i=>this._setSvgElement(i),i=>{let s=`Error retrieving icon ${r}:${o}! ${i.message}`;this._errorHandler.handleError(new Error(s))})}}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Fo({type:e,selectors:[["mat-icon"]],hostAttrs:["role","img",1,"mat-icon","notranslate"],hostVars:10,hostBindings:function(r,o){r&2&&(Lo("data-mat-icon-type",o._usingFontIcon()?"font":"svg")("data-mat-icon-name",o._svgName||o.fontIcon)("data-mat-icon-namespace",o._svgNamespace||o.fontSet)("fontIcon",o._usingFontIcon()?o.fontIcon:null),xd(o.color?"mat-"+o.color:""),ma("mat-icon-inline",o.inline)("mat-icon-no-color",o.color!=="primary"&&o.color!=="accent"&&o.color!=="warn"))},inputs:{color:"color",inline:[2,"inline","inline",Uo],svgIcon:"svgIcon",fontSet:"fontSet",fontIcon:"fontIcon"},exportAs:["matIcon"],ngContentSelectors:YA,decls:1,vars:0,template:function(r,o){r&1&&(Ad(),Rd(0))},styles:[`mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}
`],encapsulation:2,changeDetection:0})}return e})(),VG=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=de({imports:[kf,kf]})}return e})();export{B as a,te as b,Vf as c,Z as d,LD as e,x as f,ac as g,cc as h,$ as i,dn as j,fn as k,st as l,we as m,lc as n,ZD as o,hn as p,W as q,dc as r,gn as s,zr as t,Jn as u,r_ as v,fc as w,$r as x,VD as y,o_ as z,Ne as A,i_ as B,xi as C,pc as D,mn as E,Gr as F,yn as G,Wr as H,c_ as I,gc as J,u_ as K,l_ as L,d_ as M,f_ as N,Zr as O,yc as P,vc as Q,Yr as R,p_ as S,vn as T,_ as U,Qi as V,D as W,de as X,I_ as Y,E as Z,C as _,h as $,ut as aa,ue as ba,sr as ca,Yp as da,Kp as ea,lh as fa,dh as ga,oe as ha,O as ia,Qe as ja,Xe as ka,Z_ as la,dr as ma,gt as na,At as oa,qs as pa,DI as qa,Sl as ra,he as sa,jn as ta,Jt as ua,NI as va,sC as wa,fm as xa,cC as ya,RC as za,Dr as Aa,Ot as Ba,cd as Ca,$e as Da,jw as Ea,Nr as Fa,ze as Ga,Un as Ha,Ty as Ia,hd as Ja,Fo as Ka,ge as La,Et as Ma,Ny as Na,Ry as Oa,xy as Pa,Ie as Qa,k as Ra,ha as Sa,Uy as Ta,ga as Ua,Rr as Va,Me as Wa,Lo as Xa,dT as Ya,fT as Za,pT as _a,hT as $a,gT as ab,mT as bb,qy as cb,bd as db,Td as eb,Zy as fb,Md as gb,Sd as hb,Yy as ib,IT as jb,Xy as kb,ev as lb,tv as mb,MT as nb,Ad as ob,Rd as pb,AT as qb,RT as rb,xT as sb,OT as tb,FT as ub,kT as vb,PT as wb,LT as xb,iv as yb,ma as zb,xd as Ab,oM as Bb,hv as Cb,Od as Db,gv as Eb,yv as Fb,aM as Gb,vv as Hb,cM as Ib,Dv as Jb,gM as Kb,yM as Lb,EM as Mb,DM as Nb,IM as Ob,Cv as Pb,wv as Qb,Ld as Rb,ba as Sb,wS as Tb,MH as Ub,SH as Vb,NH as Wb,kS as Xb,Uo as Yb,AH as Zb,RH as _b,rE as $b,kt as ac,Ta as bc,dE as cc,sN as dc,aN as ec,lN as fc,ME as gc,SE as hc,df as ic,wN as jc,QN as kc,XN as lc,u8 as mc,l8 as nc,d8 as oc,_f as pc,If as qc,D8 as rc,Pr as sc,_t as tc,oD as uc,iD as vc,Lr as wc,dD as xc,_A as yc,oi as zc,Za as Ac,Tf as Bc,IA as Cc,hD as Dc,gz as Ec,yD as Fc,TA as Gc,LA as Hc,BA as Ic,VA as Jc,UA as Kc,bD as Lc,Af as Mc,Rf as Nc,$A as Oc,zA as Pc,dG as Qc,ZA as Rc,Ff as Sc,kf as Tc,BG as Uc,VG as Vc};
