import{a as On,b as kn,c as In,d as Rn}from"./chunk-2D4PYS2W.js";import{b as <PERSON>,c as Gn,f as Ct,h as Pt,k as At}from"./chunk-PEAKHU55.js";import{a as ui,d as wt,f as Yn,g as Un}from"./chunk-P466WDZO.js";import{g as xe,i as mi,k as Qn,m as jn,n as bt,o as qn,p as Hn,q as St}from"./chunk-M2SB7VIT.js";import{$ as y,A as tn,Aa as cn,Ab as pt,Ba as mn,Bb as P,Ca as un,Cb as ft,Da as hn,Db as li,Ea as dn,Fa as lt,G as nn,Ga as pn,Jb as Tn,Jc as zn,Ka as j,Kc as Bn,La as ye,Lc as vt,Ma as ve,Na as fn,Nc as Vn,Ob as Sn,P as at,Pa as gn,Q as ni,Qa as ct,Qc as Kn,R as $,Ra as Ie,Rc as Tt,Sa as qe,Tc as ne,U as S,Uc as Mt,Vc as Et,W as Oe,X as fe,Xa as He,Xb as Fe,Ya as mt,Yb as Ue,Z as ae,Za as ut,_ as ke,a as K,ab as ri,b as Ji,bb as oi,c as en,cb as W,d as De,da as ge,db as M,dc as Mn,ea as _e,eb as E,fa as sn,fb as be,gb as ai,gc as gt,ha as ee,hb as _n,hc as _t,i as pe,ia as le,ic as En,j as ti,jb as ht,jc as wn,kb as yn,kc as Cn,la as rn,lb as ce,lc as Pn,m as ii,mb as vn,na as on,nb as Re,nc as An,ob as Ye,oc as Dn,pb as Te,pc as Nn,qb as Ne,qc as Fn,rb as dt,sa as X,sb as te,ta as an,tb as ie,tc as yt,ua as ln,uc as xn,va as si,vc as ci,xb as bn,xc as Ge,z as ot,za as x,zb as Z,zc as Ln}from"./chunk-QPLGU5OF.js";var b=function(s){return s[s.State=0]="State",s[s.Transition=1]="Transition",s[s.Sequence=2]="Sequence",s[s.Group=3]="Group",s[s.Animate=4]="Animate",s[s.Keyframes=5]="Keyframes",s[s.Style=6]="Style",s[s.Trigger=7]="Trigger",s[s.Reference=8]="Reference",s[s.AnimateChild=9]="AnimateChild",s[s.AnimateRef=10]="AnimateRef",s[s.Query=11]="Query",s[s.Stagger=12]="Stagger",s}(b||{}),q="*";function $n(s,t=null){return{type:b.Sequence,steps:s,options:t}}function hi(s){return{type:b.Style,styles:s,offset:null}}var se=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,e=0){this.totalTime=t+e}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(i=>i()),e.length=0}},ze=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let e=0,i=0,n=0,r=this.players.length;r==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(o=>{o.onDone(()=>{++e==r&&this._onFinish()}),o.onDestroy(()=>{++i==r&&this._onDestroy()}),o.onStart(()=>{++n==r&&this._onStart()})}),this.totalTime=this.players.reduce((o,a)=>Math.max(o,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let e=t*this.totalTime;this.players.forEach(i=>{let n=i.totalTime?Math.min(1,e/i.totalTime):1;i.setPosition(n)})}getPosition(){let t=this.players.reduce((e,i)=>e===null||i.totalTime>e.totalTime?i:e,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(i=>i()),e.length=0}},$e="!";function Xn(s){return new S(3e3,!1)}function hr(){return new S(3100,!1)}function dr(){return new S(3101,!1)}function pr(s){return new S(3001,!1)}function fr(s){return new S(3003,!1)}function gr(s){return new S(3004,!1)}function Zn(s,t){return new S(3005,!1)}function Jn(){return new S(3006,!1)}function es(){return new S(3007,!1)}function ts(s,t){return new S(3008,!1)}function is(s){return new S(3002,!1)}function ns(s,t,e,i,n){return new S(3010,!1)}function ss(){return new S(3011,!1)}function rs(){return new S(3012,!1)}function os(){return new S(3200,!1)}function as(){return new S(3202,!1)}function ls(){return new S(3013,!1)}function cs(s){return new S(3014,!1)}function ms(s){return new S(3015,!1)}function us(s){return new S(3016,!1)}function hs(s,t){return new S(3404,!1)}function _r(s){return new S(3502,!1)}function ds(s){return new S(3503,!1)}function ps(){return new S(3300,!1)}function fs(s){return new S(3504,!1)}function gs(s){return new S(3301,!1)}function _s(s,t){return new S(3302,!1)}function ys(s){return new S(3303,!1)}function vs(s,t){return new S(3400,!1)}function bs(s){return new S(3401,!1)}function Ts(s){return new S(3402,!1)}function Ss(s,t){return new S(3505,!1)}function re(s){switch(s.length){case 0:return new se;case 1:return s[0];default:return new ze(s)}}function gi(s,t,e=new Map,i=new Map){let n=[],r=[],o=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),u=c==o,m=u&&a||new Map;l.forEach((v,g)=>{let d=g,_=v;if(g!=="offset")switch(d=s.normalizePropertyName(d,n),_){case $e:_=e.get(g);break;case q:_=i.get(g);break;default:_=s.normalizeStyleValue(g,d,_,n);break}m.set(d,_)}),u||r.push(m),a=m,o=c}),n.length)throw _r(n);return r}function Dt(s,t,e,i){switch(t){case"start":s.onStart(()=>i(e&&di(e,"start",s)));break;case"done":s.onDone(()=>i(e&&di(e,"done",s)));break;case"destroy":s.onDestroy(()=>i(e&&di(e,"destroy",s)));break}}function di(s,t,e){let i=e.totalTime,n=!!e.disabled,r=Ot(s.element,s.triggerName,s.fromState,s.toState,t||s.phaseName,i??s.totalTime,n),o=s._data;return o!=null&&(r._data=o),r}function Ot(s,t,e,i,n="",r=0,o){return{element:s,triggerName:t,fromState:e,toState:i,phaseName:n,totalTime:r,disabled:!!o}}function L(s,t,e){let i=s.get(t);return i||s.set(t,i=e),i}function _i(s){let t=s.indexOf(":"),e=s.substring(1,t),i=s.slice(t+1);return[e,i]}var yr=typeof document>"u"?null:document.documentElement;function kt(s){let t=s.parentNode||s.host||null;return t===yr?null:t}function vr(s){return s.substring(1,6)=="ebkit"}var Se=null,Wn=!1;function Ms(s){Se||(Se=br()||{},Wn=Se.style?"WebkitAppearance"in Se.style:!1);let t=!0;return Se.style&&!vr(s)&&(t=s in Se.style,!t&&Wn&&(t="Webkit"+s.charAt(0).toUpperCase()+s.slice(1)in Se.style)),t}function br(){return typeof document<"u"?document.body:null}function yi(s,t){for(;t;){if(t===s)return!0;t=kt(t)}return!1}function vi(s,t,e){if(e)return Array.from(s.querySelectorAll(t));let i=s.querySelector(t);return i?[i]:[]}var Tr=1e3,bi="{{",Sr="}}",Ti="ng-enter",It="ng-leave",Xe="ng-trigger",We=".ng-trigger",Si="ng-animating",Rt=".ng-animating";function J(s){if(typeof s=="number")return s;let t=s.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:pi(parseFloat(t[1]),t[2])}function pi(s,t){switch(t){case"s":return s*Tr;default:return s}}function Ze(s,t,e){return s.hasOwnProperty("duration")?s:Mr(s,t,e)}function Mr(s,t,e){let i=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,n,r=0,o="";if(typeof s=="string"){let a=s.match(i);if(a===null)return t.push(Xn(s)),{duration:0,delay:0,easing:""};n=pi(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(r=pi(parseFloat(l),a[4]));let c=a[5];c&&(o=c)}else n=s;if(!e){let a=!1,l=t.length;n<0&&(t.push(hr()),a=!0),r<0&&(t.push(dr()),a=!0),a&&t.splice(l,0,Xn(s))}return{duration:n,delay:r,easing:o}}function Es(s){return s.length?s[0]instanceof Map?s:s.map(t=>new Map(Object.entries(t))):[]}function H(s,t,e){t.forEach((i,n)=>{let r=Nt(n);e&&!e.has(n)&&e.set(n,s.style[r]),s.style[r]=i})}function me(s,t){t.forEach((e,i)=>{let n=Nt(i);s.style[n]=""})}function Be(s){return Array.isArray(s)?s.length==1?s[0]:$n(s):s}function ws(s,t,e){let i=t.params||{},n=Mi(s);n.length&&n.forEach(r=>{i.hasOwnProperty(r)||e.push(pr(r))})}var fi=new RegExp(`${bi}\\s*(.+?)\\s*${Sr}`,"g");function Mi(s){let t=[];if(typeof s=="string"){let e;for(;e=fi.exec(s);)t.push(e[1]);fi.lastIndex=0}return t}function Ve(s,t,e){let i=`${s}`,n=i.replace(fi,(r,o)=>{let a=t[o];return a==null&&(e.push(fr(o)),a=""),a.toString()});return n==i?s:n}var Er=/-+([a-z0-9])/g;function Nt(s){return s.replace(Er,(...t)=>t[1].toUpperCase())}function Cs(s,t){return s===0||t===0}function Ps(s,t,e){if(e.size&&t.length){let i=t[0],n=[];if(e.forEach((r,o)=>{i.has(o)||n.push(o),i.set(o,r)}),n.length)for(let r=1;r<t.length;r++){let o=t[r];n.forEach(a=>o.set(a,Ft(s,a)))}}return t}function z(s,t,e){switch(t.type){case b.Trigger:return s.visitTrigger(t,e);case b.State:return s.visitState(t,e);case b.Transition:return s.visitTransition(t,e);case b.Sequence:return s.visitSequence(t,e);case b.Group:return s.visitGroup(t,e);case b.Animate:return s.visitAnimate(t,e);case b.Keyframes:return s.visitKeyframes(t,e);case b.Style:return s.visitStyle(t,e);case b.Reference:return s.visitReference(t,e);case b.AnimateChild:return s.visitAnimateChild(t,e);case b.AnimateRef:return s.visitAnimateRef(t,e);case b.Query:return s.visitQuery(t,e);case b.Stagger:return s.visitStagger(t,e);default:throw gr(t.type)}}function Ft(s,t){return window.getComputedStyle(s)[t]}var Vi=(()=>{class s{validateStyleProperty(e){return Ms(e)}containsElement(e,i){return yi(e,i)}getParentElement(e){return kt(e)}query(e,i,n){return vi(e,i,n)}computeStyle(e,i,n){return n||""}animate(e,i,n,r,o,a=[],l){return new se(n,r)}static \u0275fac=function(i){return new(i||s)};static \u0275prov=Oe({token:s,factory:s.\u0275fac})}return s})(),Ee=class{static NOOP=new Vi},we=class{};var wr=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Vt=class extends we{normalizePropertyName(t,e){return Nt(t)}normalizeStyleValue(t,e,i,n){let r="",o=i.toString().trim();if(wr.has(e)&&i!==0&&i!=="0")if(typeof i=="number")r="px";else{let a=i.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&n.push(Zn(t,i))}return o+r}};var Kt="*";function Cr(s,t){let e=[];return typeof s=="string"?s.split(/\s*,\s*/).forEach(i=>Pr(i,e,t)):e.push(s),e}function Pr(s,t,e){if(s[0]==":"){let l=Ar(s,e);if(typeof l=="function"){t.push(l);return}s=l}let i=s.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(i==null||i.length<4)return e.push(ms(s)),t;let n=i[1],r=i[2],o=i[3];t.push(As(n,o));let a=n==Kt&&o==Kt;r[0]=="<"&&!a&&t.push(As(o,n))}function Ar(s,t){switch(s){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(e,i)=>parseFloat(i)>parseFloat(e);case":decrement":return(e,i)=>parseFloat(i)<parseFloat(e);default:return t.push(us(s)),"* => *"}}var xt=new Set(["true","1"]),Lt=new Set(["false","0"]);function As(s,t){let e=xt.has(s)||Lt.has(s),i=xt.has(t)||Lt.has(t);return(n,r)=>{let o=s==Kt||s==n,a=t==Kt||t==r;return!o&&e&&typeof n=="boolean"&&(o=n?xt.has(s):Lt.has(s)),!a&&i&&typeof r=="boolean"&&(a=r?xt.has(t):Lt.has(t)),o&&a}}var zs=":self",Dr=new RegExp(`s*${zs}s*,?`,"g");function Bs(s,t,e,i){return new Di(s).build(t,e,i)}var Ds="",Di=class{_driver;constructor(t){this._driver=t}build(t,e,i){let n=new Oi(e);return this._resetContextStyleTimingState(n),z(this,Be(t),n)}_resetContextStyleTimingState(t){t.currentQuerySelector=Ds,t.collectedStyles=new Map,t.collectedStyles.set(Ds,new Map),t.currentTime=0}visitTrigger(t,e){let i=e.queryCount=0,n=e.depCount=0,r=[],o=[];return t.name.charAt(0)=="@"&&e.errors.push(Jn()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(e),a.type==b.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,r.push(this.visitState(l,e))}),l.name=c}else if(a.type==b.Transition){let l=this.visitTransition(a,e);i+=l.queryCount,n+=l.depCount,o.push(l)}else e.errors.push(es())}),{type:b.Trigger,name:t.name,states:r,transitions:o,queryCount:i,depCount:n,options:null}}visitState(t,e){let i=this.visitStyle(t.styles,e),n=t.options&&t.options.params||null;if(i.containsDynamicStyles){let r=new Set,o=n||{};i.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{Mi(l).forEach(c=>{o.hasOwnProperty(c)||r.add(c)})})}),r.size&&e.errors.push(ts(t.name,[...r.values()]))}return{type:b.State,name:t.name,style:i,options:n?{params:n}:null}}visitTransition(t,e){e.queryCount=0,e.depCount=0;let i=z(this,Be(t.animation),e),n=Cr(t.expr,e.errors);return{type:b.Transition,matchers:n,animation:i,queryCount:e.queryCount,depCount:e.depCount,options:Me(t.options)}}visitSequence(t,e){return{type:b.Sequence,steps:t.steps.map(i=>z(this,i,e)),options:Me(t.options)}}visitGroup(t,e){let i=e.currentTime,n=0,r=t.steps.map(o=>{e.currentTime=i;let a=z(this,o,e);return n=Math.max(n,e.currentTime),a});return e.currentTime=n,{type:b.Group,steps:r,options:Me(t.options)}}visitAnimate(t,e){let i=Rr(t.timings,e.errors);e.currentAnimateTimings=i;let n,r=t.styles?t.styles:hi({});if(r.type==b.Keyframes)n=this.visitKeyframes(r,e);else{let o=t.styles,a=!1;if(!o){a=!0;let c={};i.easing&&(c.easing=i.easing),o=hi(c)}e.currentTime+=i.duration+i.delay;let l=this.visitStyle(o,e);l.isEmptyStep=a,n=l}return e.currentAnimateTimings=null,{type:b.Animate,timings:i,style:n,options:null}}visitStyle(t,e){let i=this._makeStyleAst(t,e);return this._validateStyleAst(i,e),i}_makeStyleAst(t,e){let i=[],n=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of n)typeof a=="string"?a===q?i.push(a):e.errors.push(is(a)):i.push(new Map(Object.entries(a)));let r=!1,o=null;return i.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(o=a.get("easing"),a.delete("easing")),!r)){for(let l of a.values())if(l.toString().indexOf(bi)>=0){r=!0;break}}}),{type:b.Style,styles:i,easing:o,offset:t.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(t,e){let i=e.currentAnimateTimings,n=e.currentTime,r=e.currentTime;i&&r>0&&(r-=i.duration+i.delay),t.styles.forEach(o=>{typeof o!="string"&&o.forEach((a,l)=>{let c=e.collectedStyles.get(e.currentQuerySelector),u=c.get(l),m=!0;u&&(r!=n&&r>=u.startTime&&n<=u.endTime&&(e.errors.push(ns(l,u.startTime,u.endTime,r,n)),m=!1),r=u.startTime),m&&c.set(l,{startTime:r,endTime:n}),e.options&&ws(a,e.options,e.errors)})})}visitKeyframes(t,e){let i={type:b.Keyframes,styles:[],options:null};if(!e.currentAnimateTimings)return e.errors.push(ss()),i;let n=1,r=0,o=[],a=!1,l=!1,c=0,u=t.steps.map(A=>{let D=this._makeStyleAst(A,e),R=D.offset!=null?D.offset:Ir(D.styles),k=0;return R!=null&&(r++,k=D.offset=R),l=l||k<0||k>1,a=a||k<c,c=k,o.push(k),D});l&&e.errors.push(rs()),a&&e.errors.push(os());let m=t.steps.length,v=0;r>0&&r<m?e.errors.push(as()):r==0&&(v=n/(m-1));let g=m-1,d=e.currentTime,_=e.currentAnimateTimings,w=_.duration;return u.forEach((A,D)=>{let R=v>0?D==g?1:v*D:o[D],k=R*w;e.currentTime=d+_.delay+k,_.duration=k,this._validateStyleAst(A,e),A.offset=R,i.styles.push(A)}),i}visitReference(t,e){return{type:b.Reference,animation:z(this,Be(t.animation),e),options:Me(t.options)}}visitAnimateChild(t,e){return e.depCount++,{type:b.AnimateChild,options:Me(t.options)}}visitAnimateRef(t,e){return{type:b.AnimateRef,animation:this.visitReference(t.animation,e),options:Me(t.options)}}visitQuery(t,e){let i=e.currentQuerySelector,n=t.options||{};e.queryCount++,e.currentQuery=t;let[r,o]=Or(t.selector);e.currentQuerySelector=i.length?i+" "+r:r,L(e.collectedStyles,e.currentQuerySelector,new Map);let a=z(this,Be(t.animation),e);return e.currentQuery=null,e.currentQuerySelector=i,{type:b.Query,selector:r,limit:n.limit||0,optional:!!n.optional,includeSelf:o,animation:a,originalSelector:t.selector,options:Me(t.options)}}visitStagger(t,e){e.currentQuery||e.errors.push(ls());let i=t.timings==="full"?{duration:0,delay:0,easing:"full"}:Ze(t.timings,e.errors,!0);return{type:b.Stagger,animation:z(this,Be(t.animation),e),timings:i,options:null}}};function Or(s){let t=!!s.split(/\s*,\s*/).find(e=>e==zs);return t&&(s=s.replace(Dr,"")),s=s.replace(/@\*/g,We).replace(/@\w+/g,e=>We+"-"+e.slice(1)).replace(/:animating/g,Rt),[s,t]}function kr(s){return s?K({},s):null}var Oi=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(t){this.errors=t}};function Ir(s){if(typeof s=="string")return null;let t=null;if(Array.isArray(s))s.forEach(e=>{if(e instanceof Map&&e.has("offset")){let i=e;t=parseFloat(i.get("offset")),i.delete("offset")}});else if(s instanceof Map&&s.has("offset")){let e=s;t=parseFloat(e.get("offset")),e.delete("offset")}return t}function Rr(s,t){if(s.hasOwnProperty("duration"))return s;if(typeof s=="number"){let r=Ze(s,t).duration;return Ei(r,0,"")}let e=s;if(e.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=Ei(0,0,"");return r.dynamic=!0,r.strValue=e,r}let n=Ze(e,t);return Ei(n.duration,n.delay,n.easing)}function Me(s){return s?(s=K({},s),s.params&&(s.params=kr(s.params))):s={},s}function Ei(s,t,e){return{duration:s,delay:t,easing:e}}function Ki(s,t,e,i,n,r,o=null,a=!1){return{type:1,element:s,keyframes:t,preStyleProps:e,postStyleProps:i,duration:n,delay:r,totalTime:n+r,easing:o,subTimeline:a}}var et=class{_map=new Map;get(t){return this._map.get(t)||[]}append(t,e){let i=this._map.get(t);i||this._map.set(t,i=[]),i.push(...e)}has(t){return this._map.has(t)}clear(){this._map.clear()}},Nr=1,Fr=":enter",xr=new RegExp(Fr,"g"),Lr=":leave",zr=new RegExp(Lr,"g");function Vs(s,t,e,i,n,r=new Map,o=new Map,a,l,c=[]){return new ki().buildKeyframes(s,t,e,i,n,r,o,a,l,c)}var ki=class{buildKeyframes(t,e,i,n,r,o,a,l,c,u=[]){c=c||new et;let m=new Ii(t,e,c,n,r,u,[]);m.options=l;let v=l.delay?J(l.delay):0;m.currentTimeline.delayNextStep(v),m.currentTimeline.setStyles([o],null,m.errors,l),z(this,i,m);let g=m.timelines.filter(d=>d.containsAnimation());if(g.length&&a.size){let d;for(let _=g.length-1;_>=0;_--){let w=g[_];if(w.element===e){d=w;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([a],null,m.errors,l)}return g.length?g.map(d=>d.buildKeyframes()):[Ki(e,[],[],[],0,v,"",!1)]}visitTrigger(t,e){}visitState(t,e){}visitTransition(t,e){}visitAnimateChild(t,e){let i=e.subInstructions.get(e.element);if(i){let n=e.createSubContext(t.options),r=e.currentTimeline.currentTime,o=this._visitSubInstructions(i,n,n.options);r!=o&&e.transformIntoNewTimeline(o)}e.previousNode=t}visitAnimateRef(t,e){let i=e.createSubContext(t.options);i.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],e,i),this.visitReference(t.animation,i),e.transformIntoNewTimeline(i.currentTimeline.currentTime),e.previousNode=t}_applyAnimationRefDelays(t,e,i){for(let n of t){let r=n?.delay;if(r){let o=typeof r=="number"?r:J(Ve(r,n?.params??{},e.errors));i.delayNextStep(o)}}}_visitSubInstructions(t,e,i){let r=e.currentTimeline.currentTime,o=i.duration!=null?J(i.duration):null,a=i.delay!=null?J(i.delay):null;return o!==0&&t.forEach(l=>{let c=e.appendInstructionToTimeline(l,o,a);r=Math.max(r,c.duration+c.delay)}),r}visitReference(t,e){e.updateOptions(t.options,!0),z(this,t.animation,e),e.previousNode=t}visitSequence(t,e){let i=e.subContextCount,n=e,r=t.options;if(r&&(r.params||r.delay)&&(n=e.createSubContext(r),n.transformIntoNewTimeline(),r.delay!=null)){n.previousNode.type==b.Style&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Qt);let o=J(r.delay);n.delayNextStep(o)}t.steps.length&&(t.steps.forEach(o=>z(this,o,n)),n.currentTimeline.applyStylesToKeyframe(),n.subContextCount>i&&n.transformIntoNewTimeline()),e.previousNode=t}visitGroup(t,e){let i=[],n=e.currentTimeline.currentTime,r=t.options&&t.options.delay?J(t.options.delay):0;t.steps.forEach(o=>{let a=e.createSubContext(t.options);r&&a.delayNextStep(r),z(this,o,a),n=Math.max(n,a.currentTimeline.currentTime),i.push(a.currentTimeline)}),i.forEach(o=>e.currentTimeline.mergeTimelineCollectedStyles(o)),e.transformIntoNewTimeline(n),e.previousNode=t}_visitTiming(t,e){if(t.dynamic){let i=t.strValue,n=e.params?Ve(i,e.params,e.errors):i;return Ze(n,e.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,e){let i=e.currentAnimateTimings=this._visitTiming(t.timings,e),n=e.currentTimeline;i.delay&&(e.incrementTime(i.delay),n.snapshotCurrentStyles());let r=t.style;r.type==b.Keyframes?this.visitKeyframes(r,e):(e.incrementTime(i.duration),this.visitStyle(r,e),n.applyStylesToKeyframe()),e.currentAnimateTimings=null,e.previousNode=t}visitStyle(t,e){let i=e.currentTimeline,n=e.currentAnimateTimings;!n&&i.hasCurrentStyleProperties()&&i.forwardFrame();let r=n&&n.easing||t.easing;t.isEmptyStep?i.applyEmptyStep(r):i.setStyles(t.styles,r,e.errors,e.options),e.previousNode=t}visitKeyframes(t,e){let i=e.currentAnimateTimings,n=e.currentTimeline.duration,r=i.duration,a=e.createSubContext().currentTimeline;a.easing=i.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*r),a.setStyles(l.styles,l.easing,e.errors,e.options),a.applyStylesToKeyframe()}),e.currentTimeline.mergeTimelineCollectedStyles(a),e.transformIntoNewTimeline(n+r),e.previousNode=t}visitQuery(t,e){let i=e.currentTimeline.currentTime,n=t.options||{},r=n.delay?J(n.delay):0;r&&(e.previousNode.type===b.Style||i==0&&e.currentTimeline.hasCurrentStyleProperties())&&(e.currentTimeline.snapshotCurrentStyles(),e.previousNode=Qt);let o=i,a=e.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!n.optional,e.errors);e.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{e.currentQueryIndex=u;let m=e.createSubContext(t.options,c);r&&m.delayNextStep(r),c===e.element&&(l=m.currentTimeline),z(this,t.animation,m),m.currentTimeline.applyStylesToKeyframe();let v=m.currentTimeline.currentTime;o=Math.max(o,v)}),e.currentQueryIndex=0,e.currentQueryTotal=0,e.transformIntoNewTimeline(o),l&&(e.currentTimeline.mergeTimelineCollectedStyles(l),e.currentTimeline.snapshotCurrentStyles()),e.previousNode=t}visitStagger(t,e){let i=e.parentContext,n=e.currentTimeline,r=t.timings,o=Math.abs(r.duration),a=o*(e.currentQueryTotal-1),l=o*e.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=a-l;break;case"full":l=i.currentStaggerTime;break}let u=e.currentTimeline;l&&u.delayNextStep(l);let m=u.currentTime;z(this,t.animation,e),e.previousNode=t,i.currentStaggerTime=n.currentTime-m+(n.startTime-i.currentTimeline.startTime)}},Qt={},Ii=class s{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=Qt;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(t,e,i,n,r,o,a,l){this._driver=t,this.element=e,this.subInstructions=i,this._enterClassName=n,this._leaveClassName=r,this.errors=o,this.timelines=a,this.currentTimeline=l||new jt(this._driver,e,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,e){if(!t)return;let i=t,n=this.options;i.duration!=null&&(n.duration=J(i.duration)),i.delay!=null&&(n.delay=J(i.delay));let r=i.params;if(r){let o=n.params;o||(o=this.options.params={}),Object.keys(r).forEach(a=>{(!e||!o.hasOwnProperty(a))&&(o[a]=Ve(r[a],o,this.errors))})}}_copyOptions(){let t={};if(this.options){let e=this.options.params;if(e){let i=t.params={};Object.keys(e).forEach(n=>{i[n]=e[n]})}}return t}createSubContext(t=null,e,i){let n=e||this.element,r=new s(this._driver,n,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(n,i||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(t),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(t){return this.previousNode=Qt,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,e,i){let n={duration:e??t.duration,delay:this.currentTimeline.currentTime+(i??0)+t.delay,easing:""},r=new Ri(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,n,t.stretchStartingKeyframe);return this.timelines.push(r),n}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,e,i,n,r,o){let a=[];if(n&&a.push(this.element),t.length>0){t=t.replace(xr,"."+this._enterClassName),t=t.replace(zr,"."+this._leaveClassName);let l=i!=1,c=this._driver.query(this.element,t,l);i!==0&&(c=i<0?c.slice(c.length+i,c.length):c.slice(0,i)),a.push(...c)}return!r&&a.length==0&&o.push(cs(e)),a}},jt=class s{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(t,e,i,n){this._driver=t,this.element=e,this.startTime=i,this._elementTimelineStylesLookup=n,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(e),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(e,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let e=this._keyframes.size===1&&this._pendingStyles.size;this.duration||e?(this.forwardTime(this.currentTime+t),e&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,e){return this.applyStylesToKeyframe(),new s(this._driver,t,e||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Nr,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,e){this._localTimelineStyles.set(t,e),this._globalTimelineStyles.set(t,e),this._styleSummary.set(t,{time:this.currentTime,value:e})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[e,i]of this._globalTimelineStyles)this._backFill.set(e,i||q),this._currentKeyframe.set(e,q);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,e,i,n){e&&this._previousKeyframe.set("easing",e);let r=n&&n.params||{},o=Br(t,this._globalTimelineStyles);for(let[a,l]of o){let c=Ve(l,r,i);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??q),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,e)=>{this._currentKeyframe.set(e,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,e)=>{this._currentKeyframe.has(e)||this._currentKeyframe.set(e,t)}))}snapshotCurrentStyles(){for(let[t,e]of this._localTimelineStyles)this._pendingStyles.set(t,e),this._updateStyle(t,e)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let e in this._currentKeyframe)t.push(e);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((e,i)=>{let n=this._styleSummary.get(i);(!n||e.time>n.time)&&this._updateStyle(i,e.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,e=new Set,i=this._keyframes.size===1&&this.duration===0,n=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,m)=>{u===$e?t.add(m):u===q&&e.add(m)}),i||c.set("offset",l/this.duration),n.push(c)});let r=[...t.values()],o=[...e.values()];if(i){let a=n[0],l=new Map(a);a.set("offset",0),l.set("offset",1),n=[a,l]}return Ki(this.element,n,r,o,this.duration,this.startTime,this.easing,!1)}},Ri=class extends jt{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(t,e,i,n,r,o,a=!1){super(t,e,o.delay),this.keyframes=i,this.preStyleProps=n,this.postStyleProps=r,this._stretchStartingKeyframe=a,this.timings={duration:o.duration,delay:o.delay,easing:o.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:e,duration:i,easing:n}=this.timings;if(this._stretchStartingKeyframe&&e){let r=[],o=i+e,a=e/o,l=new Map(t[0]);l.set("offset",0),r.push(l);let c=new Map(t[0]);c.set("offset",Os(a)),r.push(c);let u=t.length-1;for(let m=1;m<=u;m++){let v=new Map(t[m]),g=v.get("offset"),d=e+g*i;v.set("offset",Os(d/o)),r.push(v)}i=o,e=0,n="",t=r}return Ki(this.element,t,this.preStyleProps,this.postStyleProps,i,e,n,!0)}};function Os(s,t=3){let e=Math.pow(10,t-1);return Math.round(s*e)/e}function Br(s,t){let e=new Map,i;return s.forEach(n=>{if(n==="*"){i??=t.keys();for(let r of i)e.set(r,q)}else for(let[r,o]of n)e.set(r,o)}),e}function ks(s,t,e,i,n,r,o,a,l,c,u,m,v){return{type:0,element:s,triggerName:t,isRemovalTransition:n,fromState:e,fromStyles:r,toState:i,toStyles:o,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:m,errors:v}}var wi={},qt=class{_triggerName;ast;_stateStyles;constructor(t,e,i){this._triggerName=t,this.ast=e,this._stateStyles=i}match(t,e,i,n){return Vr(this.ast.matchers,t,e,i,n)}buildStyles(t,e,i){let n=this._stateStyles.get("*");return t!==void 0&&(n=this._stateStyles.get(t?.toString())||n),n?n.buildStyles(e,i):new Map}build(t,e,i,n,r,o,a,l,c,u){let m=[],v=this.ast.options&&this.ast.options.params||wi,g=a&&a.params||wi,d=this.buildStyles(i,g,m),_=l&&l.params||wi,w=this.buildStyles(n,_,m),A=new Set,D=new Map,R=new Map,k=n==="void",Pe={params:Ks(_,v),delay:this.ast.options?.delay},U=u?[]:Vs(t,e,this.ast.animation,r,o,d,w,Pe,c,m),N=0;return U.forEach(F=>{N=Math.max(F.duration+F.delay,N)}),m.length?ks(e,this._triggerName,i,n,k,d,w,[],[],D,R,N,m):(U.forEach(F=>{let ue=F.element,Ae=L(D,ue,new Set);F.preStyleProps.forEach(he=>Ae.add(he));let $i=L(R,ue,new Set);F.postStyleProps.forEach(he=>$i.add(he)),ue!==e&&A.add(ue)}),ks(e,this._triggerName,i,n,k,d,w,U,[...A.values()],D,R,N))}};function Vr(s,t,e,i,n){return s.some(r=>r(t,e,i,n))}function Ks(s,t){let e=K({},t);return Object.entries(s).forEach(([i,n])=>{n!=null&&(e[i]=n)}),e}var Ni=class{styles;defaultParams;normalizer;constructor(t,e,i){this.styles=t,this.defaultParams=e,this.normalizer=i}buildStyles(t,e){let i=new Map,n=Ks(t,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((o,a)=>{o&&(o=Ve(o,n,e));let l=this.normalizer.normalizePropertyName(a,e);o=this.normalizer.normalizeStyleValue(a,l,o,e),i.set(a,o)})}),i}};function Kr(s,t,e){return new Fi(s,t,e)}var Fi=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(t,e,i){this.name=t,this.ast=e,this._normalizer=i,e.states.forEach(n=>{let r=n.options&&n.options.params||{};this.states.set(n.name,new Ni(n.style,r,i))}),Is(this.states,"true","1"),Is(this.states,"false","0"),e.transitions.forEach(n=>{this.transitionFactories.push(new qt(t,n,this.states))}),this.fallbackTransition=Qr(t,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,e,i,n){return this.transitionFactories.find(o=>o.match(t,e,i,n))||null}matchStyles(t,e,i){return this.fallbackTransition.buildStyles(t,e,i)}};function Qr(s,t,e){let i=[(o,a)=>!0],n={type:b.Sequence,steps:[],options:null},r={type:b.Transition,animation:n,matchers:i,options:null,queryCount:0,depCount:0};return new qt(s,r,t)}function Is(s,t,e){s.has(t)?s.has(e)||s.set(e,s.get(t)):s.has(e)&&s.set(t,s.get(e))}var jr=new et,xi=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(t,e,i){this.bodyNode=t,this._driver=e,this._normalizer=i}register(t,e){let i=[],n=[],r=Bs(this._driver,e,i,n);if(i.length)throw ds(i);this._animations.set(t,r)}_buildPlayer(t,e,i){let n=t.element,r=gi(this._normalizer,t.keyframes,e,i);return this._driver.animate(n,r,t.duration,t.delay,t.easing,[],!0)}create(t,e,i={}){let n=[],r=this._animations.get(t),o,a=new Map;if(r?(o=Vs(this._driver,e,r,Ti,It,new Map,new Map,i,jr,n),o.forEach(u=>{let m=L(a,u.element,new Map);u.postStyleProps.forEach(v=>m.set(v,null))})):(n.push(ps()),o=[]),n.length)throw fs(n);a.forEach((u,m)=>{u.forEach((v,g)=>{u.set(g,this._driver.computeStyle(m,g,q))})});let l=o.map(u=>{let m=a.get(u.element);return this._buildPlayer(u,new Map,m)}),c=re(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let e=this._getPlayer(t);e.destroy(),this._playersById.delete(t);let i=this.players.indexOf(e);i>=0&&this.players.splice(i,1)}_getPlayer(t){let e=this._playersById.get(t);if(!e)throw gs(t);return e}listen(t,e,i,n){let r=Ot(e,"","","");return Dt(this._getPlayer(t),i,r,n),()=>{}}command(t,e,i,n){if(i=="register"){this.register(t,n[0]);return}if(i=="create"){let o=n[0]||{};this.create(t,e,o);return}let r=this._getPlayer(t);switch(i){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(n[0]));break;case"destroy":this.destroy(t);break}}},Rs="ng-animate-queued",qr=".ng-animate-queued",Ci="ng-animate-disabled",Hr=".ng-animate-disabled",Yr="ng-star-inserted",Ur=".ng-star-inserted",Gr=[],Qs={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},$r={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Y="__ng_removed",tt=class{namespaceId;value;options;get params(){return this.options.params}constructor(t,e=""){this.namespaceId=e;let i=t&&t.hasOwnProperty("value"),n=i?t.value:t;if(this.value=Wr(n),i){let r=t,{value:o}=r,a=en(r,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let e=t.params;if(e){let i=this.options.params;Object.keys(e).forEach(n=>{i[n]==null&&(i[n]=e[n])})}}},Je="void",Pi=new tt(Je),Li=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(t,e,i){this.id=t,this.hostElement=e,this._engine=i,this._hostClassName="ng-tns-"+t,Q(e,this._hostClassName)}listen(t,e,i,n){if(!this._triggers.has(e))throw _s(i,e);if(i==null||i.length==0)throw ys(e);if(!Zr(i))throw vs(i,e);let r=L(this._elementListeners,t,[]),o={name:e,phase:i,callback:n};r.push(o);let a=L(this._engine.statesByElement,t,new Map);return a.has(e)||(Q(t,Xe),Q(t,Xe+"-"+e),a.set(e,Pi)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(o);l>=0&&r.splice(l,1),this._triggers.has(e)||a.delete(e)})}}register(t,e){return this._triggers.has(t)?!1:(this._triggers.set(t,e),!0)}_getTrigger(t){let e=this._triggers.get(t);if(!e)throw bs(t);return e}trigger(t,e,i,n=!0){let r=this._getTrigger(e),o=new it(this.id,e,t),a=this._engine.statesByElement.get(t);a||(Q(t,Xe),Q(t,Xe+"-"+e),this._engine.statesByElement.set(t,a=new Map));let l=a.get(e),c=new tt(i,this.id);if(!(i&&i.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(e,c),l||(l=Pi),!(c.value===Je)&&l.value===c.value){if(!to(l.params,c.params)){let _=[],w=r.matchStyles(l.value,l.params,_),A=r.matchStyles(c.value,c.params,_);_.length?this._engine.reportError(_):this._engine.afterFlush(()=>{me(t,w),H(t,A)})}return}let v=L(this._engine.playersByElement,t,[]);v.forEach(_=>{_.namespaceId==this.id&&_.triggerName==e&&_.queued&&_.destroy()});let g=r.matchTransition(l.value,c.value,t,c.params),d=!1;if(!g){if(!n)return;g=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:e,transition:g,fromState:l,toState:c,player:o,isFallbackTransition:d}),d||(Q(t,Rs),o.onStart(()=>{Ke(t,Rs)})),o.onDone(()=>{let _=this.players.indexOf(o);_>=0&&this.players.splice(_,1);let w=this._engine.playersByElement.get(t);if(w){let A=w.indexOf(o);A>=0&&w.splice(A,1)}}),this.players.push(o),v.push(o),o}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(e=>e.delete(t)),this._elementListeners.forEach((e,i)=>{this._elementListeners.set(i,e.filter(n=>n.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let e=this._engine.playersByElement.get(t);e&&(e.forEach(i=>i.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,e){let i=this._engine.driver.query(t,We,!0);i.forEach(n=>{if(n[Y])return;let r=this._engine.fetchNamespacesByElement(n);r.size?r.forEach(o=>o.triggerLeaveAnimation(n,e,!1,!0)):this.clearElementCache(n)}),this._engine.afterFlushAnimationsDone(()=>i.forEach(n=>this.clearElementCache(n)))}triggerLeaveAnimation(t,e,i,n){let r=this._engine.statesByElement.get(t),o=new Map;if(r){let a=[];if(r.forEach((l,c)=>{if(o.set(c,l.value),this._triggers.has(c)){let u=this.trigger(t,c,Je,n);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,e,o),i&&re(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let e=this._elementListeners.get(t),i=this._engine.statesByElement.get(t);if(e&&i){let n=new Set;e.forEach(r=>{let o=r.name;if(n.has(o))return;n.add(o);let l=this._triggers.get(o).fallbackTransition,c=i.get(o)||Pi,u=new tt(Je),m=new it(this.id,o,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:o,transition:l,fromState:c,toState:u,player:m,isFallbackTransition:!0})})}}removeNode(t,e){let i=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,e),this.triggerLeaveAnimation(t,e,!0))return;let n=!1;if(i.totalAnimations){let r=i.players.length?i.playersByQueriedElement.get(t):[];if(r&&r.length)n=!0;else{let o=t;for(;o=o.parentNode;)if(i.statesByElement.get(o)){n=!0;break}}}if(this.prepareLeaveAnimationListeners(t),n)i.markElementAsRemoved(this.id,t,!1,e);else{let r=t[Y];(!r||r===Qs)&&(i.afterFlush(()=>this.clearElementCache(t)),i.destroyInnerAnimations(t),i._onRemovalComplete(t,e))}}insertNode(t,e){Q(t,this._hostClassName)}drainQueuedTransitions(t){let e=[];return this._queue.forEach(i=>{let n=i.player;if(n.destroyed)return;let r=i.element,o=this._elementListeners.get(r);o&&o.forEach(a=>{if(a.name==i.triggerName){let l=Ot(r,i.triggerName,i.fromState.value,i.toState.value);l._data=t,Dt(i.player,a.phase,l,a.callback)}}),n.markedForDestroy?this._engine.afterFlush(()=>{n.destroy()}):e.push(i)}),this._queue=[],e.sort((i,n)=>{let r=i.transition.ast.depCount,o=n.transition.ast.depCount;return r==0||o==0?r-o:this._engine.driver.containsElement(i.element,n.element)?1:-1})}destroy(t){this.players.forEach(e=>e.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},zi=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(t,e)=>{};_onRemovalComplete(t,e){this.onRemovalComplete(t,e)}constructor(t,e,i){this.bodyNode=t,this.driver=e,this._normalizer=i}get queuedPlayers(){let t=[];return this._namespaceList.forEach(e=>{e.players.forEach(i=>{i.queued&&t.push(i)})}),t}createNamespace(t,e){let i=new Li(t,e,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,e)?this._balanceNamespaceList(i,e):(this.newHostElements.set(e,i),this.collectEnterElement(e)),this._namespaceLookup[t]=i}_balanceNamespaceList(t,e){let i=this._namespaceList,n=this.namespacesByHostElement;if(i.length-1>=0){let o=!1,a=this.driver.getParentElement(e);for(;a;){let l=n.get(a);if(l){let c=i.indexOf(l);i.splice(c+1,0,t),o=!0;break}a=this.driver.getParentElement(a)}o||i.unshift(t)}else i.push(t);return n.set(e,t),t}register(t,e){let i=this._namespaceLookup[t];return i||(i=this.createNamespace(t,e)),i}registerTrigger(t,e,i){let n=this._namespaceLookup[t];n&&n.register(e,i)&&this.totalAnimations++}destroy(t,e){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let i=this._fetchNamespace(t);this.namespacesByHostElement.delete(i.hostElement);let n=this._namespaceList.indexOf(i);n>=0&&this._namespaceList.splice(n,1),i.destroy(e),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let e=new Set,i=this.statesByElement.get(t);if(i){for(let n of i.values())if(n.namespaceId){let r=this._fetchNamespace(n.namespaceId);r&&e.add(r)}}return e}trigger(t,e,i,n){if(zt(e)){let r=this._fetchNamespace(t);if(r)return r.trigger(e,i,n),!0}return!1}insertNode(t,e,i,n){if(!zt(e))return;let r=e[Y];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let o=this.collectedLeaveElements.indexOf(e);o>=0&&this.collectedLeaveElements.splice(o,1)}if(t){let o=this._fetchNamespace(t);o&&o.insertNode(e,i)}n&&this.collectEnterElement(e)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,e){e?this.disabledNodes.has(t)||(this.disabledNodes.add(t),Q(t,Ci)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Ke(t,Ci))}removeNode(t,e,i){if(zt(e)){let n=t?this._fetchNamespace(t):null;n?n.removeNode(e,i):this.markElementAsRemoved(t,e,!1,i);let r=this.namespacesByHostElement.get(e);r&&r.id!==t&&r.removeNode(e,i)}else this._onRemovalComplete(e,i)}markElementAsRemoved(t,e,i,n,r){this.collectedLeaveElements.push(e),e[Y]={namespaceId:t,setForRemoval:n,hasAnimation:i,removedBeforeQueried:!1,previousTriggersValues:r}}listen(t,e,i,n,r){return zt(e)?this._fetchNamespace(t).listen(e,i,n,r):()=>{}}_buildInstruction(t,e,i,n,r){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,i,n,t.fromState.options,t.toState.options,e,r)}destroyInnerAnimations(t){let e=this.driver.query(t,We,!0);e.forEach(i=>this.destroyActiveAnimationsForElement(i)),this.playersByQueriedElement.size!=0&&(e=this.driver.query(t,Rt,!0),e.forEach(i=>this.finishActiveQueriedAnimationOnElement(i)))}destroyActiveAnimationsForElement(t){let e=this.playersByElement.get(t);e&&e.forEach(i=>{i.queued?i.markedForDestroy=!0:i.destroy()})}finishActiveQueriedAnimationOnElement(t){let e=this.playersByQueriedElement.get(t);e&&e.forEach(i=>i.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return re(this.players).onDone(()=>t());t()})}processLeaveNode(t){let e=t[Y];if(e&&e.setForRemoval){if(t[Y]=Qs,e.namespaceId){this.destroyInnerAnimations(t);let i=this._fetchNamespace(e.namespaceId);i&&i.clearElementCache(t)}this._onRemovalComplete(t,e.setForRemoval)}t.classList?.contains(Ci)&&this.markElementAsDisabled(t,!1),this.driver.query(t,Hr,!0).forEach(i=>{this.markElementAsDisabled(i,!1)})}flush(t=-1){let e=[];if(this.newHostElements.size&&(this.newHostElements.forEach((i,n)=>this._balanceNamespaceList(i,n)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let i=0;i<this.collectedEnterElements.length;i++){let n=this.collectedEnterElements[i];Q(n,Yr)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let i=[];try{e=this._flushAnimations(i,t)}finally{for(let n=0;n<i.length;n++)i[n]()}}else for(let i=0;i<this.collectedLeaveElements.length;i++){let n=this.collectedLeaveElements[i];this.processLeaveNode(n)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(i=>i()),this._flushFns=[],this._whenQuietFns.length){let i=this._whenQuietFns;this._whenQuietFns=[],e.length?re(e).onDone(()=>{i.forEach(n=>n())}):i.forEach(n=>n())}}reportError(t){throw Ts(t)}_flushAnimations(t,e){let i=new et,n=[],r=new Map,o=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(h=>{u.add(h);let p=this.driver.query(h,qr,!0);for(let f=0;f<p.length;f++)u.add(p[f])});let m=this.bodyNode,v=Array.from(this.statesByElement.keys()),g=xs(v,this.collectedEnterElements),d=new Map,_=0;g.forEach((h,p)=>{let f=Ti+_++;d.set(p,f),h.forEach(T=>Q(T,f))});let w=[],A=new Set,D=new Set;for(let h=0;h<this.collectedLeaveElements.length;h++){let p=this.collectedLeaveElements[h],f=p[Y];f&&f.setForRemoval&&(w.push(p),A.add(p),f.hasAnimation?this.driver.query(p,Ur,!0).forEach(T=>A.add(T)):D.add(p))}let R=new Map,k=xs(v,Array.from(A));k.forEach((h,p)=>{let f=It+_++;R.set(p,f),h.forEach(T=>Q(T,f))}),t.push(()=>{g.forEach((h,p)=>{let f=d.get(p);h.forEach(T=>Ke(T,f))}),k.forEach((h,p)=>{let f=R.get(p);h.forEach(T=>Ke(T,f))}),w.forEach(h=>{this.processLeaveNode(h)})});let Pe=[],U=[];for(let h=this._namespaceList.length-1;h>=0;h--)this._namespaceList[h].drainQueuedTransitions(e).forEach(f=>{let T=f.player,O=f.element;if(Pe.push(T),this.collectedEnterElements.length){let I=O[Y];if(I&&I.setForMove){if(I.previousTriggersValues&&I.previousTriggersValues.has(f.triggerName)){let de=I.previousTriggersValues.get(f.triggerName),V=this.statesByElement.get(f.element);if(V&&V.has(f.triggerName)){let rt=V.get(f.triggerName);rt.value=de,V.set(f.triggerName,rt)}}T.destroy();return}}let G=!m||!this.driver.containsElement(m,O),B=R.get(O),oe=d.get(O),C=this._buildInstruction(f,i,oe,B,G);if(C.errors&&C.errors.length){U.push(C);return}if(G){T.onStart(()=>me(O,C.fromStyles)),T.onDestroy(()=>H(O,C.toStyles)),n.push(T);return}if(f.isFallbackTransition){T.onStart(()=>me(O,C.fromStyles)),T.onDestroy(()=>H(O,C.toStyles)),n.push(T);return}let Zi=[];C.timelines.forEach(I=>{I.stretchStartingKeyframe=!0,this.disabledNodes.has(I.element)||Zi.push(I)}),C.timelines=Zi,i.append(O,C.timelines);let ur={instruction:C,player:T,element:O};o.push(ur),C.queriedElements.forEach(I=>L(a,I,[]).push(T)),C.preStyleProps.forEach((I,de)=>{if(I.size){let V=l.get(de);V||l.set(de,V=new Set),I.forEach((rt,ei)=>V.add(ei))}}),C.postStyleProps.forEach((I,de)=>{let V=c.get(de);V||c.set(de,V=new Set),I.forEach((rt,ei)=>V.add(ei))})});if(U.length){let h=[];U.forEach(p=>{h.push(Ss(p.triggerName,p.errors))}),Pe.forEach(p=>p.destroy()),this.reportError(h)}let N=new Map,F=new Map;o.forEach(h=>{let p=h.element;i.has(p)&&(F.set(p,p),this._beforeAnimationBuild(h.player.namespaceId,h.instruction,N))}),n.forEach(h=>{let p=h.element;this._getPreviousPlayers(p,!1,h.namespaceId,h.triggerName,null).forEach(T=>{L(N,p,[]).push(T),T.destroy()})});let ue=w.filter(h=>Ls(h,l,c)),Ae=new Map;Fs(Ae,this.driver,D,c,q).forEach(h=>{Ls(h,l,c)&&ue.push(h)});let he=new Map;g.forEach((h,p)=>{Fs(he,this.driver,new Set(h),l,$e)}),ue.forEach(h=>{let p=Ae.get(h),f=he.get(h);Ae.set(h,new Map([...p?.entries()??[],...f?.entries()??[]]))});let Jt=[],Xi=[],Wi={};o.forEach(h=>{let{element:p,player:f,instruction:T}=h;if(i.has(p)){if(u.has(p)){f.onDestroy(()=>H(p,T.toStyles)),f.disabled=!0,f.overrideTotalTime(T.totalTime),n.push(f);return}let O=Wi;if(F.size>1){let B=p,oe=[];for(;B=B.parentNode;){let C=F.get(B);if(C){O=C;break}oe.push(B)}oe.forEach(C=>F.set(C,O))}let G=this._buildAnimation(f.namespaceId,T,N,r,he,Ae);if(f.setRealPlayer(G),O===Wi)Jt.push(f);else{let B=this.playersByElement.get(O);B&&B.length&&(f.parentPlayer=re(B)),n.push(f)}}else me(p,T.fromStyles),f.onDestroy(()=>H(p,T.toStyles)),Xi.push(f),u.has(p)&&n.push(f)}),Xi.forEach(h=>{let p=r.get(h.element);if(p&&p.length){let f=re(p);h.setRealPlayer(f)}}),n.forEach(h=>{h.parentPlayer?h.syncPlayerEvents(h.parentPlayer):h.destroy()});for(let h=0;h<w.length;h++){let p=w[h],f=p[Y];if(Ke(p,It),f&&f.hasAnimation)continue;let T=[];if(a.size){let G=a.get(p);G&&G.length&&T.push(...G);let B=this.driver.query(p,Rt,!0);for(let oe=0;oe<B.length;oe++){let C=a.get(B[oe]);C&&C.length&&T.push(...C)}}let O=T.filter(G=>!G.destroyed);O.length?Jr(this,p,O):this.processLeaveNode(p)}return w.length=0,Jt.forEach(h=>{this.players.push(h),h.onDone(()=>{h.destroy();let p=this.players.indexOf(h);this.players.splice(p,1)}),h.play()}),Jt}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,e,i,n,r){let o=[];if(e){let a=this.playersByQueriedElement.get(t);a&&(o=a)}else{let a=this.playersByElement.get(t);if(a){let l=!r||r==Je;a.forEach(c=>{c.queued||!l&&c.triggerName!=n||o.push(c)})}}return(i||n)&&(o=o.filter(a=>!(i&&i!=a.namespaceId||n&&n!=a.triggerName))),o}_beforeAnimationBuild(t,e,i){let n=e.triggerName,r=e.element,o=e.isRemovalTransition?void 0:t,a=e.isRemovalTransition?void 0:n;for(let l of e.timelines){let c=l.element,u=c!==r,m=L(i,c,[]);this._getPreviousPlayers(c,u,o,a,e.toState).forEach(g=>{let d=g.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),g.destroy(),m.push(g)})}me(r,e.fromStyles)}_buildAnimation(t,e,i,n,r,o){let a=e.triggerName,l=e.element,c=[],u=new Set,m=new Set,v=e.timelines.map(d=>{let _=d.element;u.add(_);let w=_[Y];if(w&&w.removedBeforeQueried)return new se(d.duration,d.delay);let A=_!==l,D=eo((i.get(_)||Gr).map(N=>N.getRealPlayer())).filter(N=>{let F=N;return F.element?F.element===_:!1}),R=r.get(_),k=o.get(_),Pe=gi(this._normalizer,d.keyframes,R,k),U=this._buildPlayer(d,Pe,D);if(d.subTimeline&&n&&m.add(_),A){let N=new it(t,a,_);N.setRealPlayer(U),c.push(N)}return U});c.forEach(d=>{L(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>Xr(this.playersByQueriedElement,d.element,d))}),u.forEach(d=>Q(d,Si));let g=re(v);return g.onDestroy(()=>{u.forEach(d=>Ke(d,Si)),H(l,e.toStyles)}),m.forEach(d=>{L(n,d,[]).push(g)}),g}_buildPlayer(t,e,i){return e.length>0?this.driver.animate(t.element,e,t.duration,t.delay,t.easing,i):new se(t.duration,t.delay)}},it=class{namespaceId;triggerName;element;_player=new se;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(t,e,i){this.namespaceId=t,this.triggerName=e,this.element=i}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((e,i)=>{e.forEach(n=>Dt(t,i,void 0,n))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let e=this._player;e.triggerCallback&&t.onStart(()=>e.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,e){L(this._queuedCallbacks,t,[]).push(e)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let e=this._player;e.triggerCallback&&e.triggerCallback(t)}};function Xr(s,t,e){let i=s.get(t);if(i){if(i.length){let n=i.indexOf(e);i.splice(n,1)}i.length==0&&s.delete(t)}return i}function Wr(s){return s??null}function zt(s){return s&&s.nodeType===1}function Zr(s){return s=="start"||s=="done"}function Ns(s,t){let e=s.style.display;return s.style.display=t??"none",e}function Fs(s,t,e,i,n){let r=[];e.forEach(l=>r.push(Ns(l)));let o=[];i.forEach((l,c)=>{let u=new Map;l.forEach(m=>{let v=t.computeStyle(c,m,n);u.set(m,v),(!v||v.length==0)&&(c[Y]=$r,o.push(c))}),s.set(c,u)});let a=0;return e.forEach(l=>Ns(l,r[a++])),o}function xs(s,t){let e=new Map;if(s.forEach(a=>e.set(a,[])),t.length==0)return e;let i=1,n=new Set(t),r=new Map;function o(a){if(!a)return i;let l=r.get(a);if(l)return l;let c=a.parentNode;return e.has(c)?l=c:n.has(c)?l=i:l=o(c),r.set(a,l),l}return t.forEach(a=>{let l=o(a);l!==i&&e.get(l).push(a)}),e}function Q(s,t){s.classList?.add(t)}function Ke(s,t){s.classList?.remove(t)}function Jr(s,t,e){re(e).onDone(()=>s.processLeaveNode(t))}function eo(s){let t=[];return js(s,t),t}function js(s,t){for(let e=0;e<s.length;e++){let i=s[e];i instanceof ze?js(i.players,t):t.push(i)}}function to(s,t){let e=Object.keys(s),i=Object.keys(t);if(e.length!=i.length)return!1;for(let n=0;n<e.length;n++){let r=e[n];if(!t.hasOwnProperty(r)||s[r]!==t[r])return!1}return!0}function Ls(s,t,e){let i=e.get(s);if(!i)return!1;let n=t.get(s);return n?i.forEach(r=>n.add(r)):t.set(s,i),e.delete(s),!0}var Qe=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(t,e)=>{};constructor(t,e,i){this._driver=e,this._normalizer=i,this._transitionEngine=new zi(t.body,e,i),this._timelineEngine=new xi(t.body,e,i),this._transitionEngine.onRemovalComplete=(n,r)=>this.onRemovalComplete(n,r)}registerTrigger(t,e,i,n,r){let o=t+"-"+n,a=this._triggerCache[o];if(!a){let l=[],c=[],u=Bs(this._driver,r,l,c);if(l.length)throw hs(n,l);a=Kr(n,u,this._normalizer),this._triggerCache[o]=a}this._transitionEngine.registerTrigger(e,n,a)}register(t,e){this._transitionEngine.register(t,e)}destroy(t,e){this._transitionEngine.destroy(t,e)}onInsert(t,e,i,n){this._transitionEngine.insertNode(t,e,i,n)}onRemove(t,e,i){this._transitionEngine.removeNode(t,e,i)}disableAnimations(t,e){this._transitionEngine.markElementAsDisabled(t,e)}process(t,e,i,n){if(i.charAt(0)=="@"){let[r,o]=_i(i),a=n;this._timelineEngine.command(r,e,o,a)}else this._transitionEngine.trigger(t,e,i,n)}listen(t,e,i,n,r){if(i.charAt(0)=="@"){let[o,a]=_i(i);return this._timelineEngine.listen(o,e,a,r)}return this._transitionEngine.listen(t,e,i,n,r)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function io(s,t){let e=null,i=null;return Array.isArray(t)&&t.length?(e=Ai(t[0]),t.length>1&&(i=Ai(t[t.length-1]))):t instanceof Map&&(e=Ai(t)),e||i?new no(s,e,i):null}var no=(()=>{class s{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(e,i,n){this._element=e,this._startStyles=i,this._endStyles=n;let r=s.initialStylesByElement.get(e);r||s.initialStylesByElement.set(e,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&H(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(H(this._element,this._initialStyles),this._endStyles&&(H(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(s.initialStylesByElement.delete(this._element),this._startStyles&&(me(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(me(this._element,this._endStyles),this._endStyles=null),H(this._element,this._initialStyles),this._state=3)}}return s})();function Ai(s){let t=null;return s.forEach((e,i)=>{so(i)&&(t=t||new Map,t.set(i,e))}),t}function so(s){return s==="display"||s==="position"}var Ht=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(t,e,i,n){this.element=t,this.keyframes=e,this.options=i,this._specialStyles=n,this._duration=i.duration,this._delay=i.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let e=()=>this._onFinish();this.domPlayer.addEventListener("finish",e),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",e)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let e=[];return t.forEach(i=>{e.push(Object.fromEntries(i))}),e}_triggerWebAnimation(t,e,i){return t.animate(this._convertKeyframesToObject(e),i)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((i,n)=>{n!=="offset"&&t.set(n,this._finished?i:Ft(this.element,n))}),this.currentSnapshot=t}triggerCallback(t){let e=t==="start"?this._onStartFns:this._onDoneFns;e.forEach(i=>i()),e.length=0}},Yt=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,e){return yi(t,e)}getParentElement(t){return kt(t)}query(t,e,i){return vi(t,e,i)}computeStyle(t,e,i){return Ft(t,e)}animate(t,e,i,n,r,o=[]){let a=n==0?"both":"forwards",l={duration:i,delay:n,fill:a};r&&(l.easing=r);let c=new Map,u=o.filter(g=>g instanceof Ht);Cs(i,n)&&u.forEach(g=>{g.currentSnapshot.forEach((d,_)=>c.set(_,d))});let m=Es(e).map(g=>new Map(g));m=Ps(t,m,c);let v=io(t,m);return new Ht(t,m,l,v)}};var Bt="@",qs="@.disabled",Ut=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(t,e,i,n){this.namespaceId=t,this.delegate=e,this.engine=i,this._onDestroy=n}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,e){return this.delegate.createElement(t,e)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,e){this.delegate.appendChild(t,e),this.engine.onInsert(this.namespaceId,e,t,!1)}insertBefore(t,e,i,n=!0){this.delegate.insertBefore(t,e,i),this.engine.onInsert(this.namespaceId,e,t,n)}removeChild(t,e,i){this.parentNode(e)&&this.engine.onRemove(this.namespaceId,e,this.delegate)}selectRootElement(t,e){return this.delegate.selectRootElement(t,e)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,e,i,n){this.delegate.setAttribute(t,e,i,n)}removeAttribute(t,e,i){this.delegate.removeAttribute(t,e,i)}addClass(t,e){this.delegate.addClass(t,e)}removeClass(t,e){this.delegate.removeClass(t,e)}setStyle(t,e,i,n){this.delegate.setStyle(t,e,i,n)}removeStyle(t,e,i){this.delegate.removeStyle(t,e,i)}setProperty(t,e,i){e.charAt(0)==Bt&&e==qs?this.disableAnimations(t,!!i):this.delegate.setProperty(t,e,i)}setValue(t,e){this.delegate.setValue(t,e)}listen(t,e,i,n){return this.delegate.listen(t,e,i,n)}disableAnimations(t,e){this.engine.disableAnimations(t,e)}},Bi=class extends Ut{factory;constructor(t,e,i,n,r){super(e,i,n,r),this.factory=t,this.namespaceId=e}setProperty(t,e,i){e.charAt(0)==Bt?e.charAt(1)=="."&&e==qs?(i=i===void 0?!0:!!i,this.disableAnimations(t,i)):this.engine.process(this.namespaceId,t,e.slice(1),i):this.delegate.setProperty(t,e,i)}listen(t,e,i,n){if(e.charAt(0)==Bt){let r=ro(t),o=e.slice(1),a="";return o.charAt(0)!=Bt&&([o,a]=oo(o)),this.engine.listen(this.namespaceId,r,o,a,l=>{let c=l._data||-1;this.factory.scheduleListenerCallback(c,i,l)})}return this.delegate.listen(t,e,i,n)}};function ro(s){switch(s){case"body":return document.body;case"document":return document;case"window":return window;default:return s}}function oo(s){let t=s.indexOf("."),e=s.substring(0,t),i=s.slice(t+1);return[e,i]}var Gt=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(t,e,i){this.delegate=t,this.engine=e,this._zone=i,e.onRemovalComplete=(n,r)=>{r?.removeChild(null,n)}}createRenderer(t,e){let i="",n=this.delegate.createRenderer(t,e);if(!t||!e?.data?.animation){let c=this._rendererCache,u=c.get(n);if(!u){let m=()=>c.delete(n);u=new Ut(i,n,this.engine,m),c.set(n,u)}return u}let r=e.id,o=e.id+"-"+this._currentId;this._currentId++,this.engine.register(o,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(r,o,t,c.name,c)};return e.data.animation.forEach(a),new Bi(this,o,n,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,e,i){if(t>=0&&t<this._microtaskId){this._zone.run(()=>e(i));return}let n=this._animationCallbacksBuffer;n.length==0&&queueMicrotask(()=>{this._zone.run(()=>{n.forEach(r=>{let[o,a]=r;o(a)}),this._animationCallbacksBuffer=[]})}),n.push([e,i])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(t){this.engine.flush(),this.delegate.componentReplaced?.(t)}};var lo=(()=>{class s extends Qe{constructor(e,i,n){super(e,i,n)}ngOnDestroy(){this.flush()}static \u0275fac=function(i){return new(i||s)(ke(le),ke(Ee),ke(we))};static \u0275prov=Oe({token:s,factory:s.\u0275fac})}return s})();function co(){return new Vt}function mo(s,t,e){return new Gt(s,t,e)}var Hs=[{provide:we,useFactory:co},{provide:Qe,useClass:lo},{provide:mn,useFactory:mo,deps:[En,Qe,Ie]}],pa=[{provide:Ee,useClass:Vi},{provide:si,useValue:"NoopAnimations"},...Hs],uo=[{provide:Ee,useFactory:()=>new Yt},{provide:si,useFactory:()=>"BrowserAnimations"},...Hs];function Ys(){return pn("NgEagerAnimations"),[...uo]}var Us=[{path:"",redirectTo:"/home",pathMatch:"full"},{path:"home",loadComponent:()=>import("./chunk-BOR5POER.js").then(s=>s.HomeComponent)},{path:"about",loadComponent:()=>import("./chunk-D7PVUTPY.js").then(s=>s.AboutComponent)},{path:"services",loadComponent:()=>import("./chunk-LOUXUTWO.js").then(s=>s.ServicesComponent)},{path:"blog",loadComponent:()=>import("./chunk-6FWD3OOC.js").then(s=>s.BlogComponent)},{path:"contact",loadComponent:()=>import("./chunk-AYTAXP6B.js").then(s=>s.ContactComponent)},{path:"pricing",loadComponent:()=>import("./chunk-P3WMXKUH.js").then(s=>s.PricingComponent)},{path:"interview",loadComponent:()=>import("./chunk-J6BQSAFJ.js").then(s=>s.InterviewComponent)},{path:"case-studies",loadComponent:()=>import("./chunk-QMTVJ5RG.js").then(s=>s.CaseStudiesComponent)},{path:"**",redirectTo:"/home"}];var Gs={providers:[rn(),Sn({eventCoalescing:!0}),Rn(Us),Dn(An()),Ys(),Cn(Pn())]};var ho=["*",[["mat-toolbar-row"]]],po=["*","mat-toolbar-row"],fo=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275dir=ve({type:s,selectors:[["mat-toolbar-row"]],hostAttrs:[1,"mat-toolbar-row"],exportAs:["matToolbarRow"]})}return s})(),$s=(()=>{class s{_elementRef=y(X);_platform=y(yt);_document=y(le);color;_toolbarRows;constructor(){}ngAfterViewInit(){this._platform.isBrowser&&(this._checkToolbarMixedModes(),this._toolbarRows.changes.subscribe(()=>this._checkToolbarMixedModes()))}_checkToolbarMixedModes(){this._toolbarRows.length}static \u0275fac=function(i){return new(i||s)};static \u0275cmp=j({type:s,selectors:[["mat-toolbar"]],contentQueries:function(i,n,r){if(i&1&&Ne(r,fo,5),i&2){let o;te(o=ie())&&(n._toolbarRows=o)}},hostAttrs:[1,"mat-toolbar"],hostVars:6,hostBindings:function(i,n){i&2&&(pt(n.color?"mat-"+n.color:""),Z("mat-toolbar-multiple-rows",n._toolbarRows.length>0)("mat-toolbar-single-row",n._toolbarRows.length===0))},inputs:{color:"color"},exportAs:["matToolbar"],ngContentSelectors:po,decls:2,vars:0,template:function(i,n){i&1&&(Ye(ho),Te(0),Te(1,1))},styles:[`.mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}
`],encapsulation:2,changeDetection:0})}return s})();var Xs=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275mod=ye({type:s});static \u0275inj=fe({imports:[ne,ne]})}return s})();var To=["mat-menu-item",""],So=[[["mat-icon"],["","matMenuItemIcon",""]],"*"],Mo=["mat-icon, [matMenuItemIcon]","*"];function Eo(s,t){s&1&&(sn(),M(0,"svg",2),be(1,"polygon",3),E())}var wo=["*"];function Co(s,t){if(s&1){let e=ht();ai(0,"div",0),vn("click",function(){ge(e);let n=Re();return _e(n.closed.emit("click"))})("animationstart",function(n){ge(e);let r=Re();return _e(r._onAnimationStart(n.animationName))})("animationend",function(n){ge(e);let r=Re();return _e(r._onAnimationDone(n.animationName))})("animationcancel",function(n){ge(e);let r=Re();return _e(r._onAnimationDone(n.animationName))}),ai(1,"div",1),Te(2),_n()()}if(s&2){let e=Re();pt(e._classList),Z("mat-menu-panel-animations-disabled",e._animationsDisabled)("mat-menu-panel-exit-animation",e._panelAnimationState==="void")("mat-menu-panel-animating",e._isAnimating),yn("id",e.panelId),He("aria-label",e.ariaLabel||null)("aria-labelledby",e.ariaLabelledby||null)("aria-describedby",e.ariaDescribedby||null)}}var qi=new ae("MAT_MENU_PANEL"),st=(()=>{class s{_elementRef=y(X);_document=y(le);_focusMonitor=y(Ge);_parentMenu=y(qi,{optional:!0});_changeDetectorRef=y(Fe);role="menuitem";disabled=!1;disableRipple=!1;_hovered=new pe;_focused=new pe;_highlighted=!1;_triggersSubmenu=!1;constructor(){y(Ln).load(jn),this._parentMenu?.addItem?.(this)}focus(e,i){this._focusMonitor&&e?this._focusMonitor.focusVia(this._getHostElement(),e,i):this._getHostElement().focus(i),this._focused.next(this)}ngAfterViewInit(){this._focusMonitor&&this._focusMonitor.monitor(this._elementRef,!1)}ngOnDestroy(){this._focusMonitor&&this._focusMonitor.stopMonitoring(this._elementRef),this._parentMenu&&this._parentMenu.removeItem&&this._parentMenu.removeItem(this),this._hovered.complete(),this._focused.complete()}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._elementRef.nativeElement}_checkDisabled(e){this.disabled&&(e.preventDefault(),e.stopPropagation())}_handleMouseEnter(){this._hovered.next(this)}getLabel(){let e=this._elementRef.nativeElement.cloneNode(!0),i=e.querySelectorAll("mat-icon, .material-icons");for(let n=0;n<i.length;n++)i[n].remove();return e.textContent?.trim()||""}_setHighlighted(e){this._highlighted=e,this._changeDetectorRef.markForCheck()}_setTriggersSubmenu(e){this._triggersSubmenu=e,this._changeDetectorRef.markForCheck()}_hasFocus(){return this._document&&this._document.activeElement===this._getHostElement()}static \u0275fac=function(i){return new(i||s)};static \u0275cmp=j({type:s,selectors:[["","mat-menu-item",""]],hostAttrs:[1,"mat-mdc-menu-item","mat-focus-indicator"],hostVars:8,hostBindings:function(i,n){i&1&&ce("click",function(o){return n._checkDisabled(o)})("mouseenter",function(){return n._handleMouseEnter()}),i&2&&(He("role",n.role)("tabindex",n._getTabIndex())("aria-disabled",n.disabled)("disabled",n.disabled||null),Z("mat-mdc-menu-item-highlighted",n._highlighted)("mat-mdc-menu-item-submenu-trigger",n._triggersSubmenu))},inputs:{role:"role",disabled:[2,"disabled","disabled",Ue],disableRipple:[2,"disableRipple","disableRipple",Ue]},exportAs:["matMenuItem"],attrs:To,ngContentSelectors:Mo,decls:5,vars:3,consts:[[1,"mat-mdc-menu-item-text"],["matRipple","",1,"mat-mdc-menu-ripple",3,"matRippleDisabled","matRippleTrigger"],["viewBox","0 0 5 10","focusable","false","aria-hidden","true",1,"mat-mdc-menu-submenu-icon"],["points","0,0 5,5 0,10"]],template:function(i,n){i&1&&(Ye(So),Te(0),M(1,"span",0),Te(2,1),E(),be(3,"div",1),mt(4,Eo,2,0,":svg:svg",2)),i&2&&(x(3),W("matRippleDisabled",n.disableRipple||n.disabled)("matRippleTrigger",n._getHostElement()),x(),ut(n._triggersSubmenu?4:-1))},dependencies:[Qn],encapsulation:2,changeDetection:0})}return s})();var Po=new ae("MatMenuContent");var Ao=new ae("mat-menu-default-options",{providedIn:"root",factory:Do});function Do(){return{overlapTrigger:!1,xPosition:"after",yPosition:"below",backdropClass:"cdk-overlay-transparent-backdrop"}}var Qi="_mat-menu-enter",$t="_mat-menu-exit",je=(()=>{class s{_elementRef=y(X);_changeDetectorRef=y(Fe);_injector=y(ee);_keyManager;_xPosition;_yPosition;_firstItemFocusRef;_exitFallbackTimeout;_animationsDisabled=xe();_allItems;_directDescendantItems=new an;_classList={};_panelAnimationState="void";_animationDone=new pe;_isAnimating=!1;parentMenu;direction;overlayPanelClass;backdropClass;ariaLabel;ariaLabelledby;ariaDescribedby;get xPosition(){return this._xPosition}set xPosition(e){this._xPosition=e,this.setPositionClasses()}get yPosition(){return this._yPosition}set yPosition(e){this._yPosition=e,this.setPositionClasses()}templateRef;items;lazyContent;overlapTrigger;hasBackdrop;set panelClass(e){let i=this._previousPanelClass,n=K({},this._classList);i&&i.length&&i.split(" ").forEach(r=>{n[r]=!1}),this._previousPanelClass=e,e&&e.length&&(e.split(" ").forEach(r=>{n[r]=!0}),this._elementRef.nativeElement.className=""),this._classList=n}_previousPanelClass;get classList(){return this.panelClass}set classList(e){this.panelClass=e}closed=new ct;close=this.closed;panelId=y(Bn).getId("mat-menu-panel-");constructor(){let e=y(Ao);this.overlayPanelClass=e.overlayPanelClass||"",this._xPosition=e.xPosition,this._yPosition=e.yPosition,this.backdropClass=e.backdropClass,this.overlapTrigger=e.overlapTrigger,this.hasBackdrop=e.hasBackdrop}ngOnInit(){this.setPositionClasses()}ngAfterContentInit(){this._updateDirectDescendants(),this._keyManager=new Vn(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd(),this._keyManager.tabOut.subscribe(()=>this.closed.emit("tab")),this._directDescendantItems.changes.pipe(at(this._directDescendantItems),ni(e=>ot(...e.map(i=>i._focused)))).subscribe(e=>this._keyManager.updateActiveItem(e)),this._directDescendantItems.changes.subscribe(e=>{let i=this._keyManager;if(this._panelAnimationState==="enter"&&i.activeItem?._hasFocus()){let n=e.toArray(),r=Math.max(0,Math.min(n.length-1,i.activeItemIndex||0));n[r]&&!n[r].disabled?i.setActiveItem(r):i.setNextItemActive()}})}ngOnDestroy(){this._keyManager?.destroy(),this._directDescendantItems.destroy(),this.closed.complete(),this._firstItemFocusRef?.destroy(),clearTimeout(this._exitFallbackTimeout)}_hovered(){return this._directDescendantItems.changes.pipe(at(this._directDescendantItems),ni(i=>ot(...i.map(n=>n._hovered))))}addItem(e){}removeItem(e){}_handleKeydown(e){let i=e.keyCode,n=this._keyManager;switch(i){case 27:vt(e)||(e.preventDefault(),this.closed.emit("keydown"));break;case 37:this.parentMenu&&this.direction==="ltr"&&this.closed.emit("keydown");break;case 39:this.parentMenu&&this.direction==="rtl"&&this.closed.emit("keydown");break;default:(i===38||i===40)&&n.setFocusOrigin("keyboard"),n.onKeydown(e);return}}focusFirstItem(e="program"){this._firstItemFocusRef?.destroy(),this._firstItemFocusRef=qe(()=>{let i=this._resolvePanel();if(!i||!i.contains(document.activeElement)){let n=this._keyManager;n.setFocusOrigin(e).setFirstItemActive(),!n.activeItem&&i&&i.focus()}},{injector:this._injector})}resetActiveItem(){this._keyManager.setActiveItem(-1)}setElevation(e){}setPositionClasses(e=this.xPosition,i=this.yPosition){this._classList=Ji(K({},this._classList),{"mat-menu-before":e==="before","mat-menu-after":e==="after","mat-menu-above":i==="above","mat-menu-below":i==="below"}),this._changeDetectorRef.markForCheck()}_onAnimationDone(e){let i=e===$t;(i||e===Qi)&&(i&&(clearTimeout(this._exitFallbackTimeout),this._exitFallbackTimeout=void 0),this._animationDone.next(i?"void":"enter"),this._isAnimating=!1)}_onAnimationStart(e){(e===Qi||e===$t)&&(this._isAnimating=!0)}_setIsOpen(e){if(this._panelAnimationState=e?"enter":"void",e){if(this._keyManager.activeItemIndex===0){let i=this._resolvePanel();i&&(i.scrollTop=0)}}else this._animationsDisabled||(this._exitFallbackTimeout=setTimeout(()=>this._onAnimationDone($t),200));this._animationsDisabled&&setTimeout(()=>{this._onAnimationDone(e?Qi:$t)}),this._changeDetectorRef.markForCheck()}_updateDirectDescendants(){this._allItems.changes.pipe(at(this._allItems)).subscribe(e=>{this._directDescendantItems.reset(e.filter(i=>i._parentMenu===this)),this._directDescendantItems.notifyOnChanges()})}_resolvePanel(){let e=null;return this._directDescendantItems.length&&(e=this._directDescendantItems.first._getHostElement().closest('[role="menu"]')),e}static \u0275fac=function(i){return new(i||s)};static \u0275cmp=j({type:s,selectors:[["mat-menu"]],contentQueries:function(i,n,r){if(i&1&&(Ne(r,Po,5),Ne(r,st,5),Ne(r,st,4)),i&2){let o;te(o=ie())&&(n.lazyContent=o.first),te(o=ie())&&(n._allItems=o),te(o=ie())&&(n.items=o)}},viewQuery:function(i,n){if(i&1&&dt(cn,5),i&2){let r;te(r=ie())&&(n.templateRef=r.first)}},hostVars:3,hostBindings:function(i,n){i&2&&He("aria-label",null)("aria-labelledby",null)("aria-describedby",null)},inputs:{backdropClass:"backdropClass",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],xPosition:"xPosition",yPosition:"yPosition",overlapTrigger:[2,"overlapTrigger","overlapTrigger",Ue],hasBackdrop:[2,"hasBackdrop","hasBackdrop",e=>e==null?null:Ue(e)],panelClass:[0,"class","panelClass"],classList:"classList"},outputs:{closed:"closed",close:"close"},exportAs:["matMenu"],features:[Tn([{provide:qi,useExisting:s}])],ngContentSelectors:wo,decls:1,vars:0,consts:[["tabindex","-1","role","menu",1,"mat-mdc-menu-panel",3,"click","animationstart","animationend","animationcancel","id"],[1,"mat-mdc-menu-content"]],template:function(i,n){i&1&&(Ye(),gn(0,Co,3,12,"ng-template"))},styles:[`mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:"";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}
`],encapsulation:2,changeDetection:0})}return s})(),Js=new ae("mat-menu-scroll-strategy",{providedIn:"root",factory:()=>{let s=y(ee);return()=>Le(s)}});function Oo(s){let t=y(ee);return()=>Le(t)}var ko={provide:Js,deps:[],useFactory:Oo};var nt=new WeakMap,Io=(()=>{class s{_canHaveBackdrop;_element=y(X);_viewContainerRef=y(lt);_menuItemInstance=y(st,{optional:!0,self:!0});_dir=y(Tt,{optional:!0});_focusMonitor=y(Ge);_ngZone=y(Ie);_injector=y(ee);_scrollStrategy=y(Js);_changeDetectorRef=y(Fe);_animationsDisabled=xe();_portal;_overlayRef=null;_menuOpen=!1;_closingActionsSubscription=De.EMPTY;_menuCloseSubscription=De.EMPTY;_pendingRemoval;_parentMaterialMenu;_parentInnerPadding;_openedBy=void 0;get _menu(){return this._menuInternal}set _menu(e){e!==this._menuInternal&&(this._menuInternal=e,this._menuCloseSubscription.unsubscribe(),e&&(this._parentMaterialMenu,this._menuCloseSubscription=e.close.subscribe(i=>{this._destroyMenu(i),(i==="click"||i==="tab")&&this._parentMaterialMenu&&this._parentMaterialMenu.closed.emit(i)})),this._menuItemInstance?._setTriggersSubmenu(this._triggersSubmenu()))}_menuInternal;constructor(e){this._canHaveBackdrop=e;let i=y(qi,{optional:!0});this._parentMaterialMenu=i instanceof je?i:void 0}ngOnDestroy(){this._menu&&this._ownsMenu(this._menu)&&nt.delete(this._menu),this._pendingRemoval?.unsubscribe(),this._menuCloseSubscription.unsubscribe(),this._closingActionsSubscription.unsubscribe(),this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=null)}get menuOpen(){return this._menuOpen}get dir(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}_triggersSubmenu(){return!!(this._menuItemInstance&&this._parentMaterialMenu&&this._menu)}_closeMenu(){this._menu?.close.emit()}_openMenu(e){let i=this._menu;if(this._menuOpen||!i)return;this._pendingRemoval?.unsubscribe();let n=nt.get(i);nt.set(i,this),n&&n!==this&&n._closeMenu();let r=this._createOverlay(i),o=r.getConfig(),a=o.positionStrategy;this._setPosition(i,a),this._canHaveBackdrop?o.hasBackdrop=i.hasBackdrop==null?!this._triggersSubmenu():i.hasBackdrop:o.hasBackdrop=!1,r.hasAttached()||(r.attach(this._getPortal(i)),i.lazyContent?.attach(this.menuData)),this._closingActionsSubscription=this._menuClosingActions().subscribe(()=>this._closeMenu()),i.parentMenu=this._triggersSubmenu()?this._parentMaterialMenu:void 0,i.direction=this.dir,e&&i.focusFirstItem(this._openedBy||"program"),this._setIsMenuOpen(!0),i instanceof je&&(i._setIsOpen(!0),i._directDescendantItems.changes.pipe($(i.close)).subscribe(()=>{a.withLockedPosition(!1).reapplyLastPosition(),a.withLockedPosition(!0)}))}focus(e,i){this._focusMonitor&&e?this._focusMonitor.focusVia(this._element,e,i):this._element.nativeElement.focus(i)}_destroyMenu(e){let i=this._overlayRef,n=this._menu;!i||!this.menuOpen||(this._closingActionsSubscription.unsubscribe(),this._pendingRemoval?.unsubscribe(),n instanceof je&&this._ownsMenu(n)?(this._pendingRemoval=n._animationDone.pipe(nn(1)).subscribe(()=>{i.detach(),n.lazyContent?.detach()}),n._setIsOpen(!1)):(i.detach(),n?.lazyContent?.detach()),n&&this._ownsMenu(n)&&nt.delete(n),this.restoreFocus&&(e==="keydown"||!this._openedBy||!this._triggersSubmenu())&&this.focus(this._openedBy),this._openedBy=void 0,this._setIsMenuOpen(!1))}_setIsMenuOpen(e){e!==this._menuOpen&&(this._menuOpen=e,this._menuOpen?this.menuOpened.emit():this.menuClosed.emit(),this._triggersSubmenu()&&this._menuItemInstance._setHighlighted(e),this._changeDetectorRef.markForCheck())}_createOverlay(e){if(!this._overlayRef){let i=this._getOverlayConfig(e);this._subscribeToPositions(e,i.positionStrategy),this._overlayRef=Pt(this._injector,i),this._overlayRef.keydownEvents().subscribe(n=>{this._menu instanceof je&&this._menu._handleKeydown(n)})}return this._overlayRef}_getOverlayConfig(e){return new Gn({positionStrategy:Ct(this._injector,this._getOverlayOrigin()).withLockedPosition().withGrowAfterOpen().withTransformOriginOn(".mat-menu-panel, .mat-mdc-menu-panel"),backdropClass:e.backdropClass||"cdk-overlay-transparent-backdrop",panelClass:e.overlayPanelClass,scrollStrategy:this._scrollStrategy(),direction:this._dir||"ltr",disableAnimations:this._animationsDisabled})}_subscribeToPositions(e,i){e.setPositionClasses&&i.positionChanges.subscribe(n=>{this._ngZone.run(()=>{let r=n.connectionPair.overlayX==="start"?"after":"before",o=n.connectionPair.overlayY==="top"?"below":"above";e.setPositionClasses(r,o)})})}_setPosition(e,i){let[n,r]=e.xPosition==="before"?["end","start"]:["start","end"],[o,a]=e.yPosition==="above"?["bottom","top"]:["top","bottom"],[l,c]=[o,a],[u,m]=[n,r],v=0;if(this._triggersSubmenu()){if(m=n=e.xPosition==="before"?"start":"end",r=u=n==="end"?"start":"end",this._parentMaterialMenu){if(this._parentInnerPadding==null){let g=this._parentMaterialMenu.items.first;this._parentInnerPadding=g?g._getHostElement().offsetTop:0}v=o==="bottom"?this._parentInnerPadding:-this._parentInnerPadding}}else e.overlapTrigger||(l=o==="top"?"bottom":"top",c=a==="top"?"bottom":"top");i.withPositions([{originX:n,originY:l,overlayX:u,overlayY:o,offsetY:v},{originX:r,originY:l,overlayX:m,overlayY:o,offsetY:v},{originX:n,originY:c,overlayX:u,overlayY:a,offsetY:-v},{originX:r,originY:c,overlayX:m,overlayY:a,offsetY:-v}])}_menuClosingActions(){let e=this._getOutsideClickStream(this._overlayRef),i=this._overlayRef.detachments(),n=this._parentMaterialMenu?this._parentMaterialMenu.closed:ii(),r=this._parentMaterialMenu?this._parentMaterialMenu._hovered().pipe(tn(o=>this._menuOpen&&o!==this._menuItemInstance)):ii();return ot(e,n,r,i)}_getPortal(e){return(!this._portal||this._portal.templateRef!==e.templateRef)&&(this._portal=new Un(e.templateRef,this._viewContainerRef)),this._portal}_ownsMenu(e){return nt.get(e)===this}static \u0275fac=function(i){dn()};static \u0275dir=ve({type:s})}return s})(),er=(()=>{class s extends Io{_cleanupTouchstart;_hoverSubscription=De.EMPTY;get _deprecatedMatMenuTriggerFor(){return this.menu}set _deprecatedMatMenuTriggerFor(e){this.menu=e}get menu(){return this._menu}set menu(e){this._menu=e}menuData;restoreFocus=!0;menuOpened=new ct;onMenuOpen=this.menuOpened;menuClosed=new ct;onMenuClose=this.menuClosed;constructor(){super(!0);let e=y(un);this._cleanupTouchstart=e.listen(this._element.nativeElement,"touchstart",i=>{Fn(i)||(this._openedBy="touch")},{passive:!0})}triggersSubmenu(){return super._triggersSubmenu()}toggleMenu(){return this.menuOpen?this.closeMenu():this.openMenu()}openMenu(){this._openMenu(!0)}closeMenu(){this._closeMenu()}updatePosition(){this._overlayRef?.updatePosition()}ngAfterContentInit(){this._handleHover()}ngOnDestroy(){super.ngOnDestroy(),this._cleanupTouchstart(),this._hoverSubscription.unsubscribe()}_getOverlayOrigin(){return this._element}_getOutsideClickStream(e){return e.backdropClick()}_handleMousedown(e){Nn(e)||(this._openedBy=e.button===0?"mouse":void 0,this.triggersSubmenu()&&e.preventDefault())}_handleKeydown(e){let i=e.keyCode;(i===13||i===32)&&(this._openedBy="keyboard"),this.triggersSubmenu()&&(i===39&&this.dir==="ltr"||i===37&&this.dir==="rtl")&&(this._openedBy="keyboard",this.openMenu())}_handleClick(e){this.triggersSubmenu()?(e.stopPropagation(),this.openMenu()):this.toggleMenu()}_handleHover(){this.triggersSubmenu()&&this._parentMaterialMenu&&(this._hoverSubscription=this._parentMaterialMenu._hovered().subscribe(e=>{e===this._menuItemInstance&&!e.disabled&&(this._openedBy="mouse",this._openMenu(!1))}))}static \u0275fac=function(i){return new(i||s)};static \u0275dir=ve({type:s,selectors:[["","mat-menu-trigger-for",""],["","matMenuTriggerFor",""]],hostAttrs:[1,"mat-mdc-menu-trigger"],hostVars:3,hostBindings:function(i,n){i&1&&ce("click",function(o){return n._handleClick(o)})("mousedown",function(o){return n._handleMousedown(o)})("keydown",function(o){return n._handleKeydown(o)}),i&2&&He("aria-haspopup",n.menu?"menu":null)("aria-expanded",n.menuOpen)("aria-controls",n.menuOpen?n.menu==null?null:n.menu.panelId:null)},inputs:{_deprecatedMatMenuTriggerFor:[0,"mat-menu-trigger-for","_deprecatedMatMenuTriggerFor"],menu:[0,"matMenuTriggerFor","menu"],menuData:[0,"matMenuTriggerData","menuData"],restoreFocus:[0,"matMenuTriggerRestoreFocus","restoreFocus"]},outputs:{menuOpened:"menuOpened",onMenuOpen:"onMenuOpen",menuClosed:"menuClosed",onMenuClose:"onMenuClose"},exportAs:["matMenuTrigger"],features:[fn]})}return s})();var tr=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275mod=ye({type:s});static \u0275inj=fe({providers:[ko],imports:[qn,ne,At,wt,ne]})}return s})(),ir={transformMenu:{type:7,name:"transformMenu",definitions:[{type:0,name:"void",styles:{type:6,styles:{opacity:0,transform:"scale(0.8)"},offset:null}},{type:1,expr:"void => enter",animation:{type:4,styles:{type:6,styles:{opacity:1,transform:"scale(1)"},offset:null},timings:"120ms cubic-bezier(0, 0, 0.2, 1)"},options:null},{type:1,expr:"* => void",animation:{type:4,styles:{type:6,styles:{opacity:0},offset:null},timings:"100ms 25ms linear"},options:null}],options:{}},fadeInItems:{type:7,name:"fadeInItems",definitions:[{type:0,name:"showing",styles:{type:6,styles:{opacity:1},offset:null}},{type:1,expr:"void => *",animation:[{type:6,styles:{opacity:0},offset:null},{type:4,styles:null,timings:"400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)"}],options:null}],options:{}}},ml=ir.fadeInItems,ul=ir.transformMenu;var No=["tooltip"],Hi=20;var Yi=new ae("mat-tooltip-scroll-strategy",{providedIn:"root",factory:()=>{let s=y(ee);return()=>Le(s,{scrollThrottle:Hi})}});function rr(s){let t=y(ee);return()=>Le(t,{scrollThrottle:Hi})}var or={provide:Yi,deps:[],useFactory:rr};function ar(){return{showDelay:0,hideDelay:0,touchendHideDelay:1500}}var lr=new ae("mat-tooltip-default-options",{providedIn:"root",factory:ar});var nr="tooltip-panel",sr=xn({passive:!0}),Fo=8,xo=8,Lo=24,zo=200,Ui=(()=>{class s{_elementRef=y(X);_ngZone=y(Ie);_platform=y(yt);_ariaDescriber=y(Kn);_focusMonitor=y(Ge);_dir=y(Tt);_injector=y(ee);_viewContainerRef=y(lt);_animationsDisabled=xe();_defaultOptions=y(lr,{optional:!0});_overlayRef;_tooltipInstance;_overlayPanelClass;_portal;_position="below";_positionAtOrigin=!1;_disabled=!1;_tooltipClass;_viewInitialized=!1;_pointerExitEventsInitialized=!1;_tooltipComponent=cr;_viewportMargin=8;_currentPosition;_cssClassPrefix="mat-mdc";_ariaDescriptionPending;_dirSubscribed=!1;get position(){return this._position}set position(e){e!==this._position&&(this._position=e,this._overlayRef&&(this._updatePosition(this._overlayRef),this._tooltipInstance?.show(0),this._overlayRef.updatePosition()))}get positionAtOrigin(){return this._positionAtOrigin}set positionAtOrigin(e){this._positionAtOrigin=mi(e),this._detach(),this._overlayRef=null}get disabled(){return this._disabled}set disabled(e){let i=mi(e);this._disabled!==i&&(this._disabled=i,i?this.hide(0):this._setupPointerEnterEventsIfNeeded(),this._syncAriaDescription(this.message))}get showDelay(){return this._showDelay}set showDelay(e){this._showDelay=ci(e)}_showDelay;get hideDelay(){return this._hideDelay}set hideDelay(e){this._hideDelay=ci(e),this._tooltipInstance&&(this._tooltipInstance._mouseLeaveHideDelay=this._hideDelay)}_hideDelay;touchGestures="auto";get message(){return this._message}set message(e){let i=this._message;this._message=e!=null?String(e).trim():"",!this._message&&this._isTooltipVisible()?this.hide(0):(this._setupPointerEnterEventsIfNeeded(),this._updateTooltipMessage()),this._syncAriaDescription(i)}_message="";get tooltipClass(){return this._tooltipClass}set tooltipClass(e){this._tooltipClass=e,this._tooltipInstance&&this._setTooltipClass(this._tooltipClass)}_passiveListeners=[];_touchstartTimeout=null;_destroyed=new pe;_isDestroyed=!1;constructor(){let e=this._defaultOptions;e&&(this._showDelay=e.showDelay,this._hideDelay=e.hideDelay,e.position&&(this.position=e.position),e.positionAtOrigin&&(this.positionAtOrigin=e.positionAtOrigin),e.touchGestures&&(this.touchGestures=e.touchGestures),e.tooltipClass&&(this.tooltipClass=e.tooltipClass)),this._viewportMargin=Fo}ngAfterViewInit(){this._viewInitialized=!0,this._setupPointerEnterEventsIfNeeded(),this._focusMonitor.monitor(this._elementRef).pipe($(this._destroyed)).subscribe(e=>{e?e==="keyboard"&&this._ngZone.run(()=>this.show()):this._ngZone.run(()=>this.hide(0))})}ngOnDestroy(){let e=this._elementRef.nativeElement;this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this._overlayRef&&(this._overlayRef.dispose(),this._tooltipInstance=null),this._passiveListeners.forEach(([i,n])=>{e.removeEventListener(i,n,sr)}),this._passiveListeners.length=0,this._destroyed.next(),this._destroyed.complete(),this._isDestroyed=!0,this._ariaDescriber.removeDescription(e,this.message,"tooltip"),this._focusMonitor.stopMonitoring(e)}show(e=this.showDelay,i){if(this.disabled||!this.message||this._isTooltipVisible()){this._tooltipInstance?._cancelPendingAnimations();return}let n=this._createOverlay(i);this._detach(),this._portal=this._portal||new Yn(this._tooltipComponent,this._viewContainerRef);let r=this._tooltipInstance=n.attach(this._portal).instance;r._triggerElement=this._elementRef.nativeElement,r._mouseLeaveHideDelay=this._hideDelay,r.afterHidden().pipe($(this._destroyed)).subscribe(()=>this._detach()),this._setTooltipClass(this._tooltipClass),this._updateTooltipMessage(),r.show(e)}hide(e=this.hideDelay){let i=this._tooltipInstance;i&&(i.isVisible()?i.hide(e):(i._cancelPendingAnimations(),this._detach()))}toggle(e){this._isTooltipVisible()?this.hide():this.show(void 0,e)}_isTooltipVisible(){return!!this._tooltipInstance&&this._tooltipInstance.isVisible()}_createOverlay(e){if(this._overlayRef){let o=this._overlayRef.getConfig().positionStrategy;if((!this.positionAtOrigin||!e)&&o._origin instanceof X)return this._overlayRef;this._detach()}let i=this._injector.get(ui).getAncestorScrollContainers(this._elementRef),n=`${this._cssClassPrefix}-${nr}`,r=Ct(this._injector,this.positionAtOrigin?e||this._elementRef:this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(!1).withViewportMargin(this._viewportMargin).withScrollableContainers(i);return r.positionChanges.pipe($(this._destroyed)).subscribe(o=>{this._updateCurrentPositionClass(o.connectionPair),this._tooltipInstance&&o.scrollableViewProperties.isOverlayClipped&&this._tooltipInstance.isVisible()&&this._ngZone.run(()=>this.hide(0))}),this._overlayRef=Pt(this._injector,{direction:this._dir,positionStrategy:r,panelClass:this._overlayPanelClass?[...this._overlayPanelClass,n]:n,scrollStrategy:this._injector.get(Yi)(),disableAnimations:this._animationsDisabled}),this._updatePosition(this._overlayRef),this._overlayRef.detachments().pipe($(this._destroyed)).subscribe(()=>this._detach()),this._overlayRef.outsidePointerEvents().pipe($(this._destroyed)).subscribe(()=>this._tooltipInstance?._handleBodyInteraction()),this._overlayRef.keydownEvents().pipe($(this._destroyed)).subscribe(o=>{this._isTooltipVisible()&&o.keyCode===27&&!vt(o)&&(o.preventDefault(),o.stopPropagation(),this._ngZone.run(()=>this.hide(0)))}),this._defaultOptions?.disableTooltipInteractivity&&this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`),this._dirSubscribed||(this._dirSubscribed=!0,this._dir.change.pipe($(this._destroyed)).subscribe(()=>{this._overlayRef&&this._updatePosition(this._overlayRef)})),this._overlayRef}_detach(){this._overlayRef&&this._overlayRef.hasAttached()&&this._overlayRef.detach(),this._tooltipInstance=null}_updatePosition(e){let i=e.getConfig().positionStrategy,n=this._getOrigin(),r=this._getOverlayPosition();i.withPositions([this._addOffset(K(K({},n.main),r.main)),this._addOffset(K(K({},n.fallback),r.fallback))])}_addOffset(e){let i=xo,n=!this._dir||this._dir.value=="ltr";return e.originY==="top"?e.offsetY=-i:e.originY==="bottom"?e.offsetY=i:e.originX==="start"?e.offsetX=n?-i:i:e.originX==="end"&&(e.offsetX=n?i:-i),e}_getOrigin(){let e=!this._dir||this._dir.value=="ltr",i=this.position,n;i=="above"||i=="below"?n={originX:"center",originY:i=="above"?"top":"bottom"}:i=="before"||i=="left"&&e||i=="right"&&!e?n={originX:"start",originY:"center"}:(i=="after"||i=="right"&&e||i=="left"&&!e)&&(n={originX:"end",originY:"center"});let{x:r,y:o}=this._invertPosition(n.originX,n.originY);return{main:n,fallback:{originX:r,originY:o}}}_getOverlayPosition(){let e=!this._dir||this._dir.value=="ltr",i=this.position,n;i=="above"?n={overlayX:"center",overlayY:"bottom"}:i=="below"?n={overlayX:"center",overlayY:"top"}:i=="before"||i=="left"&&e||i=="right"&&!e?n={overlayX:"end",overlayY:"center"}:(i=="after"||i=="right"&&e||i=="left"&&!e)&&(n={overlayX:"start",overlayY:"center"});let{x:r,y:o}=this._invertPosition(n.overlayX,n.overlayY);return{main:n,fallback:{overlayX:r,overlayY:o}}}_updateTooltipMessage(){this._tooltipInstance&&(this._tooltipInstance.message=this.message,this._tooltipInstance._markForCheck(),qe(()=>{this._tooltipInstance&&this._overlayRef.updatePosition()},{injector:this._injector}))}_setTooltipClass(e){this._tooltipInstance&&(this._tooltipInstance.tooltipClass=e,this._tooltipInstance._markForCheck())}_invertPosition(e,i){return this.position==="above"||this.position==="below"?i==="top"?i="bottom":i==="bottom"&&(i="top"):e==="end"?e="start":e==="start"&&(e="end"),{x:e,y:i}}_updateCurrentPositionClass(e){let{overlayY:i,originX:n,originY:r}=e,o;if(i==="center"?this._dir&&this._dir.value==="rtl"?o=n==="end"?"left":"right":o=n==="start"?"left":"right":o=i==="bottom"&&r==="top"?"above":"below",o!==this._currentPosition){let a=this._overlayRef;if(a){let l=`${this._cssClassPrefix}-${nr}-`;a.removePanelClass(l+this._currentPosition),a.addPanelClass(l+o)}this._currentPosition=o}}_setupPointerEnterEventsIfNeeded(){this._disabled||!this.message||!this._viewInitialized||this._passiveListeners.length||(this._platformSupportsMouseEvents()?this._passiveListeners.push(["mouseenter",e=>{this._setupPointerExitEventsIfNeeded();let i;e.x!==void 0&&e.y!==void 0&&(i=e),this.show(void 0,i)}]):this.touchGestures!=="off"&&(this._disableNativeGesturesIfNecessary(),this._passiveListeners.push(["touchstart",e=>{let i=e.targetTouches?.[0],n=i?{x:i.clientX,y:i.clientY}:void 0;this._setupPointerExitEventsIfNeeded(),this._touchstartTimeout&&clearTimeout(this._touchstartTimeout);let r=500;this._touchstartTimeout=setTimeout(()=>{this._touchstartTimeout=null,this.show(void 0,n)},this._defaultOptions?.touchLongPressShowDelay??r)}])),this._addListeners(this._passiveListeners))}_setupPointerExitEventsIfNeeded(){if(this._pointerExitEventsInitialized)return;this._pointerExitEventsInitialized=!0;let e=[];if(this._platformSupportsMouseEvents())e.push(["mouseleave",i=>{let n=i.relatedTarget;(!n||!this._overlayRef?.overlayElement.contains(n))&&this.hide()}],["wheel",i=>this._wheelListener(i)]);else if(this.touchGestures!=="off"){this._disableNativeGesturesIfNecessary();let i=()=>{this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this.hide(this._defaultOptions?.touchendHideDelay)};e.push(["touchend",i],["touchcancel",i])}this._addListeners(e),this._passiveListeners.push(...e)}_addListeners(e){e.forEach(([i,n])=>{this._elementRef.nativeElement.addEventListener(i,n,sr)})}_platformSupportsMouseEvents(){return!this._platform.IOS&&!this._platform.ANDROID}_wheelListener(e){if(this._isTooltipVisible()){let i=this._injector.get(le).elementFromPoint(e.clientX,e.clientY),n=this._elementRef.nativeElement;i!==n&&!n.contains(i)&&this.hide()}}_disableNativeGesturesIfNecessary(){let e=this.touchGestures;if(e!=="off"){let i=this._elementRef.nativeElement,n=i.style;(e==="on"||i.nodeName!=="INPUT"&&i.nodeName!=="TEXTAREA")&&(n.userSelect=n.msUserSelect=n.webkitUserSelect=n.MozUserSelect="none"),(e==="on"||!i.draggable)&&(n.webkitUserDrag="none"),n.touchAction="none",n.webkitTapHighlightColor="transparent"}}_syncAriaDescription(e){this._ariaDescriptionPending||(this._ariaDescriptionPending=!0,this._ariaDescriber.removeDescription(this._elementRef.nativeElement,e,"tooltip"),this._isDestroyed||qe({write:()=>{this._ariaDescriptionPending=!1,this.message&&!this.disabled&&this._ariaDescriber.describe(this._elementRef.nativeElement,this.message,"tooltip")}},{injector:this._injector}))}static \u0275fac=function(i){return new(i||s)};static \u0275dir=ve({type:s,selectors:[["","matTooltip",""]],hostAttrs:[1,"mat-mdc-tooltip-trigger"],hostVars:2,hostBindings:function(i,n){i&2&&Z("mat-mdc-tooltip-disabled",n.disabled)},inputs:{position:[0,"matTooltipPosition","position"],positionAtOrigin:[0,"matTooltipPositionAtOrigin","positionAtOrigin"],disabled:[0,"matTooltipDisabled","disabled"],showDelay:[0,"matTooltipShowDelay","showDelay"],hideDelay:[0,"matTooltipHideDelay","hideDelay"],touchGestures:[0,"matTooltipTouchGestures","touchGestures"],message:[0,"matTooltip","message"],tooltipClass:[0,"matTooltipClass","tooltipClass"]},exportAs:["matTooltip"]})}return s})(),cr=(()=>{class s{_changeDetectorRef=y(Fe);_elementRef=y(X);_isMultiline=!1;message;tooltipClass;_showTimeoutId;_hideTimeoutId;_triggerElement;_mouseLeaveHideDelay;_animationsDisabled=xe();_tooltip;_closeOnInteraction=!1;_isVisible=!1;_onHide=new pe;_showAnimation="mat-mdc-tooltip-show";_hideAnimation="mat-mdc-tooltip-hide";constructor(){}show(e){this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=setTimeout(()=>{this._toggleVisibility(!0),this._showTimeoutId=void 0},e)}hide(e){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId=setTimeout(()=>{this._toggleVisibility(!1),this._hideTimeoutId=void 0},e)}afterHidden(){return this._onHide}isVisible(){return this._isVisible}ngOnDestroy(){this._cancelPendingAnimations(),this._onHide.complete(),this._triggerElement=null}_handleBodyInteraction(){this._closeOnInteraction&&this.hide(0)}_markForCheck(){this._changeDetectorRef.markForCheck()}_handleMouseLeave({relatedTarget:e}){(!e||!this._triggerElement.contains(e))&&(this.isVisible()?this.hide(this._mouseLeaveHideDelay):this._finalizeAnimation(!1))}_onShow(){this._isMultiline=this._isTooltipMultiline(),this._markForCheck()}_isTooltipMultiline(){let e=this._elementRef.nativeElement.getBoundingClientRect();return e.height>Lo&&e.width>=zo}_handleAnimationEnd({animationName:e}){(e===this._showAnimation||e===this._hideAnimation)&&this._finalizeAnimation(e===this._showAnimation)}_cancelPendingAnimations(){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=this._hideTimeoutId=void 0}_finalizeAnimation(e){e?this._closeOnInteraction=!0:this.isVisible()||this._onHide.next()}_toggleVisibility(e){let i=this._tooltip.nativeElement,n=this._showAnimation,r=this._hideAnimation;if(i.classList.remove(e?r:n),i.classList.add(e?n:r),this._isVisible!==e&&(this._isVisible=e,this._changeDetectorRef.markForCheck()),e&&!this._animationsDisabled&&typeof getComputedStyle=="function"){let o=getComputedStyle(i);(o.getPropertyValue("animation-duration")==="0s"||o.getPropertyValue("animation-name")==="none")&&(this._animationsDisabled=!0)}e&&this._onShow(),this._animationsDisabled&&(i.classList.add("_mat-animation-noopable"),this._finalizeAnimation(e))}static \u0275fac=function(i){return new(i||s)};static \u0275cmp=j({type:s,selectors:[["mat-tooltip-component"]],viewQuery:function(i,n){if(i&1&&dt(No,7),i&2){let r;te(r=ie())&&(n._tooltip=r.first)}},hostAttrs:["aria-hidden","true"],hostBindings:function(i,n){i&1&&ce("mouseleave",function(o){return n._handleMouseLeave(o)})},decls:4,vars:4,consts:[["tooltip",""],[1,"mdc-tooltip","mat-mdc-tooltip",3,"animationend","ngClass"],[1,"mat-mdc-tooltip-surface","mdc-tooltip__surface"]],template:function(i,n){if(i&1){let r=ht();M(0,"div",1,0),ce("animationend",function(a){return ge(r),_e(n._handleAnimationEnd(a))}),M(2,"div",2),P(3),E()()}i&2&&(Z("mdc-tooltip--multiline",n._isMultiline),W("ngClass",n.tooltipClass),x(3),ft(n.message))},dependencies:[Mn],styles:[`.mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:"";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mat-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mat-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mat-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mat-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mat-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mat-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}
`],encapsulation:2,changeDetection:0})}return s})(),Gi=(()=>{class s{static \u0275fac=function(i){return new(i||s)};static \u0275mod=ye({type:s});static \u0275inj=fe({providers:[or],imports:[zn,At,ne,ne,wt]})}return s})();var Xt=class s{constructor(t){this.platformId=t;_t(this.platformId)&&(this.initializeTheme(),this.setupMediaQueryListener())}THEME_KEY="qts-theme";currentTheme=new ti("light");isDarkMode=new ti(!1);theme$=this.currentTheme.asObservable();isDarkMode$=this.isDarkMode.asObservable();initializeTheme(){let t=localStorage.getItem(this.THEME_KEY),e=window.matchMedia("(prefers-color-scheme: dark)").matches;t?this.setTheme(t):e?this.setTheme("dark"):this.setTheme("light")}setupMediaQueryListener(){window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{this.currentTheme.value==="auto"&&this.applyTheme(e.matches?"dark":"light")})}setTheme(t){if(this.currentTheme.next(t),_t(this.platformId))if(localStorage.setItem(this.THEME_KEY,t),t==="auto"){let e=window.matchMedia("(prefers-color-scheme: dark)").matches;this.applyTheme(e?"dark":"light")}else this.applyTheme(t)}applyTheme(t){if(_t(this.platformId)){let e=document.documentElement;t==="dark"?(e.setAttribute("data-theme","dark"),e.classList.add("dark-theme"),this.isDarkMode.next(!0)):(e.setAttribute("data-theme","light"),e.classList.remove("dark-theme"),this.isDarkMode.next(!1)),window.dispatchEvent(new CustomEvent("theme-changed",{detail:{theme:t,isDark:t==="dark"}}))}}toggleTheme(){let t=this.currentTheme.value;t==="light"?this.setTheme("dark"):t==="dark"?this.setTheme("auto"):this.setTheme("light")}getCurrentTheme(){return this.currentTheme.value}getIsDarkMode(){return this.isDarkMode.value}static \u0275fac=function(e){return new(e||s)(ke(ln))};static \u0275prov=Oe({token:s,factory:s.\u0275fac,providedIn:"root"})};function qo(s,t){s&1&&(M(0,"mat-icon",2),P(1,"light_mode"),E())}function Ho(s,t){s&1&&(M(0,"mat-icon",3),P(1,"dark_mode"),E())}function Yo(s,t){s&1&&(M(0,"mat-icon",4),P(1,"brightness_auto"),E())}var Wt=class s{constructor(t){this.themeService=t}currentTheme="light";isDarkMode=!1;subscriptions=new De;ngOnInit(){this.subscriptions.add(this.themeService.theme$.subscribe(t=>{this.currentTheme=t})),this.subscriptions.add(this.themeService.isDarkMode$.subscribe(t=>{this.isDarkMode=t}))}ngOnDestroy(){this.subscriptions.unsubscribe()}toggleTheme(){this.themeService.toggleTheme()}getTooltipText(){switch(this.currentTheme){case"light":return"Switch to Dark Mode";case"dark":return"Switch to Auto Mode";case"auto":return"Switch to Light Mode";default:return"Toggle Theme"}}static \u0275fac=function(e){return new(e||s)(hn(Xt))};static \u0275cmp=j({type:s,selectors:[["app-theme-toggle"]],decls:5,vars:4,consts:[["mat-icon-button","","matTooltipPosition","below",1,"theme-toggle",3,"click","matTooltip"],[1,"icon-container"],[1,"theme-icon","light-icon"],[1,"theme-icon","dark-icon"],[1,"theme-icon","auto-icon"]],template:function(e,i){e&1&&(M(0,"button",0),ce("click",function(){return i.toggleTheme()}),M(1,"div",1),mt(2,qo,2,0,"mat-icon",2)(3,Ho,2,0,"mat-icon",3)(4,Yo,2,0,"mat-icon",4),E()()),e&2&&(Z("dark-mode",i.isDarkMode),W("matTooltip",i.getTooltipText()),x(2),ut(i.currentTheme==="light"?2:i.currentTheme==="dark"?3:4))},dependencies:[gt,St,bt,Et,Mt,Gi,Ui],styles:['.theme-toggle[_ngcontent-%COMP%]{position:relative;width:48px!important;height:48px!important;border-radius:50%!important;background:#ffffff1a!important;-webkit-backdrop-filter:blur(10px)!important;backdrop-filter:blur(10px)!important;border:2px solid rgba(255,255,255,.2)!important;transition:all .3s cubic-bezier(.4,0,.2,1)!important;overflow:hidden}.theme-toggle[_ngcontent-%COMP%]:hover{background:#fff3!important;transform:scale(1.1);box-shadow:0 8px 25px #00000026}.theme-toggle.dark-mode[_ngcontent-%COMP%]{background:#0003!important;border-color:#ffffff4d!important}.theme-toggle.dark-mode[_ngcontent-%COMP%]:hover{background:#0000004d!important}.icon-container[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;display:flex;align-items:center;justify-content:center}.theme-icon[_ngcontent-%COMP%]{font-size:24px!important;width:24px!important;height:24px!important;transition:all .3s ease!important;color:#fff!important}.light-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_sunRotate 8s linear infinite}.dark-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_moonGlow 2s ease-in-out infinite alternate}.auto-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_autoFlicker 3s ease-in-out infinite}.theme-toggle[_ngcontent-%COMP%]:hover   .theme-icon[_ngcontent-%COMP%]{transform:scale(1.2)}@keyframes _ngcontent-%COMP%_sunRotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_moonGlow{0%{filter:drop-shadow(0 0 5px rgba(255,255,255,.5));transform:scale(1)}to{filter:drop-shadow(0 0 15px rgba(255,255,255,.8));transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_autoFlicker{0%,to{opacity:1}25%{opacity:.7}50%{opacity:1}75%{opacity:.8}}.theme-toggle[_ngcontent-%COMP%]:after{content:"";position:absolute;top:50%;left:50%;width:0;height:0;border-radius:50%;background:#ffffff4d;transform:translate(-50%,-50%);transition:width .3s ease,height .3s ease;pointer-events:none}.theme-toggle[_ngcontent-%COMP%]:active:after{width:100px;height:100px}.theme-toggle[_ngcontent-%COMP%]:focus{outline:2px solid #0607E1;outline-offset:2px}@media (prefers-reduced-motion: reduce){.theme-toggle[_ngcontent-%COMP%], .theme-icon[_ngcontent-%COMP%], .theme-toggle[_ngcontent-%COMP%]:after{animation:none!important;transition:none!important}}@media (prefers-contrast: high){.theme-toggle[_ngcontent-%COMP%]{border-width:3px!important;background:#000c!important}.theme-icon[_ngcontent-%COMP%]{color:#fff!important;filter:contrast(2)}}']})};var mr=(s,t)=>t.path;function Uo(s,t){if(s&1&&(M(0,"a",8),P(1),E()),s&2){let e=t.$implicit;W("routerLink",e.path),x(),li(" ",e.label," ")}}function Go(s,t){if(s&1&&(M(0,"a",11),P(1),E()),s&2){let e=t.$implicit;W("routerLink",e.path),x(),li(" ",e.label," ")}}var Zt=class s{title=on("Quadrate Tech Solutions");navigationItems=[{path:"/home",label:"Home"},{path:"/about",label:"About"},{path:"/services",label:"Services"},{path:"/case-studies",label:"Case Studies"},{path:"/blog",label:"Blog"},{path:"/contact",label:"Contact"},{path:"/pricing",label:"Pricing"},{path:"/interview",label:"Interview"}];static \u0275fac=function(e){return new(e||s)};static \u0275cmp=j({type:s,selectors:[["app-root"]],decls:57,vars:2,consts:[["mobileMenu","matMenu"],["color","primary",1,"navbar"],[1,"navbar-container"],[1,"navbar-brand"],["routerLink","/home",1,"brand-link"],["src","https://ik.imagekit.io/quadrate/QTS%20Logo%20Primary.png","alt","Quadrate Tech Solutions",1,"logo"],[1,"brand-text"],[1,"navbar-nav","desktop-nav"],["mat-button","","routerLinkActive","active",1,"nav-link",3,"routerLink"],["mat-icon-button","",1,"mobile-menu-btn",3,"matMenuTriggerFor"],[1,"mobile-menu"],["mat-menu-item","","routerLinkActive","active",3,"routerLink"],[1,"main-content"],[1,"footer"],[1,"footer-container"],[1,"footer-content"],[1,"footer-section"],["routerLink","/services"],["routerLink","/about"],["routerLink","/contact"],["routerLink","/blog"],[1,"footer-bottom"]],template:function(e,i){if(e&1&&(M(0,"mat-toolbar",1)(1,"div",2)(2,"div",3)(3,"a",4),be(4,"img",5),M(5,"span",6),P(6),E()()(),M(7,"nav",7),ri(8,Uo,2,2,"a",8,mr),be(10,"app-theme-toggle"),E(),M(11,"button",9)(12,"mat-icon"),P(13,"menu"),E()(),M(14,"mat-menu",10,0),ri(16,Go,2,2,"a",11,mr),E()()(),M(18,"main",12),be(19,"router-outlet"),E(),M(20,"footer",13)(21,"div",14)(22,"div",15)(23,"div",16)(24,"h3"),P(25,"Quadrate Tech Solutions"),E(),M(26,"p"),P(27,"Innovative technology solutions for modern businesses"),E()(),M(28,"div",16)(29,"h4"),P(30,"Services"),E(),M(31,"ul")(32,"li")(33,"a",17),P(34,"AI/ML Solutions"),E()(),M(35,"li")(36,"a",17),P(37,"Custom Development"),E()(),M(38,"li")(39,"a",17),P(40,"Cloud Solutions"),E()()()(),M(41,"div",16)(42,"h4"),P(43,"Company"),E(),M(44,"ul")(45,"li")(46,"a",18),P(47,"About Us"),E()(),M(48,"li")(49,"a",19),P(50,"Contact"),E()(),M(51,"li")(52,"a",20),P(53,"Blog"),E()()()()(),M(54,"div",21)(55,"p"),P(56,"\xA9 2024 Quadrate Tech Solutions. All rights reserved."),E()()()()),e&2){let n=bn(15);x(6),ft(i.title()),x(2),oi(i.navigationItems),x(3),W("matMenuTriggerFor",n),x(5),oi(i.navigationItems)}},dependencies:[gt,On,kn,In,Xs,$s,St,Hn,bt,Et,Mt,tr,je,st,er,Wt],styles:["[_nghost-%COMP%]{--primary-blue: #0607E1;--primary-white: #FFFFFF;--primary-black: #000000;--ai-blue: #0607E1;--generative-purple: #4D0AFF;--vision-cyan: #06B6D4;--nlp-green: #10B981;--dev-orange: #F59E0B;--data-red: #EF4444;--cloud-purple: #8B5CF6;font-family:Montserrat,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif;box-sizing:border-box;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.navbar[_ngcontent-%COMP%]{position:sticky;top:0;z-index:1000;background:var(--primary-blue)!important}.navbar[_ngcontent-%COMP%]   .navbar-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%;max-width:1200px;margin:0 auto;padding:0 1rem}.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]{display:flex;align-items:center;text-decoration:none;color:#fff}.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:40px;margin-right:12px}.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;color:#fff}.navbar[_ngcontent-%COMP%]   .desktop-nav[_ngcontent-%COMP%]{display:flex;gap:1rem}.navbar[_ngcontent-%COMP%]   .desktop-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{color:#fff!important;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.navbar[_ngcontent-%COMP%]   .desktop-nav[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%], .navbar[_ngcontent-%COMP%]   .desktop-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}@media (max-width: 768px){.navbar[_ngcontent-%COMP%]   .desktop-nav[_ngcontent-%COMP%]{display:none}}.navbar[_ngcontent-%COMP%]   .mobile-menu-btn[_ngcontent-%COMP%]{color:#fff}@media (min-width: 769px){.navbar[_ngcontent-%COMP%]   .mobile-menu-btn[_ngcontent-%COMP%]{display:none}}.main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 64px);padding-top:2rem}.footer[_ngcontent-%COMP%]{background-color:#1a1a1a;color:#fff;margin-top:4rem}.footer[_ngcontent-%COMP%]   .footer-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:3rem 1rem 1rem}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:2rem;margin-bottom:2rem}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-bottom:1rem;color:var(--primary-blue)}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:1rem;color:#ccc}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:.5rem}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#ccc;text-decoration:none;transition:color .3s ease}.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--primary-blue)}.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]{border-top:1px solid #333;padding-top:1rem;text-align:center;color:#999}"]})};wn(Zt,Gs).catch(s=>console.error(s));
