import { LucideIconData } from './types';
/**
 * @component @name ReplaceAll
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTRhMiAyIDAgMCAxIDIgMnY0YTIgMiAwIDAgMS0yIDIiIC8+CiAgPHBhdGggZD0iTTE0IDRhMiAyIDAgMCAxIDItMiIgLz4KICA8cGF0aCBkPSJNMTYgMTBhMiAyIDAgMCAxLTItMiIgLz4KICA8cGF0aCBkPSJNMjAgMTRhMiAyIDAgMCAxIDIgMnY0YTIgMiAwIDAgMS0yIDIiIC8+CiAgPHBhdGggZD0iTTIwIDJhMiAyIDAgMCAxIDIgMiIgLz4KICA8cGF0aCBkPSJNMjIgOGEyIDIgMCAwIDEtMiAyIiAvPgogIDxwYXRoIGQ9Im0zIDcgMyAzIDMtMyIgLz4KICA8cGF0aCBkPSJNNiAxMFY1YSAzIDMgMCAwIDEgMy0zaDEiIC8+CiAgPHJlY3QgeD0iMiIgeT0iMTQiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/replace-all
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
declare const ReplaceAll: LucideIconData;
export default ReplaceAll;
