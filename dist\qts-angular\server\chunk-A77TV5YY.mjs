import './polyfills.server.mjs';
import{a as P,b as Q,d as zf,g as $n}from"./chunk-X2SEQXRR.mjs";function N(e){return typeof e=="function"}function zn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var si=zn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function on(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Z=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(N(r))try{r()}catch(i){t=i instanceof si?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Gf(i)}catch(s){t=t??[],s instanceof si?t=[...t,...s.errors]:t.push(s)}}if(t)throw new si(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Gf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&on(n,t)}remove(t){let{_finalizers:n}=this;n&&on(n,t),t instanceof e&&t._removeParent(this)}};Z.EMPTY=(()=>{let e=new Z;return e.closed=!0,e})();var Wa=Z.EMPTY;function ai(e){return e instanceof Z||e&&"closed"in e&&N(e.remove)&&N(e.add)&&N(e.unsubscribe)}function Gf(e){N(e)?e():e.unsubscribe()}var ci=class extends Z{constructor(t,n){super()}schedule(t,n=0){return this}};var Vr={setInterval(e,t,...n){let{delegate:r}=Vr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Vr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var ui=class extends ci{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Vr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Vr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,on(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Ur={now(){return(Ur.delegate||Date).now()},delegate:void 0};var Gn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Gn.now=Ur.now;var li=class extends Gn{constructor(t,n=Gn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var sn=new li(ui),Wf=sn;var We={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Wn={setTimeout(e,t,...n){let{delegate:r}=Wn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Wn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function di(e){Wn.setTimeout(()=>{let{onUnhandledError:t}=We;if(t)t(e);else throw e})}function Hr(){}var qf=qa("C",void 0,void 0);function Zf(e){return qa("E",void 0,e)}function Yf(e){return qa("N",e,void 0)}function qa(e,t,n){return{kind:e,value:t,error:n}}var an=null;function qn(e){if(We.useDeprecatedSynchronousErrorHandling){let t=!an;if(t&&(an={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=an;if(an=null,n)throw r}}else e()}function Kf(e){We.useDeprecatedSynchronousErrorHandling&&an&&(an.errorThrown=!0,an.error=e)}var cn=class extends Z{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ai(t)&&t.add(this)):this.destination=HD}static create(t,n,r){return new Dt(t,n,r)}next(t){this.isStopped?Ya(Yf(t),this):this._next(t)}error(t){this.isStopped?Ya(Zf(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ya(qf,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},VD=Function.prototype.bind;function Za(e,t){return VD.call(e,t)}var Ka=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){fi(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){fi(r)}else fi(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){fi(n)}}},Dt=class extends cn{constructor(t,n,r){super();let o;if(N(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&We.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Za(t.next,i),error:t.error&&Za(t.error,i),complete:t.complete&&Za(t.complete,i)}):o=t}this.destination=new Ka(o)}};function fi(e){We.useDeprecatedSynchronousErrorHandling?Kf(e):di(e)}function UD(e){throw e}function Ya(e,t){let{onStoppedNotification:n}=We;n&&Wn.setTimeout(()=>n(e,t))}var HD={closed:!0,next:Hr,error:UD,complete:Hr};var Zn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function De(e){return e}function $D(...e){return Qa(e)}function Qa(e){return e.length===0?De:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var R=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=GD(n)?n:new Dt(n,r,o);return qn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Qf(r),new r((o,i)=>{let s=new Dt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Zn](){return this}pipe(...n){return Qa(n)(this)}toPromise(n){return n=Qf(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Qf(e){var t;return(t=e??We.Promise)!==null&&t!==void 0?t:Promise}function zD(e){return e&&N(e.next)&&N(e.error)&&N(e.complete)}function GD(e){return e&&e instanceof cn||zD(e)&&ai(e)}function pi(e){return e&&N(e.schedule)}function Jf(e){return e instanceof Date&&!isNaN(e)}function $r(e=0,t,n=Wf){let r=-1;return t!=null&&(pi(t)?n=t:r=t),new R(o=>{let i=Jf(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function WD(e=0,t=sn){return e<0&&(e=0),$r(e,e,t)}function Ja(e){return N(e?.lift)}function S(e){return t=>{if(Ja(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function T(e,t,n,r,o){return new Xa(e,t,n,r,o)}var Xa=class extends cn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function ec(){return S((e,t)=>{let n=null;e._refCount++;let r=T(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var tc=class extends R{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ja(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Z;let n=this.getSubject();t.add(this.source.subscribe(T(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Z.EMPTY)}return t}refCount(){return ec()(this)}};var Xf=zn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var H=(()=>{class e extends R{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new hi(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Xf}next(n){qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){qn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Wa:(this.currentObservers=null,i.push(n),new Z(()=>{this.currentObservers=null,on(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new R;return n.source=this,n}}return e.create=(t,n)=>new hi(t,n),e})(),hi=class extends H{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Wa}};var un=class extends H{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var gi=class extends H{constructor(t=1/0,n=1/0,r=Ur){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var ln=new R(e=>e.complete());function nc(e){return e[e.length-1]}function mi(e){return N(nc(e))?e.pop():void 0}function et(e){return pi(nc(e))?e.pop():void 0}function ep(e,t){return typeof nc(e)=="number"?e.pop():t}function np(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function tp(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function dn(e){return this instanceof dn?(this.v=e,this):new dn(e)}function rp(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(D){return new Promise(function(y,m){i.push([f,D,y,m])>1||c(f,D)})},g&&(o[f]=g(o[f])))}function c(f,g){try{u(r[f](g))}catch(D){p(i[0][3],D)}}function u(f){f.value instanceof dn?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function p(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function op(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof tp=="function"?tp(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var yi=e=>e&&typeof e.length=="number"&&typeof e!="function";function vi(e){return N(e?.then)}function Ei(e){return N(e[Zn])}function Di(e){return Symbol.asyncIterator&&N(e?.[Symbol.asyncIterator])}function _i(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function qD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ii=qD();function Ci(e){return N(e?.[Ii])}function wi(e){return rp(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield dn(n.read());if(o)return yield dn(void 0);yield yield dn(r)}}finally{n.releaseLock()}})}function bi(e){return N(e?.getReader)}function $(e){if(e instanceof R)return e;if(e!=null){if(Ei(e))return ZD(e);if(yi(e))return YD(e);if(vi(e))return KD(e);if(Di(e))return ip(e);if(Ci(e))return QD(e);if(bi(e))return JD(e)}throw _i(e)}function ZD(e){return new R(t=>{let n=e[Zn]();if(N(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function YD(e){return new R(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function KD(e){return new R(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,di)})}function QD(e){return new R(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function ip(e){return new R(t=>{XD(e,t).catch(n=>t.error(n))})}function JD(e){return ip(wi(e))}function XD(e,t){var n,r,o,i;return np(this,void 0,void 0,function*(){try{for(n=op(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function we(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ti(e,t=0){return S((n,r)=>{n.subscribe(T(r,o=>we(r,e,()=>r.next(o),t),()=>we(r,e,()=>r.complete(),t),o=>we(r,e,()=>r.error(o),t)))})}function Mi(e,t=0){return S((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function sp(e,t){return $(e).pipe(Mi(t),Ti(t))}function ap(e,t){return $(e).pipe(Mi(t),Ti(t))}function cp(e,t){return new R(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function up(e,t){return new R(n=>{let r;return we(n,t,()=>{r=e[Ii](),we(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>N(r?.return)&&r.return()})}function Si(e,t){if(!e)throw new Error("Iterable cannot be null");return new R(n=>{we(n,t,()=>{let r=e[Symbol.asyncIterator]();we(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function lp(e,t){return Si(wi(e),t)}function dp(e,t){if(e!=null){if(Ei(e))return sp(e,t);if(yi(e))return cp(e,t);if(vi(e))return ap(e,t);if(Di(e))return Si(e,t);if(Ci(e))return up(e,t);if(bi(e))return lp(e,t)}throw _i(e)}function je(e,t){return t?dp(e,t):$(e)}function be(...e){let t=et(e);return je(e,t)}function rc(e,t){let n=N(e)?e:()=>e,r=o=>o.error(n());return new R(t?o=>t.schedule(r,0,o):r)}function e_(e){return!!e&&(e instanceof R||N(e.lift)&&N(e.subscribe))}var fn=zn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function W(e,t){return S((n,r)=>{let o=0;n.subscribe(T(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:t_}=Array;function n_(e,t){return t_(t)?e(...t):e(t)}function Ni(e){return W(t=>n_(e,t))}var{isArray:r_}=Array,{getPrototypeOf:o_,prototype:i_,keys:s_}=Object;function Ai(e){if(e.length===1){let t=e[0];if(r_(t))return{args:t,keys:null};if(a_(t)){let n=s_(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function a_(e){return e&&typeof e=="object"&&o_(e)===i_}function Ri(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function oc(...e){let t=et(e),n=mi(e),{args:r,keys:o}=Ai(e);if(r.length===0)return je([],t);let i=new R(c_(r,t,o?s=>Ri(o,s):De));return n?i.pipe(Ni(n)):i}function c_(e,t,n=De){return r=>{fp(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)fp(t,()=>{let u=je(e[c],t),l=!1;u.subscribe(T(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function fp(e,t,n){e?we(n,e,t):t()}function pp(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,p=()=>{d&&!c.length&&!u&&t.complete()},f=D=>u<r?g(D):c.push(D),g=D=>{i&&t.next(D),u++;let y=!1;$(n(D,l++)).subscribe(T(t,m=>{o?.(m),i?f(m):t.next(m)},()=>{y=!0},void 0,()=>{if(y)try{for(u--;c.length&&u<r;){let m=c.shift();s?we(t,s,()=>g(m)):g(m)}p()}catch(m){t.error(m)}}))};return e.subscribe(T(t,f,()=>{d=!0,p()})),()=>{a?.()}}function pn(e,t,n=1/0){return N(t)?pn((r,o)=>W((i,s)=>t(r,i,o,s))($(e(r,o))),n):(typeof t=="number"&&(n=t),S((r,o)=>pp(r,o,e,n)))}function zr(e=1/0){return pn(De,e)}function hp(){return zr(1)}function Yn(...e){return hp()(je(e,et(e)))}function u_(e){return new R(t=>{$(e()).subscribe(t)})}function ic(...e){let t=mi(e),{args:n,keys:r}=Ai(e),o=new R(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;$(n[l]).subscribe(T(i,p=>{d||(d=!0,u--),a[l]=p},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Ri(r,a):a),i.complete())}))}});return t?o.pipe(Ni(t)):o}function l_(...e){let t=et(e),n=ep(e,1/0),r=e;return r.length?r.length===1?$(r[0]):zr(n)(je(r,t)):ln}function Ne(e,t){return S((n,r)=>{let o=0;n.subscribe(T(r,i=>e.call(t,i,o++)&&r.next(i)))})}function gp(e){return S((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(T(n,u=>{r=!0,o=u,i||$(e(u)).subscribe(i=T(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function d_(e,t=sn){return gp(()=>$r(e,t))}function xi(e){return S((t,n)=>{let r=null,o=!1,i;r=t.subscribe(T(n,void 0,void 0,s=>{i=$(e(s,xi(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function mp(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(T(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function sc(e,t){return N(t)?pn(e,t,1):pn(e,1)}function hn(e,t=sn){return S((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(T(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Gr(e){return S((t,n)=>{let r=!1;t.subscribe(T(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function gn(e){return e<=0?()=>ln:S((t,n)=>{let r=0;t.subscribe(T(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function ac(e,t=De){return e=e??f_,S((n,r)=>{let o,i=!0;n.subscribe(T(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function f_(e,t){return e===t}function Oi(e=p_){return S((t,n)=>{let r=!1;t.subscribe(T(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function p_(){return new fn}function Wr(e){return S((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function h_(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ne((o,i)=>e(o,i,r)):De,gn(1),n?Gr(t):Oi(()=>new fn))}function cc(e){return e<=0?()=>ln:S((t,n)=>{let r=[];t.subscribe(T(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function g_(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ne((o,i)=>e(o,i,r)):De,cc(1),n?Gr(t):Oi(()=>new fn))}function m_(){return S((e,t)=>{let n,r=!1;e.subscribe(T(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function y_(e,t){return S(mp(e,t,arguments.length>=2,!0))}function qr(e={}){let{connector:t=()=>new H,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,l=d=!1},g=()=>{let D=s;f(),D?.unsubscribe()};return S((D,y)=>{u++,!d&&!l&&p();let m=c=c??t();y.add(()=>{u--,u===0&&!d&&!l&&(a=uc(g,o))}),m.subscribe(y),!s&&u>0&&(s=new Dt({next:L=>m.next(L),error:L=>{d=!0,p(),a=uc(f,n,L),m.error(L)},complete:()=>{l=!0,p(),a=uc(f,r),m.complete()}}),$(D).subscribe(s))})(i)}}function uc(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Dt({next:()=>{r.unsubscribe(),e()}});return $(t(...n)).subscribe(r)}function v_(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,qr({connector:()=>new gi(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function Zr(e){return Ne((t,n)=>e<=n)}function lc(...e){let t=et(e);return S((n,r)=>{(t?Yn(e,n,t):Yn(e,n)).subscribe(r)})}function dc(e,t){return S((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(T(r,c=>{o?.unsubscribe();let u=0,l=i++;$(e(c,l)).subscribe(o=T(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Yr(e){return S((t,n)=>{$(e).subscribe(T(n,()=>n.complete(),Hr)),!n.closed&&t.subscribe(n)})}function E_(e,t=!1){return S((n,r)=>{let o=0;n.subscribe(T(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function _t(e,t,n){let r=N(e)||t||n?{next:e,error:t,complete:n}:e;return r?S((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(T(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):De}var fc;function Fi(){return fc}function tt(e){let t=fc;return fc=e,t}var yp=Symbol("NotFound");function Kn(e){return e===yp||e?.name==="\u0275NotFound"}function Bi(e,t){return Object.is(e,t)}var oe=null,ki=!1,pc=1,D_=null,ie=Symbol("SIGNAL");function M(e){let t=oe;return oe=e,t}function Vi(){return oe}var jt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function mn(e){if(ki)throw new Error("");if(oe===null)return;oe.consumerOnSignalRead(e);let t=oe.nextProducerIndex++;if($i(oe),t<oe.producerNode.length&&oe.producerNode[t]!==e&&Qr(oe)){let n=oe.producerNode[t];Hi(n,oe.producerIndexOfThis[t])}oe.producerNode[t]!==e&&(oe.producerNode[t]=e,oe.producerIndexOfThis[t]=Qr(oe)?Ep(e,oe,t):0),oe.producerLastReadVersion[t]=e.version}function vp(){pc++}function Ui(e){if(!(Qr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===pc)){if(!e.producerMustRecompute(e)&&!yn(e)){ji(e);return}e.producerRecomputeValue(e),ji(e)}}function hc(e){if(e.liveConsumerNode===void 0)return;let t=ki;ki=!0;try{for(let n of e.liveConsumerNode)n.dirty||__(n)}finally{ki=t}}function gc(){return oe?.consumerAllowSignalWrites!==!1}function __(e){e.dirty=!0,hc(e),e.consumerMarkedDirty?.(e)}function ji(e){e.dirty=!1,e.lastCleanEpoch=pc}function It(e){return e&&(e.nextProducerIndex=0),M(e)}function Bt(e,t){if(M(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Qr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Hi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function yn(e){$i(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ui(n),r!==n.version))return!0}return!1}function Qn(e){if($i(e),Qr(e))for(let t=0;t<e.producerNode.length;t++)Hi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ep(e,t,n){if(Dp(e),e.liveConsumerNode.length===0&&_p(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ep(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Hi(e,t){if(Dp(e),e.liveConsumerNode.length===1&&_p(e))for(let r=0;r<e.producerNode.length;r++)Hi(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];$i(o),o.producerIndexOfThis[r]=t}}function Qr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function $i(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Dp(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function _p(e){return e.producerNode!==void 0}function zi(e){D_?.(e)}function Jr(e,t){let n=Object.create(I_);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ui(n),mn(n),n.value===Kr)throw n.error;return n.value};return r[ie]=n,zi(n),r}var Pi=Symbol("UNSET"),Li=Symbol("COMPUTING"),Kr=Symbol("ERRORED"),I_=Q(P({},jt),{value:Pi,dirty:!0,error:null,equal:Bi,kind:"computed",producerMustRecompute(e){return e.value===Pi||e.value===Li},producerRecomputeValue(e){if(e.value===Li)throw new Error("");let t=e.value;e.value=Li;let n=It(e),r,o=!1;try{r=e.computation(),M(null),o=t!==Pi&&t!==Kr&&r!==Kr&&e.equal(t,r)}catch(i){r=Kr,e.error=i}finally{Bt(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function C_(){throw new Error}var Ip=C_;function Cp(e){Ip(e)}function mc(e){Ip=e}var w_=null;function yc(e,t){let n=Object.create(Xr);n.value=e,t!==void 0&&(n.equal=t);let r=()=>wp(n);return r[ie]=n,zi(n),[r,s=>Jn(n,s),s=>vc(n,s)]}function wp(e){return mn(e),e.value}function Jn(e,t){gc()||Cp(e),e.equal(e.value,t)||(e.value=t,b_(e))}function vc(e,t){gc()||Cp(e),Jn(e,t(e.value))}var Xr=Q(P({},jt),{equal:Bi,value:void 0,kind:"signal"});function b_(e){e.version++,vp(),hc(e),w_?.(e)}function bp(e){let t=M(null);try{return e()}finally{M(t)}}var Yi="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(nt(t,n)),this.code=t}};function T_(e){return`NG0${Math.abs(e)}`}function nt(e,t){return`${T_(e)}${t?": "+t:""}`}var Re=globalThis;function B(e){for(let t in e)if(e[t]===B)return t;throw Error("")}function Sp(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function wt(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(wt).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Ki(e,t){return e?t?`${e} ${t}`:e:t||""}var M_=B({__forward_ref__:B});function Qi(e){return e.__forward_ref__=Qi,e.toString=function(){return wt(this())},e}function le(e){return Ac(e)?e():e}function Ac(e){return typeof e=="function"&&e.hasOwnProperty(M_)&&e.__forward_ref__===Qi}function Np(e,t){e==null&&Rc(t,e,null,"!=")}function Rc(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function E(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function pe(e){return{providers:e.providers||[],imports:e.imports||[]}}function io(e){return N_(e,Ji)}function S_(e){return io(e)!==null}function N_(e,t){return e.hasOwnProperty(t)&&e[t]||null}function A_(e){let t=e?.[Ji]??null;return t||null}function Dc(e){return e&&e.hasOwnProperty(Wi)?e[Wi]:null}var Ji=B({\u0275prov:B}),Wi=B({\u0275inj:B}),v=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=E({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function xc(e){return e&&!!e.\u0275providers}var Oc=B({\u0275cmp:B}),Fc=B({\u0275dir:B}),kc=B({\u0275pipe:B}),Pc=B({\u0275mod:B}),no=B({\u0275fac:B}),In=B({__NG_ELEMENT_ID__:B}),Tp=B({__NG_ENV_ID__:B});function bt(e){return typeof e=="string"?e:e==null?"":String(e)}function qi(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():bt(e)}var Lc=B({ngErrorCode:B}),Ap=B({ngErrorMessage:B}),to=B({ngTokenPath:B});function jc(e,t){return Rp("",-200,t)}function Xi(e,t){throw new _(-201,!1)}function R_(e,t){e[to]??=[];let n=e[to],r;typeof t=="object"&&"multi"in t&&t?.multi===!0?(Np(t.provide,"Token with multi: true should have a provide property"),r=qi(t.provide)):r=qi(t),n[0]!==r&&e[to].unshift(r)}function x_(e,t){let n=e[to],r=e[Lc],o=e[Ap]||e.message;return e.message=F_(o,r,n,t),e}function Rp(e,t,n){let r=new _(t,e);return r[Lc]=t,r[Ap]=e,n&&(r[to]=n),r}function O_(e){return e[Lc]}function F_(e,t,n=[],r=null){let o="";n&&n.length>1&&(o=` Path: ${n.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return nt(t,`${e}${i}${o}`)}var _c;function xp(){return _c}function _e(e){let t=_c;return _c=e,t}function Bc(e,t,n){let r=io(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;Xi(e,"Injector")}var k_={},vn=k_,Ic="__NG_DI_FLAG__",Cc=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=En(n)||0;try{return this.injector.get(t,r&8?null:vn,r)}catch(o){if(Kn(o))return o;throw o}}};function P_(e,t=0){let n=Fi();if(n===void 0)throw new _(-203,!1);if(n===null)return Bc(e,void 0,t);{let r=L_(t),o=n.retrieve(e,r);if(Kn(o)){if(r.optional)return null;throw o}return o}}function C(e,t=0){return(xp()||P_)(le(e),t)}function h(e,t){return C(e,En(t))}function En(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function L_(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function wc(e){let t=[];for(let n=0;n<e.length;n++){let r=le(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=j_(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function Op(e,t){return e[Ic]=t,e.prototype[Ic]=t,e}function j_(e){return e[Ic]}function Vt(e,t){let n=e.hasOwnProperty(no);return n?e[no]:null}function Fp(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function kp(e){return e.flat(Number.POSITIVE_INFINITY)}function es(e,t){e.forEach(n=>Array.isArray(n)?es(n,t):t(n))}function Vc(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function so(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Pp(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Lp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ts(e,t,n){let r=er(e,t);return r>=0?e[r|1]=n:(r=~r,Lp(e,r,t,n)),r}function ns(e,t){let n=er(e,t);if(n>=0)return e[n|1]}function er(e,t){return B_(e,t,1)}function B_(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ht={},de=[],rt=new v(""),Uc=new v("",-1),Hc=new v(""),ro=class{get(t,n=vn){if(n===vn){let o=Rp("",-201);throw o.name="\u0275NotFound",o}return n}};function $c(e){return e[Pc]||null}function ot(e){return e[Oc]||null}function zc(e){return e[Fc]||null}function jp(e){return e[kc]||null}function it(e){return{\u0275providers:e}}function Bp(e){return it([{provide:rt,multi:!0,useValue:e}])}function Vp(...e){return{\u0275providers:Gc(!0,e),\u0275fromNgModule:!0}}function Gc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return es(t,s=>{let a=s;Zi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Up(o,i),n}function Up(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Wc(o,i=>{t(i,r)})}}function Zi(e,t,n,r){if(e=le(e),!e)return!1;let o=null,i=Dc(e),s=!i&&ot(e);if(!i&&!s){let c=e.ngModule;if(i=Dc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Zi(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{es(i.imports,l=>{Zi(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Up(u,t)}if(!a){let u=Vt(o)||(()=>new o);t({provide:o,useFactory:u,deps:de},o),t({provide:Hc,useValue:o,multi:!0},o),t({provide:rt,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Wc(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Wc(e,t){for(let n of e)xc(n)&&(n=n.\u0275providers),Array.isArray(n)?Wc(n,t):t(n)}var V_=B({provide:String,useValue:B});function Hp(e){return e!==null&&typeof e=="object"&&V_ in e}function U_(e){return!!(e&&e.useExisting)}function H_(e){return!!(e&&e.useFactory)}function Dn(e){return typeof e=="function"}function $p(e){return!!e.useClass}var ao=new v(""),Gi={},Mp={},Ec;function tr(){return Ec===void 0&&(Ec=new ro),Ec}var ge=class{},_n=class extends ge{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Tc(t,s=>this.processProvider(s)),this.records.set(Uc,Xn(void 0,this)),o.has("environment")&&this.records.set(ge,Xn(void 0,this));let i=this.records.get(ao);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Hc,de,{self:!0}))}retrieve(t,n){let r=En(n)||0;try{return this.get(t,vn,r)}catch(o){if(Kn(o))return o;throw o}}destroy(){eo(this),this._destroyed=!0;let t=M(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),M(t)}}onDestroy(t){return eo(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){eo(this);let n=tt(this),r=_e(void 0),o;try{return t()}finally{tt(n),_e(r)}}get(t,n=vn,r){if(eo(this),t.hasOwnProperty(Tp))return t[Tp](this);let o=En(r),i,s=tt(this),a=_e(void 0);try{if(!(o&4)){let u=this.records.get(t);if(u===void 0){let l=q_(t)&&io(t);l&&this.injectableDefInScope(l)?u=Xn(bc(t),Gi):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u,o)}let c=o&2?tr():this.parent;return n=o&8&&n===vn?null:n,c.get(t,n)}catch(c){let u=O_(c);throw u===-200||u===-201?new _(u,null):c}finally{_e(a),tt(s)}}resolveInjectorInitializers(){let t=M(null),n=tt(this),r=_e(void 0),o;try{let i=this.get(rt,de,{self:!0});for(let s of i)s()}finally{tt(n),_e(r),M(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(wt(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=le(t);let n=Dn(t)?t:le(t&&t.provide),r=z_(t);if(!Dn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Xn(void 0,Gi,!0),o.factory=()=>wc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=M(null);try{if(n.value===Mp)throw jc(wt(t));return n.value===Gi&&(n.value=Mp,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&W_(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{M(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=le(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function bc(e){let t=io(e),n=t!==null?t.factory:Vt(e);if(n!==null)return n;if(e instanceof v)throw new _(204,!1);if(e instanceof Function)return $_(e);throw new _(204,!1)}function $_(e){if(e.length>0)throw new _(204,!1);let n=A_(e);return n!==null?()=>n.factory(e):()=>new e}function z_(e){if(Hp(e))return Xn(void 0,e.useValue);{let t=qc(e);return Xn(t,Gi)}}function qc(e,t,n){let r;if(Dn(e)){let o=le(e);return Vt(o)||bc(o)}else if(Hp(e))r=()=>le(e.useValue);else if(H_(e))r=()=>e.useFactory(...wc(e.deps||[]));else if(U_(e))r=(o,i)=>C(le(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=le(e&&(e.useClass||e.provide));if(G_(e))r=()=>new o(...wc(e.deps));else return Vt(o)||bc(o)}return r}function eo(e){if(e.destroyed)throw new _(205,!1)}function Xn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function G_(e){return!!e.deps}function W_(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function q_(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Tc(e,t){for(let n of e)Array.isArray(n)?Tc(n,t):n&&xc(n)?Tc(n.\u0275providers,t):t(n)}function nr(e,t){let n;e instanceof _n?(eo(e),n=e):n=new Cc(e);let r,o=tt(n),i=_e(void 0);try{return t()}finally{tt(o),_e(i)}}function Zc(){return xp()!==void 0||Fi()!=null}var J=0,I=1,w=2,te=3,Be=4,Ie=5,Te=6,Cn=7,K=8,wn=9,st=10,k=11,rr=12,Yc=13,bn=14,ce=15,$t=16,Tn=17,at=18,co=19,Kc=20,Ct=21,rs=22,Tt=23,xe=24,Mn=25,x=26,Qc=1,Sn=6,ct=7,uo=8,Nn=9,se=10;function ut(e){return Array.isArray(e)&&typeof e[Qc]=="object"}function me(e){return Array.isArray(e)&&e[Qc]===!0}function Jc(e){return(e.flags&4)!==0}function lt(e){return e.componentOffset>-1}function lo(e){return(e.flags&1)===1}function dt(e){return!!e.template}function zt(e){return(e[w]&512)!==0}function Xc(e){return(e.type&16)===16}function zp(e){return(e[w]&32)===32}function An(e){return(e[w]&256)===256}var eu="svg",Gp="math";function z(e){for(;Array.isArray(e);)e=e[J];return e}function os(e){for(;Array.isArray(e);){if(typeof e[Qc]=="object")return e;e=e[J]}return null}function tu(e,t){return z(t[e])}function Ve(e,t){return z(t[e.index])}function fo(e,t){return e.data[t]}function nu(e,t){return e[t]}function ru(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function Ue(e,t){let n=t[e];return ut(n)?n:n[J]}function Wp(e){return(e[w]&4)===4}function is(e){return(e[w]&128)===128}function qp(e){return me(e[te])}function Oe(e,t){return t==null?null:e[t]}function ou(e){e[Tn]=0}function iu(e){e[w]&1024||(e[w]|=1024,is(e)&&Gt(e))}function Zp(e,t){for(;e>0;)t=t[bn],e--;return t}function po(e){return!!(e[w]&9216||e[xe]?.dirty)}function ss(e){e[st].changeDetectionScheduler?.notify(8),e[w]&64&&(e[w]|=1024),po(e)&&Gt(e)}function Gt(e){e[st].changeDetectionScheduler?.notify(0);let t=Ut(e);for(;t!==null&&!(t[w]&8192||(t[w]|=8192,!is(t)));)t=Ut(t)}function su(e,t){if(An(e))throw new _(911,!1);e[Ct]===null&&(e[Ct]=[]),e[Ct].push(t)}function Yp(e,t){if(e[Ct]===null)return;let n=e[Ct].indexOf(t);n!==-1&&e[Ct].splice(n,1)}function Ut(e){let t=e[te];return me(t)?t[te]:t}function au(e){return e[Cn]??=[]}function cu(e){return e.cleanup??=[]}function Kp(e,t,n,r){let o=au(t);o.push(n),e.firstCreatePass&&cu(e).push(r,o.length-1)}var A={lFrame:fh(null),bindingsEnabled:!0,skipHydrationRootTNode:null},ho=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(ho||{}),Z_=0,Mc=!1;function Qp(){return A.lFrame.elementDepthCount}function Jp(){A.lFrame.elementDepthCount++}function uu(){A.lFrame.elementDepthCount--}function lu(){return A.bindingsEnabled}function as(){return A.skipHydrationRootTNode!==null}function du(e){return A.skipHydrationRootTNode===e}function Xp(e){A.skipHydrationRootTNode=e}function fu(){A.skipHydrationRootTNode=null}function b(){return A.lFrame.lView}function q(){return A.lFrame.tView}function eh(e){return A.lFrame.contextLView=e,e[K]}function th(e){return A.lFrame.contextLView=null,e}function ae(){let e=pu();for(;e!==null&&e.type===64;)e=e.parent;return e}function pu(){return A.lFrame.currentTNode}function nh(){let e=A.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function or(e,t){let n=A.lFrame;n.currentTNode=e,n.isParent=t}function hu(){return A.lFrame.isParent}function gu(){A.lFrame.isParent=!1}function rh(){return A.lFrame.contextLView}function mu(e){Rc("Must never be called in production mode"),Z_=e}function yu(){return Mc}function ir(e){let t=Mc;return Mc=e,t}function oh(){let e=A.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ih(){return A.lFrame.bindingIndex}function sh(e){return A.lFrame.bindingIndex=e}function Wt(){return A.lFrame.bindingIndex++}function cs(e){let t=A.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function ah(){return A.lFrame.inI18n}function ch(e,t){let n=A.lFrame;n.bindingIndex=n.bindingRootIndex=e,us(t)}function uh(){return A.lFrame.currentDirectiveIndex}function us(e){A.lFrame.currentDirectiveIndex=e}function lh(e){let t=A.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function ls(){return A.lFrame.currentQueryIndex}function go(e){A.lFrame.currentQueryIndex=e}function Y_(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[Ie]:null}function vu(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=Y_(i),o===null||(i=i[bn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=A.lFrame=dh();return r.currentTNode=t,r.lView=e,!0}function ds(e){let t=dh(),n=e[I];A.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function dh(){let e=A.lFrame,t=e===null?null:e.child;return t===null?fh(e):t}function fh(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function ph(){let e=A.lFrame;return A.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Eu=ph;function fs(){let e=ph();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function hh(e){return(A.lFrame.contextLView=Zp(e,A.lFrame.contextLView))[K]}function ft(){return A.lFrame.selectedIndex}function qt(e){A.lFrame.selectedIndex=e}function mo(){let e=A.lFrame;return fo(e.tView,e.selectedIndex)}function gh(){A.lFrame.currentNamespace=eu}function mh(){K_()}function K_(){A.lFrame.currentNamespace=null}function Du(){return A.lFrame.currentNamespace}var yh=!0;function ps(){return yh}function Mt(e){yh=e}function Sc(e,t=null,n=null,r){let o=_u(e,t,n,r);return o.resolveInjectorInitializers(),o}function _u(e,t=null,n=null,r,o=new Set){let i=[n||de,Vp(e)];return r=r||(typeof e=="object"?void 0:wt(e)),new _n(i,t||tr(),r||null,o)}var Y=class e{static THROW_IF_NOT_FOUND=vn;static NULL=new ro;static create(t,n){if(Array.isArray(t))return Sc({name:""},n,t,"");{let r=t.name??"";return Sc({name:r},t.parent,t.providers,r)}}static \u0275prov=E({token:e,providedIn:"any",factory:()=>C(Uc)});static __NG_ELEMENT_ID__=-1},O=new v(""),He=(()=>{class e{static __NG_ELEMENT_ID__=Q_;static __NG_ENV_ID__=n=>n}return e})(),oo=class extends He{_lView;constructor(t){super(),this._lView=t}get destroyed(){return An(this._lView)}onDestroy(t){let n=this._lView;return su(n,t),()=>Yp(n,t)}};function Q_(){return new oo(b())}var fe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},qe=new v("",{providedIn:"root",factory:()=>{let e=h(ge),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(fe),t.handleError(n))}}}),Iu={provide:rt,useValue:()=>void h(fe),multi:!0},J_=new v("",{providedIn:"root",factory:()=>{}});function X_(){return it([Bp(()=>void h(J_))])}function sr(e){return typeof e=="function"&&e[ie]!==void 0}function pt(e,t){let[n,r,o]=yc(e,t?.equal),i=n,s=i[ie];return i.set=r,i.update=o,i.asReadonly=Cu.bind(i),i}function Cu(){let e=this[ie];if(e.readonlyFn===void 0){let t=()=>this();t[ie]=e,e.readonlyFn=t}return e.readonlyFn}function wu(e){return sr(e)&&typeof e.set=="function"}var Ae=class{},ar=new v("",{providedIn:"root",factory:()=>!1});var bu=new v(""),Tu=new v("");var Rn=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=eI}return e})();function eI(){return new Rn(b(),ae())}var Zt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new un(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new R(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),yo=(()=>{class e{internalPendingTasks=h(Zt);scheduler=h(Ae);errorHandler=h(qe);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})();function xn(...e){}var vo=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new Nc})}return e})(),Nc=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};var Mu={JSACTION:"jsaction"};function vr(e){return{toString:e}.toString()}var hs="__parameters__";function sI(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function aI(e,t,n){return vr(()=>{let r=sI(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(hs)?c[hs]:Object.defineProperty(c,hs,{value:[]})[hs];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Rl=Op(aI("Optional"),8);function eg(e){let t=Re.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function cI(e){return typeof e=="function"}var Ts=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function tg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var $s=(()=>{let e=()=>ng;return e.ngInherit=!0,e})();function ng(e){return e.type.prototype.ngOnChanges&&(e.setInput=lI),uI}function uI(){let e=og(this),t=e?.current;if(t){let n=e.previous;if(n===Ht)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function lI(e,t,n,r,o){let i=this.declaredInputs[r],s=og(e)||dI(e,{previous:Ht,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Ts(u&&u.currentValue,n,c===Ht),tg(e,t,o,n)}var rg="__ngSimpleChanges__";function og(e){return e[rg]||null}function dI(e,t){return e[rg]=t}var vh=[];var V=function(e,t=null,n){for(let r=0;r<vh.length;r++){let o=vh[r];o(e,t,n)}};function fI(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=ng(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function ig(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Ds(e,t,n){sg(e,t,3,n)}function _s(e,t,n,r){(e[w]&3)===n&&sg(e,t,n,r)}function Su(e,t){let n=e[w];(n&3)===t&&(n&=16383,n+=1,e[w]=n)}function sg(e,t,n,r){let o=r!==void 0?e[Tn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Tn]+=65536),(a<i||i==-1)&&(pI(e,n,t,c),e[Tn]=(e[Tn]&**********)+c+2),c++}function Eh(e,t){V(4,e,t);let n=M(null);try{t.call(e)}finally{M(n),V(5,e,t)}}function pI(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[w]>>14<e[Tn]>>16&&(e[w]&3)===t&&(e[w]+=16384,Eh(a,i)):Eh(a,i)}var ur=-1,Pn=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r,o){this.factory=t,this.name=o,this.canSeeViewProviders=n,this.injectImpl=r}};function xl(e){return e!=null&&typeof e=="object"&&(e.insertBeforeIndex===null||typeof e.insertBeforeIndex=="number"||Array.isArray(e.insertBeforeIndex))}function ag(e){return!!(e.type&128)}function hI(e){return(e.flags&8)!==0}function gI(e){return(e.flags&16)!==0}function mI(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];yI(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function cg(e){return e===3||e===4||e===6}function yI(e){return e.charCodeAt(0)===64}function dr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Dh(e,n,o,null,t[++r]):Dh(e,n,o,null,null))}}return e}function Dh(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function ug(e){return e!==ur}function Ms(e){return e&32767}function vI(e){return e>>16}function Ss(e,t){let n=vI(e),r=t;for(;n>0;)r=r[bn],n--;return r}var Hu=!0;function Ns(e){let t=Hu;return Hu=e,t}var EI=256,lg=EI-1,dg=5,DI=0,ht={};function _I(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(In)&&(r=n[In]),r==null&&(r=n[In]=DI++);let o=r&lg,i=1<<o;t.data[e+(o>>dg)]|=i}function As(e,t){let n=fg(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,Nu(r.data,e),Nu(t,null),Nu(r.blueprint,null));let o=Ol(e,t),i=e.injectorIndex;if(ug(o)){let s=Ms(o),a=Ss(o,t),c=a[I].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Nu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function fg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ol(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=yg(o),r===null)return ur;if(n++,o=o[bn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return ur}function $u(e,t,n){_I(e,t,n)}function II(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(cg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function pg(e,t,n){if(n&8||e!==void 0)return e;Xi(t,"NodeInjector")}function hg(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[wn],i=_e(void 0);try{return o?o.get(t,r,n&8):Bc(t,r,n&8)}finally{_e(i)}}return pg(r,t,n)}function gg(e,t,n,r=0,o){if(e!==null){if(t[w]&2048&&!(r&2)){let s=MI(e,t,n,r,ht);if(s!==ht)return s}let i=mg(e,t,n,r,ht);if(i!==ht)return i}return hg(t,n,r,o)}function mg(e,t,n,r,o){let i=wI(n);if(typeof i=="function"){if(!vu(t,e,r))return r&1?pg(o,n,r):hg(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Xi(n);else return s}finally{Eu()}}else if(typeof i=="number"){let s=null,a=fg(e,t),c=ur,u=r&1?t[ce][Ie]:null;for((a===-1||r&4)&&(c=a===-1?Ol(e,t):t[a+8],c===ur||!Ih(r,!1)?a=-1:(s=t[I],a=Ms(c),t=Ss(c,t)));a!==-1;){let l=t[I];if(_h(i,a,l.data)){let d=CI(a,t,n,s,r,u);if(d!==ht)return d}c=t[a+8],c!==ur&&Ih(r,t[I].data[a+8]===u)&&_h(i,a,t)?(s=l,a=Ms(c),t=Ss(c,t)):a=-1}}return o}function CI(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],c=r==null?lt(a)&&Hu:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Is(a,s,n,c,u);return l!==null?Do(t,s,l,a,o):ht}function Is(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:u;for(let f=d;f<p;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&dt(f)&&f.type===n)return c}return null}function Do(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof Pn){let a=i;if(a.resolving){let f=qi(s[n]);throw jc(f)}let c=Ns(a.canSeeViewProviders);a.resolving=!0;let u=s[n].type||s[n],l,d=a.injectImpl?_e(a.injectImpl):null,p=vu(e,r,0);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&fI(n,s[n],t)}finally{d!==null&&_e(d),Ns(c),a.resolving=!1,Eu()}}return i}function wI(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(In)?e[In]:void 0;return typeof t=="number"?t>=0?t&lg:bI:t}function _h(e,t,n){let r=1<<e;return!!(n[t+(e>>dg)]&r)}function Ih(e,t){return!(e&2)&&!(e&1&&t)}var kn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return gg(this._tNode,this._lView,t,En(r),n)}};function bI(){return new kn(ae(),b())}function TI(e){return vr(()=>{let t=e.prototype.constructor,n=t[no]||zu(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[no]||zu(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function zu(e){return Ac(e)?()=>{let t=zu(le(e));return t&&t()}:Vt(e)}function MI(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[w]&2048&&!zt(s);){let a=mg(i,s,n,r|2,ht);if(a!==ht)return a;let c=i.parent;if(!c){let u=s[Kc];if(u){let l=u.get(n,ht,r);if(l!==ht)return l}c=yg(s),s=s[bn]}i=c}return o}function yg(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[Ie]:null}function Fl(e){return II(ae(),e)}function SI(){return Er(ae(),b())}function Er(e,t){return new he(Ve(e,t))}var he=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=SI}return e})();function vg(e){return e instanceof he?e.nativeElement:e}function NI(){return this._results[Symbol.iterator]()}var Ln=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new H}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=kp(t);(this._changesDetected=!Fp(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=NI},Dr="ngSkipHydration",AI="ngskiphydration";function kl(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===AI)return!0}return!1}function Eg(e){return e.hasAttribute(Dr)}function _o(e){return(e.flags&128)===128}function _r(e){if(_o(e))return!0;let t=e.parent;for(;t;){if(_o(e)||kl(t))return!0;t=t.parent}return!1}function RI(e){return _o(e)||kl(e)||_r(e)}var Pl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pl||{}),Dg=new Map,xI=0;function OI(){return xI++}function FI(e){Dg.set(e[co],e)}function Gu(e){Dg.delete(e[co])}var Ch="__ngContext__";function fr(e,t){ut(t)?(e[Ch]=t[co],FI(t)):e[Ch]=t}function _g(e){return Cg(e[rr])}function Ig(e){return Cg(e[Be])}function Cg(e){for(;e!==null&&!me(e);)e=e[Be];return e}var Wu;function Ll(e){Wu=e}function Ao(){if(Wu!==void 0)return Wu;if(typeof document<"u")return document;throw new _(210,!1)}var At=new v("",{providedIn:"root",factory:()=>kI}),kI="ng",zs=new v(""),Qt=new v("",{providedIn:"platform",factory:()=>"unknown"});var PI=new v(""),Ir=new v("",{providedIn:"root",factory:()=>Ao().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function LI(){return new Cr}var Cr=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:LI});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();var jl="h",Bl="b",wg="f",bg="n",Gs="e",Ws="t",wr="c",Ro="x",Yt="r",qs="i",Zs="n",br="d",Vl="l",Ul="di",Hl="s",Tg="p",Mg="t",$l=new v(""),Sg=!1,jI=new v("",{providedIn:"root",factory:()=>Sg}),Ng=new v(""),zl=new v(""),Ag=!1,Rg=new v("");var BI=["click","keydown"],VI=["mouseenter","mouseover","focusin"];var xg="ngb";function Gl(e,t,n=null){if(t.length===0||e.nodeType!==Node.ELEMENT_NODE)return;let r=e.getAttribute(Mu.JSACTION),o=t.reduce((s,a)=>(r?.indexOf(a)??-1)===-1?s+a+":;":s,"");e.setAttribute(Mu.JSACTION,`${r??""}${o}`);let i=n??"";i!==""&&o.length>0&&e.setAttribute(xg,i)}var UI=(e,t,n,r)=>{};function HI(e,t,n,r){UI(e,t,n,r)}function Tr(e){return(e.flags&32)===32}var $I="__nghData__",Wl=$I,zI="__nghDeferData__",Og=zI,lr="ngh",Fg="nghm",kg=()=>null;function GI(e,t,n=!1){let r=e.getAttribute(lr);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,c={};if(r!==""){let l=t.get(Cr,null,{optional:!0});l!==null&&(c=l.get(Wl,[])[Number(r)])}let u={data:c,firstChild:e.firstChild??null};return n&&(u.firstChild=e,Ys(u,0,e.nextSibling)),a?e.setAttribute(lr,a):e.removeAttribute(lr),u}function Pg(){kg=GI}function Lg(e,t,n=!1){return kg(e,t,n)}function jg(e){let t=e._lView;return t[I].type===2?null:(zt(t)&&(t=t[x]),t)}function WI(e){return e.textContent?.replace(/\s/gm,"")}function qI(e){let t=Ao(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=WI(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function Ys(e,t,n){e.segmentHeads??={},e.segmentHeads[t]=n}function qu(e,t){return e.segmentHeads?.[t]??null}function Bg(e){return e.get(Rg,!1,{optional:!0})}function ZI(e,t){let n=e.data,r=n[Gs]?.[t]??null;return r===null&&n[wr]?.[t]&&(r=ql(e,t)),r}function Vg(e,t){return e.data[wr]?.[t]??null}function ql(e,t){let n=Vg(e,t)??[],r=0;for(let o of n)r+=o[Yt]*(o[Ro]??1);return r}function YI(e){if(typeof e.disconnectedNodes>"u"){let t=e.data[br];e.disconnectedNodes=t?new Set(t):null}return e.disconnectedNodes}function Ug(e,t){if(typeof e.disconnectedNodes>"u"){let n=e.data[br];e.disconnectedNodes=n?new Set(n):null}return!!YI(e)?.has(t)}function Ks(e,t){let n=e[Te];return n!==null&&!as()&&!Tr(t)&&!Ug(n,t.index-x)}function Zl(e,t){let n=t,r=e.corruptedTextNodes;n.textContent===""?r.set(n,"ngetn"):n.nextSibling?.nodeType===Node.TEXT_NODE&&r.set(n,"ngtns")}function Hg(e){let t=[];return e!==null&&(e.has(4)&&t.push(...VI),e.has(3)&&t.push(...BI)),t}function Au(e){return!!e&&e.nodeType===Node.COMMENT_NODE&&e.textContent?.trim()===Fg}function wh(e){for(;e&&e.nodeType===Node.TEXT_NODE;)e=e.previousSibling;return e}function $g(e){for(let r of e.body.childNodes)if(Au(r))return;let t=wh(e.body.previousSibling);if(Au(t))return;let n=wh(e.head.lastChild);if(!Au(n))throw new _(-507,!1)}function zg(e,t){let n=e.contentQueries;if(n!==null){let r=M(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];go(i),a.contentQueries(2,t[s],s)}}}finally{M(r)}}}function Zu(e,t,n){go(0);let r=M(null);try{t(e,n)}finally{M(r)}}function Gg(e,t,n){if(Jc(t)){let r=M(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{M(r)}}}var $e=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}($e||{}),gs;function KI(){if(gs===void 0&&(gs=null,Re.trustedTypes))try{gs=Re.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return gs}function Qs(e){return KI()?.createHTML(e)||e}var ms;function Wg(){if(ms===void 0&&(ms=null,Re.trustedTypes))try{ms=Re.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ms}function bh(e){return Wg()?.createHTML(e)||e}function Th(e){return Wg()?.createScriptURL(e)||e}var St=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Yi})`}},Yu=class extends St{getTypeName(){return"HTML"}},Ku=class extends St{getTypeName(){return"Style"}},Qu=class extends St{getTypeName(){return"Script"}},Ju=class extends St{getTypeName(){return"URL"}},Xu=class extends St{getTypeName(){return"ResourceURL"}};function Fe(e){return e instanceof St?e.changingThisBreaksApplicationSecurity:e}function mt(e,t){let n=qg(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Yi})`)}return n===t}function qg(e){return e instanceof St&&e.getTypeName()||null}function Yl(e){return new Yu(e)}function Kl(e){return new Ku(e)}function Ql(e){return new Qu(e)}function Jl(e){return new Ju(e)}function Xl(e){return new Xu(e)}function QI(e){let t=new tl(e);return JI()?new el(t):t}var el=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Qs(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},tl=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Qs(t),n}};function JI(){try{return!!new window.DOMParser().parseFromString(Qs(""),"text/html")}catch{return!1}}var XI=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function xo(e){return e=String(e),e.match(XI)?e:"unsafe:"+e}function Rt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Oo(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Zg=Rt("area,br,col,hr,img,wbr"),Yg=Rt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Kg=Rt("rp,rt"),eC=Oo(Kg,Yg),tC=Oo(Yg,Rt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),nC=Oo(Kg,Rt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Mh=Oo(Zg,tC,nC,eC),Qg=Rt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),rC=Rt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),oC=Rt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),iC=Oo(Qg,rC,oC),sC=Rt("script,style,template"),nl=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=uC(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=cC(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Sh(t).toLowerCase();if(!Mh.hasOwnProperty(n))return this.sanitizedSomething=!0,!sC.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!iC.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Qg[a]&&(c=xo(c)),this.buf.push(" ",s,'="',Nh(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Sh(t).toLowerCase();Mh.hasOwnProperty(n)&&!Zg.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Nh(t))}};function aC(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function cC(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Jg(t);return t}function uC(e){let t=e.firstChild;if(t&&aC(e,t))throw Jg(t);return t}function Sh(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Jg(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var lC=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,dC=/([^\#-~ |!])/g;function Nh(e){return e.replace(/&/g,"&amp;").replace(lC,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(dC,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ys;function Js(e,t){let n=null;try{ys=ys||QI(e);let r=t?String(t):"";n=ys.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ys.getInertBodyElement(r)}while(r!==i);let a=new nl().sanitizeChildren(Ah(n)||n);return Qs(a)}finally{if(n){let r=Ah(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Ah(e){return"content"in e&&fC(e)?e.content:null}function fC(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ue=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ue||{});function pC(e){let t=ed();return t?bh(t.sanitize(ue.HTML,e)||""):mt(e,"HTML")?bh(Fe(e)):Js(Ao(),bt(e))}function Xg(e){let t=ed();return t?t.sanitize(ue.URL,e)||"":mt(e,"URL")?Fe(e):xo(bt(e))}function em(e){let t=ed();if(t)return Th(t.sanitize(ue.RESOURCE_URL,e)||"");if(mt(e,"ResourceURL"))return Th(Fe(e));throw new _(904,!1)}function hC(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?em:Xg}function gC(e,t,n){return hC(t,n)(e)}function ed(){let e=b();return e&&e[st].sanitizer}var mC=/^>|^->|<!--|-->|--!>|<!-$/g,yC=/(<|>)/g,vC="\u200B$1\u200B";function EC(e){return e.replace(mC,t=>t.replace(yC,vC))}function tm(e){return e.ownerDocument.body}function nm(e){return e instanceof Function?e():e}function DC(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var rm="ng-template";function _C(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&DC(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(td(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function td(e){return e.type===4&&e.value!==rm}function IC(e,t,n){let r=e.type===4&&!n?rm:e.value;return t===r}function CC(e,t,n){let r=4,o=e.attrs,i=o!==null?TC(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ze(r)&&!Ze(c))return!1;if(s&&Ze(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!IC(e,c,n)||c===""&&t.length===1){if(Ze(r))return!1;s=!0}}else if(r&8){if(o===null||!_C(e,o,c,n)){if(Ze(r))return!1;s=!0}}else{let u=t[++a],l=wC(c,o,td(e),n);if(l===-1){if(Ze(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Ze(r))return!1;s=!0}}}}return Ze(r)||s}function Ze(e){return(e&1)===0}function wC(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return MC(t,e)}function om(e,t,n=!1){for(let r=0;r<t.length;r++)if(CC(e,t[r],n))return!0;return!1}function bC(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function TC(e){for(let t=0;t<e.length;t++){let n=e[t];if(cg(n))return t}return e.length}function MC(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function SC(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Rh(e,t){return e?":not("+t.trim()+")":t}function NC(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ze(s)&&(t+=Rh(i,o),o=""),r=s,i=i||!Ze(r);n++}return o!==""&&(t+=Rh(i,o)),t}function AC(e){return e.map(NC).join(",")}function RC(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ze(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Me={};function im(e,t){return e.createText(t)}function xC(e,t,n){e.setValue(t,n)}function sm(e,t){return e.createComment(EC(t))}function nd(e,t,n){return e.createElement(t,n)}function Rs(e,t,n,r,o){e.insertBefore(t,n,r,o)}function am(e,t,n){e.appendChild(t,n)}function xh(e,t,n,r,o){r!==null?Rs(e,t,n,r,o):am(e,t,n)}function cm(e,t,n){e.removeChild(null,t,n)}function um(e){e.textContent=""}function OC(e,t,n){e.setAttribute(t,"style",n)}function FC(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function lm(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&mI(e,t,r),o!==null&&FC(e,t,o),i!==null&&OC(e,t,i)}function rd(e,t,n,r,o,i,s,a,c,u,l){let d=x+r,p=d+o,f=kC(d,p),g=typeof u=="function"?u():u;return f[I]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function kC(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Me);return n}function PC(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=rd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function od(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[J]=o,d[w]=r|4|128|8|64|1024,(u!==null||e&&e[w]&2048)&&(d[w]|=2048),ou(d),d[te]=d[bn]=e,d[K]=n,d[st]=s||e&&e[st],d[k]=a||e&&e[k],d[wn]=c||e&&e[wn]||null,d[Ie]=i,d[co]=OI(),d[Te]=l,d[Kc]=u,d[ce]=t.type==2?e[ce]:d,d}function LC(e,t,n){let r=Ve(t,e),o=PC(n),i=e[st].rendererFactory,s=id(e,od(e,o,null,dm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function dm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function fm(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function id(e,t){return e[rr]?e[Yc][Be]=t:e[rr]=t,e[Yc]=t,t}function jC(e=1){pm(q(),b(),ft()+e,!1)}function pm(e,t,n,r){if(!r)if((t[w]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ds(t,i,n)}else{let i=e.preOrderHooks;i!==null&&_s(t,i,0,n)}qt(n)}var Xs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Xs||{});function rl(e,t,n,r){let o=M(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Xs.SignalBased)!==0&&(c=t[i][ie]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):tg(t,c,i,r)}finally{M(o)}}var gt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(gt||{}),BC;function sd(e,t){return BC(e,t)}function cr(e,t,n,r,o){if(r!=null){let i,s=!1;me(r)?i=r:ut(r)&&(s=!0,r=r[J]);let a=z(r);e===0&&n!==null?o==null?am(t,n,a):Rs(t,n,a,o||null,!0):e===1&&n!==null?Rs(t,n,a,o||null,!0):e===2?cm(t,a,s):e===3&&t.destroyNode(a),i!=null&&YC(t,e,i,n,o)}}function VC(e,t){hm(e,t),t[J]=null,t[Ie]=null}function UC(e,t,n,r,o,i){r[J]=o,r[Ie]=t,ta(e,r,n,1,o,i)}function hm(e,t){t[st].changeDetectionScheduler?.notify(9),ta(e,t,t[k],2,null,null)}function HC(e){let t=e[rr];if(!t)return Ru(e[I],e);for(;t;){let n=null;if(ut(t))n=t[rr];else{let r=t[se];r&&(n=r)}if(!n){for(;t&&!t[Be]&&t!==e;)ut(t)&&Ru(t[I],t),t=t[te];t===null&&(t=e),ut(t)&&Ru(t[I],t),n=t&&t[Be]}t=n}}function ad(e,t){let n=e[Nn],r=n.indexOf(t);n.splice(r,1)}function ea(e,t){if(An(t))return;let n=t[k];n.destroyNode&&ta(e,t,n,3,null,null),HC(t)}function Ru(e,t){if(An(t))return;let n=M(null);try{t[w]&=-129,t[w]|=256,t[xe]&&Qn(t[xe]),zC(e,t),$C(e,t),t[I].type===1&&t[k].destroy();let r=t[$t];if(r!==null&&me(t[te])){r!==t[te]&&ad(r,t);let o=t[at];o!==null&&o.detachView(e)}Gu(t)}finally{M(n)}}function $C(e,t){let n=e.cleanup,r=t[Cn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Cn]=null);let o=t[Ct];if(o!==null){t[Ct]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Tt];if(i!==null){t[Tt]=null;for(let s of i)s.destroy()}}function zC(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Pn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];V(4,a,c);try{c.call(a)}finally{V(5,a,c)}}else{V(4,o,i);try{i.call(o)}finally{V(5,o,i)}}}}}function cd(e,t,n){return GC(e,t.parent,n)}function GC(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[J];if(lt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===$e.None||o===$e.Emulated)return null}return Ve(r,n)}function gm(e,t,n){return qC(e,t,n)}function WC(e,t,n){return e.type&40?Ve(e,n):null}var qC=WC,Oh;function ud(e,t,n,r){let o=cd(e,r,t),i=t[k],s=r.parent||t[Ie],a=gm(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)xh(i,o,n[c],a,!1);else xh(i,o,n,a,!1);Oh!==void 0&&Oh(i,r,t,n,o)}function Fn(e,t){if(t!==null){let n=t.type;if(n&3)return Ve(t,e);if(n&4)return ol(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Fn(e,r);{let o=e[t.index];return me(o)?ol(-1,o):z(o)}}else{if(n&128)return Fn(e,t.next);if(n&32)return sd(t,e)()||z(e[t.index]);{let r=mm(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Ut(e[ce]);return Fn(o,r)}else return Fn(e,t.next)}}}return null}function mm(e,t){if(t!==null){let r=e[ce][Ie],o=t.projection;return r.projection[o]}return null}function ol(e,t){let n=se+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return Fn(r,o)}return t[ct]}function ld(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&fr(z(a),r),n.flags|=2),!Tr(n))if(c&8)ld(e,t,n.child,r,o,i,!1),cr(t,e,o,a,i);else if(c&32){let u=sd(n,r),l;for(;l=u();)cr(t,e,o,l,i);cr(t,e,o,a,i)}else c&16?ym(e,t,r,n,o,i):cr(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ta(e,t,n,r,o,i){ld(n,r,e.firstChild,t,o,i,!1)}function ZC(e,t,n){let r=t[k],o=cd(e,n,t),i=n.parent||t[Ie],s=gm(i,n,t);ym(r,0,t,n,o,s)}function ym(e,t,n,r,o,i){let s=n[ce],c=s[Ie].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];cr(t,e,o,l,i)}else{let u=c,l=s[te];_o(r)&&(u.flags|=128),ld(e,t,u,l,o,i,!0)}}function YC(e,t,n,r,o){let i=n[ct],s=z(n);i!==s&&cr(t,e,r,i,o);for(let a=se;a<n.length;a++){let c=n[a];ta(c[I],c,e,t,r,i)}}function KC(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:gt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=gt.Important),e.setStyle(n,r,o,i))}}function vm(e,t,n,r,o){let i=ft(),s=r&2;try{qt(-1),s&&t.length>x&&pm(e,t,x,!1),V(s?2:0,o,n),n(r,o)}finally{qt(i),V(s?3:1,o,n)}}function dd(e,t,n){nw(e,t,n),(n.flags&64)===64&&rw(e,t,n)}function na(e,t,n=Ve){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function QC(e,t,n,r){let i=r.get(jI,Sg)||n===$e.ShadowDom,s=e.selectRootElement(t,i);return JC(s),s}function JC(e){Em(e)}var Em=()=>null;function XC(e){Eg(e)?um(e):qI(e)}function Dm(){Em=XC}function ew(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function _m(e,t,n,r,o,i){let s=t[I];if(fd(e,s,t,n,r)){lt(e)&&tw(t,e.index);return}e.type&3&&(n=ew(n)),Im(e,t,n,r,o,i)}function Im(e,t,n,r,o,i){if(e.type&3){let s=Ve(e,t);r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function tw(e,t){let n=Ue(t,e);n[w]&16||(n[w]|=64)}function nw(e,t,n){let r=n.directiveStart,o=n.directiveEnd;lt(n)&&LC(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||As(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Do(t,e,s,n);if(fr(c,t),i!==null&&aw(t,s-r,c,a,n,i),dt(a)){let u=Ue(n.index,t);u[K]=Do(t,e,s,n)}}}function rw(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=uh();try{qt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];us(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&ow(c,u)}}finally{qt(-1),us(s)}}function ow(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Cm(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];om(t,i.selectors,!1)&&(r??=[],dt(i)?r.unshift(i):r.push(i))}return r}function iw(e,t,n,r,o,i){let s=Ve(e,t);sw(t[k],s,i,e.value,n,r,o)}function sw(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?bt(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function aw(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];rl(r,n,c,u)}}function wm(e,t,n,r,o){let i=x+n,s=t[I],a=o(s,t,e,r,n);t[i]=a,or(e,!0);let c=e.type===2;return c?(lm(t[k],a,e),(Qp()===0||lo(e))&&fr(a,t),Jp()):fr(a,t),ps()&&(!c||!Tr(e))&&ud(s,t,a,e),e}function bm(e){let t=e;return hu()?gu():(t=t.parent,or(t,!1)),t}function cw(e,t){let n=e[wn];if(!n)return;n.get(qe,null)?.(t)}function fd(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];rl(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];rl(l,u,r,o),a=!0}return a}function uw(e,t){let n=Ue(t,e),r=n[I];lw(r,n);let o=n[J];o!==null&&n[Te]===null&&(n[Te]=Lg(o,n[wn])),V(18),pd(r,n,n[K]),V(19,n[K])}function lw(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function pd(e,t,n){ds(t);try{let r=e.viewQuery;r!==null&&Zu(1,r,n);let o=e.template;o!==null&&vm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[at]?.finishViewCreation(e),e.staticContentQueries&&zg(e,t),e.staticViewQueries&&Zu(2,e.viewQuery,n);let i=e.components;i!==null&&dw(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[w]&=-5,fs()}}function dw(e,t){for(let n=0;n<t.length;n++)uw(e,t[n])}function Fo(e,t,n,r){let o=M(null);try{let i=t.tView,a=e[w]&4096?4096:16,c=od(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[$t]=u;let l=e[at];return l!==null&&(c[at]=l.createEmbeddedView(i)),pd(i,c,n),c}finally{M(o)}}function pr(e,t){return!t||t.firstChild===null||_o(e)}var Fh=!1,fw=new v("");function hr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(z(i)),me(i)&&ra(i,r);let s=n.type;if(s&8)hr(e,t,n.child,r);else if(s&32){let a=sd(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=mm(t,n);if(Array.isArray(a))r.push(...a);else{let c=Ut(t[ce]);hr(c[I],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function ra(e,t){for(let n=se;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&hr(r[I],r,o,t)}e[ct]!==e[J]&&t.push(e[ct])}function Tm(e){if(e[Mn]!==null){for(let t of e[Mn])t.impl.addSequence(t);e[Mn].length=0}}var Mm=[];function pw(e){return e[xe]??hw(e)}function hw(e){let t=Mm.pop()??Object.create(mw);return t.lView=e,t}function gw(e){e.lView[xe]!==e&&(e.lView=null,Mm.push(e))}var mw=Q(P({},jt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Gt(e.lView)},consumerOnSignalRead(){this.lView[xe]=this}});function yw(e){let t=e[xe]??Object.create(vw);return t.lView=e,t}var vw=Q(P({},jt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Ut(e.lView);for(;t&&!Sm(t[I]);)t=Ut(t);t&&iu(t)},consumerOnSignalRead(){this.lView[xe]=this}});function Sm(e){return e.type!==2}function Nm(e){if(e[Tt]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Tt])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[w]&8192)}}var Ew=100;function hd(e,t=0){let r=e[st].rendererFactory,o=!1;o||r.begin?.();try{Dw(e,t)}finally{o||r.end?.()}}function Dw(e,t){let n=yu();try{ir(!0),il(e,t);let r=0;for(;po(e);){if(r===Ew)throw new _(103,!1);r++,il(e,1)}}finally{ir(n)}}function Am(e,t){mu(t?ho.Exhaustive:ho.OnlyDirtyViews);try{hd(e)}finally{mu(ho.Off)}}function _w(e,t,n,r){if(An(t))return;let o=t[w],i=!1,s=!1;ds(t);let a=!0,c=null,u=null;i||(Sm(e)?(u=pw(t),c=It(u)):Vi()===null?(a=!1,u=yw(t),c=It(u)):t[xe]&&(Qn(t[xe]),t[xe]=null));try{ou(t),sh(e.bindingStartIndex),n!==null&&vm(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Ds(t,f,null)}else{let f=e.preOrderHooks;f!==null&&_s(t,f,0,null),Su(t,0)}if(s||Iw(t),Nm(t),Rm(t,0),e.contentQueries!==null&&zg(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Ds(t,f)}else{let f=e.contentHooks;f!==null&&_s(t,f,1),Su(t,1)}ww(e,t);let d=e.components;d!==null&&Om(t,d,0);let p=e.viewQuery;if(p!==null&&Zu(2,p,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Ds(t,f)}else{let f=e.viewHooks;f!==null&&_s(t,f,2),Su(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[rs]){for(let f of t[rs])f();t[rs]=null}i||(Tm(t),t[w]&=-73)}catch(l){throw i||Gt(t),l}finally{u!==null&&(Bt(u,c),a&&gw(u)),fs()}}function Rm(e,t){for(let n=_g(e);n!==null;n=Ig(n))for(let r=se;r<n.length;r++){let o=n[r];xm(o,t)}}function Iw(e){for(let t=_g(e);t!==null;t=Ig(t)){if(!(t[w]&2))continue;let n=t[Nn];for(let r=0;r<n.length;r++){let o=n[r];iu(o)}}}function Cw(e,t,n){V(18);let r=Ue(t,e);xm(r,n),V(19,r[K])}function xm(e,t){is(e)&&il(e,t)}function il(e,t){let r=e[I],o=e[w],i=e[xe],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&yn(i)),s||=!1,i&&(i.dirty=!1),e[w]&=-9217,s)_w(r,e,r.template,e[K]);else if(o&8192){let a=M(null);try{Nm(e),Rm(e,1);let c=r.components;c!==null&&Om(e,c,1),Tm(e)}finally{M(a)}}}function Om(e,t,n){for(let r=0;r<t.length;r++)Cw(e,t[r],n)}function ww(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)qt(~o);else{let i=o,s=n[++r],a=n[++r];ch(s,i);let c=t[i];V(24,c),a(2,c),V(25,c)}}}finally{qt(-1)}}function gd(e,t){let n=yu()?64:1088;for(e[st].changeDetectionScheduler?.notify(t);e;){e[w]|=n;let r=Ut(e);if(zt(e)&&!r)return e;e=r}return null}function Fm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function km(e,t){let n=se+t;if(n<e.length)return e[n]}function ko(e,t,n,r=!0){let o=t[I];if(bw(o,t,e,n),r){let s=ol(n,e),a=t[k],c=a.parentNode(e[ct]);c!==null&&UC(o,e[Ie],a,t,c,s)}let i=t[Te];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Pm(e,t){let n=Io(e,t);return n!==void 0&&ea(n[I],n),n}function Io(e,t){if(e.length<=se)return;let n=se+t,r=e[n];if(r){let o=r[$t];o!==null&&o!==e&&ad(o,r),t>0&&(e[n-1][Be]=r[Be]);let i=so(e,se+t);VC(r[I],r);let s=i[at];s!==null&&s.detachView(i[I]),r[te]=null,r[Be]=null,r[w]&=-129}return r}function bw(e,t,n,r){let o=se+r,i=n.length;r>0&&(n[o-1][Be]=t),r<i-se?(t[Be]=n[o],Vc(n,se+r,t)):(n.push(t),t[Be]=null),t[te]=n;let s=t[$t];s!==null&&n!==s&&Lm(s,t);let a=t[at];a!==null&&a.insertView(e),ss(t),t[w]|=128}function Lm(e,t){let n=e[Nn],r=t[te];if(ut(r))e[w]|=2;else{let o=r[te][ce];t[ce]!==o&&(e[w]|=2)}n===null?e[Nn]=[t]:n.push(t)}var Kt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[I];return hr(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[K]}set context(t){this._lView[K]=t}get destroyed(){return An(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[te];if(me(t)){let n=t[uo],r=n?n.indexOf(this):-1;r>-1&&(Io(t,r),so(n,r))}this._attachedToViewContainer=!1}ea(this._lView[I],this._lView)}onDestroy(t){su(this._lView,t)}markForCheck(){gd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[w]&=-129}reattach(){ss(this._lView),this._lView[w]|=128}detectChanges(){this._lView[w]|=1024,hd(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[wn].get(fw,Fh)}catch{this.exhaustive=Fh}}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=zt(this._lView),n=this._lView[$t];n!==null&&!t&&ad(n,this._lView),hm(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=zt(this._lView),r=this._lView[$t];r!==null&&!n&&Lm(r,this._lView),ss(this._lView)}};var gr=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Tw;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Fo(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new Kt(i)}}return e})();function Tw(){return oa(ae(),b())}function oa(e,t){return e.type&4?new gr(t,e,Er(e,t)):null}var sl="<-- AT THIS LOCATION";function Mw(e){switch(e){case 4:return"view container";case 2:return"element";case 8:return"ng-container";case 32:return"icu";case 64:return"i18n";case 16:return"projection";case 1:return"text";case 128:return"@let";default:return"<unknown>"}}function Sw(e,t){let n=`During serialization, Angular was unable to find an element in the DOM:

`,r=`${xw(e,t,!1)}

`,o=Fw();throw new _(-502,n+r+o)}function jm(e){let t="During serialization, Angular detected DOM nodes that were created outside of Angular context and provided as projectable nodes (likely via `ViewContainerRef.createComponent` or `createComponent` APIs). Hydration is not supported for such cases, consider refactoring the code to avoid this pattern or using `ngSkipHydration` on the host element of the component.\n\n",n=`${Ow(e)}

`,r=t+n+kw();return new _(-503,r)}function Nw(e){let t=[];if(e.attrs)for(let n=0;n<e.attrs.length;){let r=e.attrs[n++];if(typeof r=="number")break;let o=e.attrs[n++];t.push(`${r}="${xs(o)}"`)}return t.join(" ")}var Aw=new Set(["ngh","ng-version","ng-server-context"]);function Rw(e){let t=[];for(let n=0;n<e.attributes.length;n++){let r=e.attributes[n];Aw.has(r.name)||t.push(`${r.name}="${xs(r.value)}"`)}return t.join(" ")}function xu(e,t="\u2026"){switch(e.type){case 1:return`#text${e.value?`(${e.value})`:""}`;case 2:let r=Nw(e),o=e.value.toLowerCase();return`<${o}${r?" "+r:""}>${t}</${o}>`;case 8:return"<!-- ng-container -->";case 4:return"<!-- container -->";default:return`#node(${Mw(e.type)})`}}function Cs(e,t="\u2026"){let n=e;switch(n.nodeType){case Node.ELEMENT_NODE:let r=n.tagName.toLowerCase(),o=Rw(n);return`<${r}${o?" "+o:""}>${t}</${r}>`;case Node.TEXT_NODE:let i=n.textContent?xs(n.textContent):"";return`#text${i?`(${i})`:""}`;case Node.COMMENT_NODE:return`<!-- ${xs(n.textContent??"")} -->`;default:return`#node(${n.nodeType})`}}function xw(e,t,n){let r="  ",o="";t.prev?(o+=r+`\u2026
`,o+=r+xu(t.prev)+`
`):t.type&&t.type&12&&(o+=r+`\u2026
`),n?(o+=r+xu(t)+`
`,o+=r+`<!-- container -->  ${sl}
`):o+=r+xu(t)+`  ${sl}
`,o+=r+`\u2026
`;let i=t.type?cd(e[I],t,e):null;return i&&(o=Cs(i,`
`+o)),o}function Ow(e){let t="  ",n="",r=e;return r.previousSibling&&(n+=t+`\u2026
`,n+=t+Cs(r.previousSibling)+`
`),n+=t+Cs(r)+`  ${sl}
`,e.nextSibling&&(n+=t+`\u2026
`),e.parentNode&&(n=Cs(r.parentNode,`
`+n)),n}function Fw(e){return`To fix this problem:
  * check ${e?`the "${e}"`:"corresponding"} component for hydration-related issues
  * check to see if your template has valid HTML structure
  * or skip hydration by adding the \`ngSkipHydration\` attribute to its host node in a template

`}function kw(){return`Note: attributes are only displayed to better represent the DOM but have no effect on hydration mismatches.

`}function Pw(e){return e.replace(/\s+/gm,"")}function xs(e,t=50){return e?(e=Pw(e),e.length>t?`${e.substring(0,t-1)}\u2026`:e):""}function Mr(e,t,n,r,o){let i=e.data[t];if(i===null)i=Lw(e,t,n,r,o),ah()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=nh();i.injectorIndex=s===null?-1:s.injectorIndex}return or(i,!0),i}function Lw(e,t,n,r,o){let i=pu(),s=hu(),a=s?i:i&&i.parent,c=e.data[t]=Bw(e,a,n,t,r,o);return jw(e,c,i,s),c}function jw(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Bw(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return as()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Vw(e,t){let n=t[e.currentCaseLViewIndex];return n===null?n:n<0?~n:n}function Bm(e,t,n){e.index=0;let r=Vw(t,n);r!==null?e.removes=t.remove[r]:e.removes=de}function al(e){if(e.index<e.removes.length){let t=e.removes[e.index++];if(t>0)return e.lView[t];{e.stack.push(e.index,e.removes);let n=~t,r=e.lView[I].data[n];return Bm(e,r,e.lView),al(e)}}else return e.stack.length===0?null:(e.removes=e.stack.pop(),e.index=e.stack.pop(),al(e))}function Uw(e,t){let n={stack:[],index:-1,lView:t};return Bm(n,e,t),al.bind(null,n)}var Hw=new RegExp(`^(\\d+)*(${Bl}|${jl})*(.*)`);function $w(e,t){let n=[e];for(let r of t){let o=n.length-1;if(o>0&&n[o-1]===r){let i=n[o]||1;n[o]=i+1}else n.push(r,"")}return n.join("")}function zw(e){let t=e.match(Hw),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[c,u,l]of i.matchAll(/(f|n)(\d*)/g)){let d=parseInt(l,10)||1;a.push(u,d)}return[s,...a]}function Gw(e){return!e.prev&&e.parent?.type===8}function Ou(e){return e.index-x}function Sr(e,t){return!(e.type&144)&&!!t[e.index]&&Vm(z(t[e.index]))}function Vm(e){return!!e&&!e.isConnected}function Ww(e,t){let n=e.i18nNodes;if(n)return n.get(t)}function ia(e,t,n,r){let o=Ou(r),i=Ww(e,o);if(i===void 0){let s=e.data[Zs];if(s?.[o])i=Zw(s[o],n);else if(t.firstChild===r)i=e.firstChild;else{let a=r.prev===null,c=r.prev??r.parent;if(Gw(r)){let u=Ou(r.parent);i=qu(e,u)}else{let u=Ve(c,n);if(a)i=u.firstChild;else{let l=Ou(c),d=qu(e,l);if(c.type===2&&d){let f=ql(e,l)+1;i=sa(f,d)}else i=u.nextSibling}}}}return i}function sa(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function qw(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case wg:n=n.firstChild;break;case bg:n=n.nextSibling;break}}return n}function Zw(e,t){let[n,...r]=zw(e),o;if(n===jl)o=t[ce][J];else if(n===Bl)o=tm(t[ce][J]);else{let i=Number(n);o=z(t[i+x])}return qw(o,r)}function cl(e,t){if(e===t)return[];if(e.parentElement==null||t.parentElement==null)return null;if(e.parentElement===t.parentElement)return Yw(e,t);{let n=t.parentElement,r=cl(e,n),o=cl(n.firstChild,t);return!r||!o?null:[...r,wg,...o]}}function Yw(e,t){let n=[],r=null;for(r=e;r!=null&&r!==t;r=r.nextSibling)n.push(bg);return r==null?null:n}function kh(e,t,n){let r=cl(e,t);return r===null?null:$w(n,r)}function Um(e,t,n){let r=e.parent,o,i,s;for(;r!==null&&(Sr(r,t)||n?.has(r.index));)r=r.parent;r===null||!(r.type&3)?(o=s=jl,i=t[ce][J]):(o=r.index,i=z(t[o]),s=bt(o-x));let a=z(t[e.index]);if(e.type&44){let u=Fn(t,e);u&&(a=u)}let c=kh(i,a,s);if(c===null&&i!==a){let u=i.ownerDocument.body;if(c=kh(u,a,Bl),c===null)throw Sw(t,e)}return c}var Hm=!1;function $m(e){Hm=e}function zm(){return Hm}function Gm(e){return e=e??h(Y),e.get(Ng,!1)}function Wm(e,t){let n=t.i18nChildren.get(e);return n===void 0&&(n=Kw(e),t.i18nChildren.set(e,n)),n}function Kw(e){let t=new Set;function n(r){switch(t.add(r.index),r.kind){case 1:case 2:{for(let o of r.children)n(o);break}case 3:{for(let o of r.cases)for(let i of o)n(i);break}}}for(let r=x;r<e.bindingStartIndex;r++){let o=e.data[r];if(!(!o||!o.ast))for(let i of o.ast)n(i)}return t.size===0?null:t}function qm(e,t,n){if(!n.isI18nHydrationEnabled)return null;let r=e[I],o=r.data[t];if(!o||!o.ast)return null;let i=r.data[o.parentTNodeIndex];if(i&&RI(i))return null;let s={caseQueue:[],disconnectedNodes:new Set,disjointNodes:new Set};return ul(e,s,n,o.ast),s.caseQueue.length===0&&s.disconnectedNodes.size===0&&s.disjointNodes.size===0?null:s}function ul(e,t,n,r){let o=null;for(let i of r){let s=Jw(e,t,n,i);s&&(Qw(o,s)&&t.disjointNodes.add(i.index-x),o=s)}return o}function Qw(e,t){return e&&e.nextSibling!==t}function Jw(e,t,n,r){let o=z(e[r.index]);if(!o||Vm(o))return t.disconnectedNodes.add(r.index-x),null;let i=o;switch(r.kind){case 0:{Zl(n,i);break}case 1:case 2:{ul(e,t,n,r.children);break}case 3:{let s=e[r.currentCaseLViewIndex];if(s!=null){let a=s<0?~s:s;t.caseQueue.push(a),ul(e,t,n,r.cases[a])}break}}return Xw(e,r)}function Xw(e,t){let r=e[I].data[t.index];return xl(r)?Fn(e,r):t.kind===3?Uw(r,e)()??z(e[t.index]):z(e[t.index])??null}function md(e){let t=e[Sn]??[],r=e[te][k],o=[];for(let i of t)i.data[Ul]!==void 0?o.push(i):eb(i,r);e[Sn]=o}function eb(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[Yt];for(;n<o;){let i=r.nextSibling;cm(t,r,!1),r=i,n++}}}function tb(e,t){let n=[];for(let r of t)for(let o=0;o<(r[Ro]??1);o++){let i={data:r,firstChild:null};r[Yt]>0&&(i.firstChild=e,e=sa(r[Yt],e)),n.push(i)}return[e,n]}var Zm=()=>null,Ym=()=>null;function Km(){Zm=nb,Ym=rb}function nb(e,t){return Jm(e,t)?e[Sn].shift():(md(e),null)}function Co(e,t){return Zm(e,t)}function rb(e,t,n){if(t.tView.ssrId===null)return null;let r=Co(e,t.tView.ssrId);return n[I].firstUpdatePass&&r===null&&ob(n,t),r}function Qm(e,t,n){return Ym(e,t,n)}function ob(e,t){let n=t;for(;n;){if(Ph(e,n))return;if((n.flags&256)===256)break;n=n.prev}for(n=t.next;n&&(n.flags&512)===512;){if(Ph(e,n))return;n=n.next}}function Jm(e,t){let n=e[Sn];return!t||n===null||n.length===0?!1:n[0].data[qs]===t}function Ph(e,t){let n=t.tView?.ssrId;if(n==null)return!1;let r=e[t.index];return me(r)&&Jm(r,n)?(md(r),!0):!1}var Xm=class{},aa=class{},ll=class{resolveComponentFactory(t){throw new _(917,!1)}},Po=class{static NULL=new ll},Nt=class{},ca=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>ib()}return e})();function ib(){let e=b(),t=ae(),n=Ue(t.index,e);return(ut(n)?n:e)[k]}var ey=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>null})}return e})();var ws={},dl=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,ws,r);return o!==ws||n===ws?o:this.parentInjector.get(t,n,r)}};function Os(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ki(o,a);else if(i==2){let c=a,u=t[++s];r=Ki(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function ze(e,t=0){let n=b();if(n===null)return C(e,t);let r=ae();return gg(r,n,le(e),t)}function sb(){let e="invalid";throw new Error(e)}function ty(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}ub(e,t,n,a,i,c,u)}i!==null&&r!==null&&ab(n,r,i)}function ab(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function cb(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function ub(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&dt(f)&&(c=!0,cb(e,n,p)),$u(As(n,t),e,f.type)}gb(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=fm(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=dr(n.mergedAttrs,f.hostAttrs),db(e,n,t,d,f),hb(d,f,o),s!==null&&s.has(f)){let[D,y]=s.get(f);n.directiveToIndex.set(f.type,[d,D+n.directiveStart,y+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}lb(e,n,i)}function lb(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Lh(0,t,o,r),Lh(1,t,o,r),Bh(t,r,!1);else{let i=n.get(o);jh(0,t,i,r),jh(1,t,i,r),Bh(t,r,!0)}}}function Lh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),ny(t,i)}}function jh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),ny(t,s)}}function ny(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Bh(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||td(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function db(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Vt(o.type,!0)),s=new Pn(i,dt(o),ze,null);e.blueprint[r]=s,n[r]=s,fb(e,t,r,fm(e,n,o.hostVars,Me),o)}function fb(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;pb(s)!=a&&s.push(a),s.push(n,r,i)}}function pb(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function hb(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;dt(t)&&(n[""]=e)}}function gb(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function ry(e,t,n,r,o,i,s,a){let c=t[I],u=c.consts,l=Oe(u,s),d=Mr(c,e,n,r,l);return i&&ty(c,t,d,Oe(u,a),o),d.mergedAttrs=dr(d.mergedAttrs,d.attrs),d.attrs!==null&&Os(d,d.attrs,!1),d.mergedAttrs!==null&&Os(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function oy(e,t){ig(e,t),Jc(t)&&e.queries.elementEnd(t)}function mb(e,t,n,r,o,i){let s=t.consts,a=Oe(s,o),c=Mr(t,e,n,r,a);if(c.mergedAttrs=dr(c.mergedAttrs,c.attrs),i!=null){let u=Oe(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&Os(c,c.attrs,!1),c.mergedAttrs!==null&&Os(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function yb(e,t,n){return e[t]=n}function Ke(e,t,n){if(n===Me)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function iy(e,t,n,r){let o=Ke(e,t,n);return Ke(e,t+1,r)||o}function bs(e,t,n){return function r(o){let i=lt(e)?Ue(e.index,t):t;gd(i,5);let s=t[K],a=Vh(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Vh(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Vh(e,t,n,r){let o=M(null);try{return V(6,t,n),n(r)!==!1}catch(i){return cw(e,i),!1}finally{V(7,t,n),M(o)}}function sy(e,t,n,r,o,i,s,a){let c=lo(e),u=!1,l=null;if(!r&&c&&(l=vb(t,n,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Ve(e,n),p=r?r(d):d;HI(n,p,i,a);let f=o.listen(p,i,a),g=r?D=>r(z(D[e.index])):e.index;ay(g,t,n,i,a,f,!1)}return u}function vb(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Cn],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function ay(e,t,n,r,o,i,s){let a=t.firstCreatePass?cu(t):null,c=au(n),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Uh(e,t,n,r,o,i){let s=t[n],a=t[I],u=a.data[n].outputs[r],d=s[u].subscribe(i);ay(e.index,a,t,o,i,d,!0)}var fl=Symbol("BINDING");var Fs=class extends Po{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=ot(t);return new jn(n,this.ngModule)}};function Eb(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Xs.SignalBased)!==0};return o&&(i.transform=o),i})}function Db(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function _b(e,t,n){let r=t instanceof ge?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new dl(n,r):n}function Ib(e){let t=e.get(Nt,null);if(t===null)throw new _(407,!1);let n=e.get(ey,null),r=e.get(Ae,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function Cb(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return nd(t,n,n==="svg"?eu:n==="math"?Gp:null)}var jn=class extends aa{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Eb(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Db(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=AC(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){V(22);let a=M(null);try{let c=this.componentDef,u=wb(r,c,s,i),l=_b(c,o||this.ngModule,t),d=Ib(l),p=d.rendererFactory.createRenderer(null,c),f=r?QC(p,r,c.encapsulation,l):Cb(c,p),g=s?.some(Hh)||i?.some(m=>typeof m!="function"&&m.bindings.some(Hh)),D=od(null,u,null,512|dm(c),null,null,d,p,l,null,Lg(f,l,!0));D[x]=f,ds(D);let y=null;try{let m=ry(x,D,2,"#host",()=>u.directiveRegistry,!0,0);f&&(lm(p,f,m),fr(f,D)),dd(u,D,m),Gg(u,m,D),oy(u,m),n!==void 0&&Tb(m,this.ngContentSelectors,n),y=Ue(m.index,D),D[K]=y[K],pd(u,D,null)}catch(m){throw y!==null&&Gu(y),Gu(D),m}finally{V(23),fs()}return new ks(this.componentType,D,!!g)}finally{M(a)}}};function wb(e,t,n,r){let o=e?["ng-version","20.1.2"]:RC(t.selectors[0]),i=null,s=null,a=0;if(n)for(let l of n)a+=l[fl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let p of d.bindings){a+=p[fl].requiredVars;let f=l+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,p=zc(d);c.push(p)}return rd(0,null,bb(i,s),1,a,c,null,null,null,[o],null)}function bb(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Hh(e){let t=e[fl].kind;return t==="input"||t==="twoWay"}var ks=class extends Xm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=fo(n[I],x),this.location=Er(this._tNode,n),this.instance=Ue(this._tNode.index,n)[K],this.hostView=this.changeDetectorRef=new Kt(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=fd(r,o[I],o,t,n);this.previousInputValues.set(t,n);let s=Ue(r.index,o);gd(s,1)}get injector(){return new kn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Tb(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Nr=(()=>{class e{static __NG_ELEMENT_ID__=Mb}return e})();function Mb(){let e=ae();return uy(e,b())}var Sb=Nr,cy=class extends Sb{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Er(this._hostTNode,this._hostLView)}get injector(){return new kn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ol(this._hostTNode,this._hostLView);if(ug(t)){let n=Ss(t,this._hostLView),r=Ms(t),o=n[I].data[r+8];return new kn(o,n)}else return new kn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=$h(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-se}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Co(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,pr(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!cI(t),u;if(c)u=n;else{let y=n||{};u=y.index,r=y.injector,o=y.projectableNodes,i=y.environmentInjector||y.ngModuleRef,s=y.directives,a=y.bindings}let l=c?t:new jn(ot(t)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let m=(c?d:this.parentInjector).get(ge,null);m&&(i=m)}let p=ot(l.componentType??{}),f=Co(this._lContainer,p?.id??null),g=f?.firstChild??null,D=l.create(d,o,g,i,s,a);return this.insertImpl(D.hostView,u,pr(this._hostTNode,f)),D}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(qp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[te],u=new cy(c,c[Ie],c[te]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return ko(s,o,i,r),t.attachToViewContainerRef(),Vc(Fu(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=$h(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Io(this._lContainer,n);r&&(so(Fu(this._lContainer),n),ea(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=Io(this._lContainer,n);return r&&so(Fu(this._lContainer),n)!=null?new Kt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function $h(e){return e[uo]}function Fu(e){return e[uo]||(e[uo]=[])}function uy(e,t){let n,r=t[e.index];return me(r)?n=r:(n=Fm(r,t,null,e),t[e.index]=n,id(t,n)),ly(n,t,e,r),new cy(n,e,t)}function Nb(e,t){let n=e[k],r=n.createComment(""),o=Ve(t,e),i=n.parentNode(o);return Rs(n,i,r,n.nextSibling(o),!1),r}var ly=dy,yd=()=>!1;function Ab(e,t,n){return yd(e,t,n)}function dy(e,t,n,r){if(e[ct])return;let o;n.type&8?o=z(r):o=Nb(t,n),e[ct]=o}function Rb(e,t,n){if(e[ct]&&e[Sn])return!0;let r=n[Te],o=t.index-x;if(!r||_r(t)||Ug(r,o))return!1;let s=qu(r,o),a=r.data[wr]?.[o],[c,u]=tb(s,a);return e[ct]=c,e[Sn]=u,!0}function xb(e,t,n,r){yd(e,n,t)||dy(e,t,n,r)}function fy(){ly=xb,yd=Rb}var pl=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},hl=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Ed(t,n).matches!==null&&this.queries[n].setDirty()}},Ps=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Lb(t):this.predicate=t}},gl=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ml=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Ob(n,i)),this.matchTNodeWithReadOption(t,n,Is(n,t,i,!1,!1))}else r===gr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Is(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===he||o===Nr||o===gr&&n.type&4)this.addMatch(n.index,-2);else{let i=Is(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Ob(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Fb(e,t){return e.type&11?Er(e,t):e.type&4?oa(e,t):null}function kb(e,t,n,r){return n===-1?Fb(t,e):n===-2?Pb(e,t,r):Do(e,e[I],n,t)}function Pb(e,t,n){if(n===he)return Er(t,e);if(n===gr)return oa(t,e);if(n===Nr)return uy(t,e)}function py(e,t,n,r){let o=t[at].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(kb(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function yl(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=py(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=se;d<l.length;d++){let p=l[d];p[$t]===p[te]&&yl(p[I],p,u,r)}if(l[Nn]!==null){let d=l[Nn];for(let p=0;p<d.length;p++){let f=d[p];yl(f[I],f,u,r)}}}}}return r}function vd(e,t){return e[at].queries[t].queryList}function hy(e,t,n){let r=new Ln((n&4)===4);return Kp(e,t,r,r.destroy),(t[at]??=new hl).queries.push(new pl(r))-1}function gy(e,t,n){let r=q();return r.firstCreatePass&&(yy(r,new Ps(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),hy(r,b(),t)}function my(e,t,n,r){let o=q();if(o.firstCreatePass){let i=ae();yy(o,new Ps(t,n,r),i.index),jb(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return hy(o,b(),n)}function Lb(e){return e.split(",").map(t=>t.trim())}function yy(e,t,n){e.queries===null&&(e.queries=new gl),e.queries.track(new ml(t,n))}function jb(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Ed(e,t){return e.queries.getByIndex(t)}function vy(e,t){let n=e[I],r=Ed(n,t);return r.crossesNgTemplate?yl(n,e,t,[]):py(n,e,r,t)}function Ey(e,t,n){let r,o=Jr(()=>{r._dirtyCounter();let i=Bb(r,e);if(t&&i===void 0)throw new _(-951,!1);return i});return r=o[ie],r._dirtyCounter=pt(0),r._flatValue=void 0,o}function Dd(e){return Ey(!0,!1,e)}function _d(e){return Ey(!0,!0,e)}function Dy(e,t){let n=e[ie];n._lView=b(),n._queryIndex=t,n._queryList=vd(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function Bb(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[w]&4)return t?void 0:de;let o=vd(n,r),i=vy(n,r);return o.reset(i,vg),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function _y(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(Ub))}return i}return Ls.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(u=>{o.template=u}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let u=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,p)=>{a.push(""),s.push(r(d).then(f=>{a[u+p]=f,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(u=>{a.push(u),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>Hb(i));t.push(c)}),Iy(),Promise.all(t).then(()=>{})}var Ls=new Map,Vb=new Set;function Iy(){let e=Ls;return Ls=new Map,e}function Cy(){return Ls.size===0}function Ub(e){return typeof e=="string"?e:e.text()}function Hb(e){Vb.delete(e)}var zh=new Set;function ke(e){zh.has(e)||(zh.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Bn=class{},wy=class{};var wo=class extends Bn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Fs(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=$c(t);this._bootstrapComponents=nm(i.bootstrap),this._r3Injector=_u(t,n,[{provide:Bn,useValue:this},{provide:Po,useValue:this.componentFactoryResolver},...r],wt(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},bo=class extends wy{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new wo(this.moduleType,t,[])}};function by(e,t,n){return new wo(e,t,n,!1)}var To=class extends Bn{injector;componentFactoryResolver=new Fs(this);instance=null;constructor(t){super();let n=new _n([...t.providers,{provide:Bn,useValue:this},{provide:Po,useValue:this.componentFactoryResolver}],t.parent||tr(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Ty(e,t,n=null){return new To({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var $b=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Gc(!1,n.type),o=r.length>0?Ty([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=E({token:e,providedIn:"environment",factory:()=>new e(C(ge))})}return e})();function Lo(e){return vr(()=>{let t=My(e),n=Q(P({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get($b).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||$e.Emulated,styles:e.styles||de,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&ke("NgStandalone"),Sy(n);let r=e.dependencies;return n.directiveDefs=Gh(r,zb),n.pipeDefs=Gh(r,jp),n.id=Zb(n),n})}function zb(e){return ot(e)||zc(e)}function ye(e){return vr(()=>({type:e.type,bootstrap:e.bootstrap||de,declarations:e.declarations||de,imports:e.imports||de,exports:e.exports||de,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Gb(e,t){if(e==null)return Ht;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Xs.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Wb(e){if(e==null)return Ht;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function yt(e){return vr(()=>{let t=My(e);return Sy(t),t})}function Id(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function My(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ht,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||de,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Gb(e.inputs,t),outputs:Wb(e.outputs),debugInfo:null}}function Sy(e){e.features?.forEach(t=>t(e))}function Gh(e,t){return e?()=>{let n=typeof e=="function"?e():e,r=[];for(let o of n){let i=t(o);i!==null&&r.push(i)}return r}:null}var qb=new Map;function Zb(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Yb(e){return Object.getPrototypeOf(e.prototype).constructor}function Ny(e){let t=Yb(e.type),n=!0,r=[e];for(;t;){let o;if(dt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ku(e.inputs),s.declaredInputs=ku(e.declaredInputs),s.outputs=ku(e.outputs);let a=o.hostBindings;a&&eT(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Jb(e,c),u&&Xb(e,u),Kb(e,o),Sp(e.outputs,o.outputs),dt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ny&&(n=!1)}}t=Object.getPrototypeOf(t)}Qb(r)}function Kb(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Qb(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=dr(o.hostAttrs,n=dr(n,o.hostAttrs))}}function ku(e){return e===Ht?{}:e===de?[]:e}function Jb(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Xb(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function eT(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Ay(e,t,n,r,o,i,s,a){if(n.firstCreatePass){e.mergedAttrs=dr(e.mergedAttrs,e.attrs);let l=e.tView=rd(2,e,o,i,s,n.directiveRegistry,n.pipeRegistry,null,n.schemas,n.consts,null);n.queries!==null&&(n.queries.template(n,e),l.queries=n.queries.embeddedTView(e))}a&&(e.flags|=a),or(e,!1);let c=Oy(n,t,e,r);ps()&&ud(n,t,c,e),fr(c,t);let u=Fm(c,t,c,e);t[r+x]=u,id(t,u),Ab(u,e,t)}function tT(e,t,n,r,o,i,s,a,c,u,l){let d=n+x,p;return t.firstCreatePass?(p=Mr(t,d,4,s||null,a||null),lu()&&ty(t,e,p,Oe(t.consts,u),Cm),ig(t,p)):p=t.data[d],Ay(p,e,t,n,r,o,i,c),lo(p)&&dd(t,e,p),u!=null&&na(e,p,l),p}function mr(e,t,n,r,o,i,s,a,c,u,l){let d=n+x,p;if(t.firstCreatePass){if(p=Mr(t,d,4,s||null,a||null),u!=null){let f=Oe(t.consts,u);p.localNames=[];for(let g=0;g<f.length;g+=2)p.localNames.push(f[g],-1)}}else p=t.data[d];return Ay(p,e,t,n,r,o,i,c),u!=null&&na(e,p,l),p}function Ry(e,t,n,r,o,i,s,a){let c=b(),u=q(),l=Oe(u.consts,i);return tT(c,u,e,t,n,r,o,l,void 0,s,a),Ry}function xy(e,t,n,r,o,i,s,a){let c=b(),u=q(),l=Oe(u.consts,i);return mr(c,u,e,t,n,r,o,l,void 0,s,a),xy}var Oy=Fy;function Fy(e,t,n,r){return Mt(!0),t[k].createComment("")}function nT(e,t,n,r){let o=!Ks(t,n);Mt(o);let i=t[Te]?.data[Ws]?.[r]??null;if(i!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=i),o)return Fy(e,t);let s=t[Te],a=ia(s,e,t,n);Ys(s,r,a);let c=ql(s,r);return sa(c,a)}function ky(){Oy=nT}var Cd=1;var ua=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(ua||{}),Jt=new v(""),Py=!1,vl=class extends H{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Zc()&&(this.destroyRef=h(He,{optional:!0})??void 0,this.pendingTasks=h(Zt,{optional:!0})??void 0)}emit(t){let n=M(null);try{super.next(t)}finally{M(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Z&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Ce=vl;function Ly(e){let t,n;function r(){e=xn;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Wh(e){return queueMicrotask(()=>e()),()=>{e=xn}}var wd="isAngularZone",js=wd+"_ID",rT=0,F=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Ce(!1);onMicrotaskEmpty=new Ce(!1);onStable=new Ce(!1);onError=new Ce(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Py}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,sT(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(wd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,oT,xn,xn);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},oT={};function bd(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function iT(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Ly(()=>{e.callbackScheduled=!1,El(e),e.isCheckStableRunning=!0,bd(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),El(e)}function sT(e){let t=()=>{iT(e)},n=rT++;e._inner=e._inner.fork({name:"angular",properties:{[wd]:!0,[js]:n,[js+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(aT(c))return r.invokeTask(i,s,a,c);try{return qh(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Zh(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return qh(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!cT(c)&&t(),Zh(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,El(e),bd(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function El(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function qh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Zh(e){e._nesting--,bd(e)}var Mo=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Ce;onMicrotaskEmpty=new Ce;onStable=new Ce;onError=new Ce;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function aT(e){return jy(e,"__ignore_ng_zone__")}function cT(e){return jy(e,"__scheduler_tick__")}function jy(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function By(e="zone.js",t){return e==="noop"?new Mo:e==="zone.js"?new F(t):e}var la=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),Td=[0,1,2,3],Md=(()=>{class e{ngZone=h(F);scheduler=h(Ae);errorHandler=h(fe,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){h(Jt,{optional:!0})}execute(){let n=this.sequences.size>0;n&&V(16),this.executing=!0;for(let r of Td)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&V(17)}register(n){let{view:r}=n;r!==void 0?((r[Mn]??=[]).push(n),Gt(r),r[w]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(ua.AFTER_NEXT_RENDER,n):n()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),So=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Mn];t&&(this.view[Mn]=t.filter(n=>n!==this))}};function Sd(e,t){let n=t?.injector??h(Y);return Nd}function uT(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function lT(e,t,n,r){let o=t.get(la);o.impl??=t.get(Md);let i=t.get(Jt,null,{optional:!0}),s=n?.manualCleanup!==!0?t.get(He):null,a=t.get(Rn,null,{optional:!0}),c=new So(o.impl,uT(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Nd={destroy(){}};function Ad(e){return e+1}function Vy(e,t){let n=e[I],r=Ad(t.index);return e[r]}function Rd(e,t){let n=Ad(t.index);return e.data[n]}function dT(e){return e!==null&&typeof e=="object"&&typeof e.primaryTmplIndex=="number"}function Uy(e,t){let n=null,r=Ad(t.index);return x<r&&r<e.bindingStartIndex&&(n=Rd(e,t)),!!n&&dT(n)}var Hy=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var xd=new v(""),Od=new v(""),$y=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];_taskTrackingZone=null;_destroyRef;constructor(n,r,o){this._ngZone=n,this.registry=r,Zc()&&(this._destroyRef=h(He,{optional:!0})??void 0),kd||(zy(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this._taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){let n=this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),r=this._ngZone.runOutsideAngular(()=>this._ngZone.onStable.subscribe({next:()=>{F.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}}));this._destroyRef?.onDestroy(()=>{n.unsubscribe(),r.unsubscribe()})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this._taskTrackingZone?this._taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this._taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||e)(C(F),C(Fd),C(Od))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Fd=(()=>{class e{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return kd?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function zy(e){kd=e}var kd;function da(e){return!!e&&typeof e.then=="function"}function Pd(e){return!!e&&typeof e.subscribe=="function"}var Gy=new v("");var Ld=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=h(Gy,{optional:!0})??[];injector=h(Y);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=nr(this.injector,o);if(da(i))n.push(i);else if(Pd(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),fa=new v("");function jd(){mc(()=>{let e="";throw new _(600,e)})}function Wy(e){return e.isBoundToModule}var fT=10;function Bd(e,t){return Array.isArray(t)?t.reduce(Bd,e):P(P({},e),t)}var Qe=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=h(qe);afterRenderManager=h(la);zonelessEnabled=h(ar);rootEffectScheduler=h(vo);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new H;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=h(Zt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(W(n=>!n))}constructor(){h(Jt,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=h(ge);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Y.NULL){return this._injector.get(F).run(()=>{V(10);let s=n instanceof aa;if(!this._injector.get(Ld).done){let g="";throw new _(405,g)}let c;s?c=n:c=this._injector.get(Po).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let u=Wy(c)?void 0:this._injector.get(Bn),l=r||c.selector,d=c.create(o,[],l,u),p=d.location.nativeElement,f=d.injector.get(xd,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),Eo(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),V(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){V(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(ua.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=M(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,M(n),this.afterTick.next(),V(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Nt,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<fT;)V(14),this.synchronizeOnce(),V(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!po(o))continue;let i=r&&!this.zonelessEnabled?0:1;hd(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>po(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Eo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(fa,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Eo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Eo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function jo(e,t,n,r){let o=b(),i=Wt();if(Ke(o,i,t)){let s=q(),a=mo();iw(a,o,e,t,n,r)}return jo}var Dl=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Pu(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function pT(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],d=Pu(i,u,i,l,n);if(d!==0){d<0&&e.updateValue(i,l),i++;continue}let p=e.at(s),f=t[c],g=Pu(s,p,c,f,n);if(g!==0){g<0&&e.updateValue(s,f),s--,c--;continue}let D=n(i,u),y=n(s,p),m=n(i,l);if(Object.is(m,y)){let L=n(c,f);Object.is(L,D)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new Bs,o??=Kh(e,i,s,n),_l(e,r,i,m))e.updateValue(i,l),i++,s++;else if(o.has(m))r.set(D,e.detach(i)),s--;else{let L=e.create(i,t[i]);e.attach(i,L),i++,s++}}for(;i<=c;)Yh(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),d=u.value,p=Pu(i,l,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,u=c.next();else{r??=new Bs,o??=Kh(e,i,s,n);let f=n(i,d);if(_l(e,r,i,f))e.updateValue(i,d),i++,s++,u=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,u=c.next();else{let g=n(i,l);r.set(g,e.detach(i)),s--}}}for(;!u.done;)Yh(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function _l(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Yh(e,t,n,r,o){if(_l(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Kh(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Bs=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function hT(e,t,n,r,o,i,s,a){ke("NgControlFlow");let c=b(),u=q(),l=Oe(u.consts,i);return mr(c,u,e,t,n,r,o,l,256,s,a),Vd}function Vd(e,t,n,r,o,i,s,a){ke("NgControlFlow");let c=b(),u=q(),l=Oe(u.consts,i);return mr(c,u,e,t,n,r,o,l,512,s,a),Vd}function gT(e,t){ke("NgControlFlow");let n=b(),r=Wt(),o=n[r]!==Me?n[r]:-1,i=o!==-1?Vs(n,x+o):void 0,s=0;if(Ke(n,r,e)){let a=M(null);try{if(i!==void 0&&Pm(i,s),e!==-1){let c=x+e,u=Vs(n,c),l=bl(n[I],c),d=Qm(u,l,n),p=Fo(n,l,t,{dehydratedView:d});ko(u,p,s,pr(l,d))}}finally{M(a)}}else if(i!==void 0){let a=km(i,s);a!==void 0&&(a[K]=t)}}var Il=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-se}};function mT(e){return e}function yT(e,t){return t}var Cl=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function vT(e,t,n,r,o,i,s,a,c,u,l,d,p){ke("NgControlFlow");let f=b(),g=q(),D=c!==void 0,y=b(),m=a?s.bind(y[ce][K]):s,L=new Cl(D,m);y[x+e]=L,mr(f,g,e+1,t,n,r,o,Oe(g.consts,i),256),D&&mr(f,g,e+2,c,u,l,d,Oe(g.consts,p),512)}var wl=class extends Dl{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-se}at(t){return this.getLView(t)[K].$implicit}attach(t,n){let r=n[Te];this.needsIndexUpdate||=t!==this.length,ko(this.lContainer,n,t,pr(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,DT(this.lContainer,t)}create(t,n){let r=Co(this.lContainer,this.templateTNode.tView.ssrId),o=Fo(this.hostLView,this.templateTNode,new Il(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ea(t[I],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[K].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[K].$index=t}getLView(t){return _T(this.lContainer,t)}};function ET(e){let t=M(null),n=ft();try{let r=b(),o=r[I],i=r[n],s=n+1,a=Vs(r,s);if(i.liveCollection===void 0){let u=bl(o,s);i.liveCollection=new wl(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(pT(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Wt(),l=c.length===0;if(Ke(r,u,l)){let d=n+2,p=Vs(r,d);if(l){let f=bl(o,d),g=Qm(p,f,r),D=Fo(r,f,void 0,{dehydratedView:g});ko(p,D,0,pr(f,g))}else o.firstUpdatePass&&md(p),Pm(p,0)}}}finally{M(t)}}function Vs(e,t){return e[t]}function DT(e,t){return Io(e,t)}function _T(e,t){return km(e,t)}function bl(e,t){return fo(e,t)}function qy(e,t,n){let r=b(),o=Wt();if(Ke(r,o,t)){let i=q(),s=mo();_m(s,r,e,t,r[k],n)}return qy}function Tl(e,t,n,r,o){fd(t,e,n,o?"class":"style",r)}function Ud(e,t,n,r){let o=b(),i=o[I],s=e+x,a=i.firstCreatePass?ry(s,o,2,t,Cm,lu(),n,r):i.data[s];if(wm(a,o,e,t,Gd),lo(a)){let c=o[I];dd(c,o,a),Gg(c,a,o)}return r!=null&&na(o,a),Ud}function Hd(){let e=q(),t=ae(),n=bm(t);return e.firstCreatePass&&oy(e,n),du(n)&&fu(),uu(),n.classesWithoutHost!=null&&hI(n)&&Tl(e,n,b(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&gI(n)&&Tl(e,n,b(),n.stylesWithoutHost,!1),Hd}function Zy(e,t,n,r){return Ud(e,t,n,r),Hd(),Zy}function $d(e,t,n,r){let o=b(),i=o[I],s=e+x,a=i.firstCreatePass?mb(s,i,2,t,n,r):i.data[s];return wm(a,o,e,t,Gd),r!=null&&na(o,a),$d}function zd(){let e=ae(),t=bm(e);return du(t)&&fu(),uu(),zd}function Yy(e,t,n,r){return $d(e,t,n,r),zd(),Yy}var Gd=(e,t,n,r,o)=>(Mt(!0),nd(t[k],r,Du()));function IT(e,t,n,r,o){let i=!Ks(t,n);if(Mt(i),i)return nd(t[k],r,Du());let s=t[Te],a=ia(s,e,t,n);return Vg(s,o)&&Ys(s,o,a.nextSibling),s&&(kl(n)||Eg(a))&&lt(n)&&(Xp(n),um(a)),a}function Ky(){Gd=IT}var CT=(e,t,n,r,o)=>(Mt(!0),sm(t[k],""));function wT(e,t,n,r,o){let i,s=!Ks(t,n);if(Mt(s),s)return sm(t[k],"");let a=t[Te],c=ia(a,e,t,n),u=ZI(a,o);return Ys(a,o,c),i=sa(u,c),i}function Qy(){CT=wT}function bT(){return b()}function Jy(e,t,n){let r=b(),o=Wt();if(Ke(r,o,t)){let i=q(),s=mo();Im(s,r,e,t,r[k],n)}return Jy}var On=void 0;function TT(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var MT=["en",[["a","p"],["AM","PM"],On],[["AM","PM"],On,On],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],On,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],On,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",On,"{1} 'at' {0}",On],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",TT],Lu={};function Pe(e){let t=ST(e),n=Qh(t);if(n)return n;let r=t.split("-")[0];if(n=Qh(r),n)return n;if(r==="en")return MT;throw new _(701,!1)}function Qh(e){return e in Lu||(Lu[e]=Re.ng&&Re.ng.common&&Re.ng.common.locales&&Re.ng.common.locales[e]),Lu[e]}var X=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(X||{});function ST(e){return e.toLowerCase().replace(/_/g,"-")}var Bo="en-US";var NT=Bo;function Xy(e){typeof e=="string"&&(NT=e.toLowerCase().replace(/_/g,"-"))}function ev(e,t,n){let r=b(),o=q(),i=ae();return nv(o,r,r[k],i,e,t,n),ev}function tv(e,t,n){let r=b(),o=q(),i=ae();return(i.type&3||n)&&sy(i,o,r,n,r[k],e,t,bs(i,r,t)),tv}function nv(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=bs(r,t,i),sy(r,e,t,s,n,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let p=l[d],f=l[d+1];c??=bs(r,t,i),Uh(r,t,p,f,o,c)}if(u&&u.length)for(let d of u)c??=bs(r,t,i),Uh(r,t,d,o,o,c)}}function AT(e=1){return hh(e)}function RT(e,t){let n=null,r=bC(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?om(e,i,!0):SC(r,i))return o}return n}function Wd(e){let t=b()[ce][Ie];if(!t.projection){let n=e?e.length:1,r=t.projection=Pp(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?RT(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function qd(e,t=0,n,r,o,i){let s=b(),a=q(),c=r?e+1:null;c!==null&&mr(s,a,c,r,o,i,null,n);let u=Mr(a,x+e,16,null,n||null);u.projection===null&&(u.projection=t),gu();let d=!s[Te]||as();s[ce][Ie].projection[u.projection]===null&&c!==null?xT(s,a,c):d&&!Tr(u)&&ZC(a,s,u)}function xT(e,t,n){let r=x+n,o=t.data[r],i=e[r],s=Co(i,o.tView.ssrId),a=Fo(e,o,void 0,{dehydratedView:s});ko(i,a,0,pr(o,s))}function OT(e,t,n,r){my(e,t,n,r)}function FT(e,t,n){gy(e,t,n)}function kT(e){let t=b(),n=q(),r=ls();go(r+1);let o=Ed(n,r);if(e.dirty&&Wp(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=vy(t,r);e.reset(i,vg),e.notifyOnChanges()}return!0}return!1}function PT(){return vd(b(),ls())}function LT(e,t,n,r,o){Dy(t,my(e,n,r,o))}function jT(e,t,n,r){Dy(e,gy(t,n,r))}function BT(e=1){go(ls()+e)}function VT(e){let t=rh();return nu(t,x+e)}function vs(e,t){return e<<17|t<<2}function Vn(e){return e>>17&32767}function UT(e){return(e&2)==2}function HT(e,t){return e&131071|t<<17}function Ml(e){return e|2}function yr(e){return(e&131068)>>2}function ju(e,t){return e&-131069|t<<2}function $T(e){return(e&1)===1}function Sl(e){return e|1}function zT(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Vn(s),c=yr(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||er(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let p=Vn(e[a+1]);e[r+1]=vs(p,a),p!==0&&(e[p+1]=ju(e[p+1],r)),e[a+1]=HT(e[a+1],r)}else e[r+1]=vs(a,0),a!==0&&(e[a+1]=ju(e[a+1],r)),a=r;else e[r+1]=vs(c,0),a===0?a=r:e[c+1]=ju(e[c+1],r),c=r;u&&(e[r+1]=Ml(e[r+1])),Jh(e,l,r,!0),Jh(e,l,r,!1),GT(t,l,e,r,i),s=vs(a,c),i?t.classBindings=s:t.styleBindings=s}function GT(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&er(i,t)>=0&&(n[r+1]=Sl(n[r+1]))}function Jh(e,t,n,r){let o=e[n+1],i=t===null,s=r?Vn(o):yr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];WT(c,t)&&(a=!0,e[s+1]=r?Sl(u):Ml(u)),s=r?Vn(u):yr(u)}a&&(e[n+1]=r?Ml(o):Sl(o))}function WT(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?er(e,t)>=0:!1}var Ye={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function qT(e){return e.substring(Ye.key,Ye.keyEnd)}function ZT(e){return YT(e),rv(e,ov(e,0,Ye.textEnd))}function rv(e,t){let n=Ye.textEnd;return n===t?-1:(t=Ye.keyEnd=KT(e,Ye.key=t,n),ov(e,t,n))}function YT(e){Ye.key=0,Ye.keyEnd=0,Ye.value=0,Ye.valueEnd=0,Ye.textEnd=e.length}function ov(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function KT(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function iv(e,t,n){return sv(e,t,n,!1),iv}function pa(e,t){return sv(e,t,null,!0),pa}function Zd(e){JT(oM,QT,e,!0)}function QT(e,t){for(let n=ZT(t);n>=0;n=rv(t,n))ts(e,qT(t),!0)}function sv(e,t,n,r){let o=b(),i=q(),s=cs(2);if(i.firstUpdatePass&&cv(i,e,s,r),t!==Me&&Ke(o,s,t)){let a=i.data[ft()];uv(i,a,o,o[k],e,o[s+1]=sM(t,n),r,s)}}function JT(e,t,n,r){let o=q(),i=cs(2);o.firstUpdatePass&&cv(o,null,i,r);let s=b();if(n!==Me&&Ke(s,i,n)){let a=o.data[ft()];if(lv(a,r)&&!av(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Ki(c,n||"")),Tl(o,a,s,n,r)}else iM(o,a,s,s[k],s[i+1],s[i+1]=rM(e,t,n),r,i)}}function av(e,t){return t>=e.expandoStartIndex}function cv(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[ft()],s=av(e,n);lv(i,r)&&t===null&&!s&&(t=!1),t=XT(o,i,t,r),zT(o,i,t,n,s,r)}}function XT(e,t,n,r){let o=lh(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Bu(null,e,t,n,r),n=No(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Bu(o,e,t,n,r),i===null){let c=eM(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Bu(null,e,t,c[1],r),c=No(c,t.attrs,r),tM(e,t,r,c))}else i=nM(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function eM(e,t,n){let r=n?t.classBindings:t.styleBindings;if(yr(r)!==0)return e[Vn(r)]}function tM(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Vn(o)]=r}function nM(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=No(r,s,n)}return No(r,t.attrs,n)}function Bu(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=No(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function No(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ts(e,s,n?!0:t[++i]))}return e===void 0?null:e}function rM(e,t,n){if(n==null||n==="")return de;let r=[],o=Fe(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function oM(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ts(e,r,n)}function iM(e,t,n,r,o,i,s,a){o===Me&&(o=de);let c=0,u=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=u<i.length?i[u+1]:void 0,g=null,D;l===d?(c+=2,u+=2,p!==f&&(g=d,D=f)):d===null||l!==null&&l<d?(c+=2,g=l):(u+=2,g=d,D=f),g!==null&&uv(e,t,n,r,g,D,s,a),l=c<o.length?o[c]:null,d=u<i.length?i[u]:null}}function uv(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=$T(u)?Xh(c,t,n,o,yr(u),s):void 0;if(!Us(l)){Us(i)||UT(u)&&(i=Xh(c,null,n,o,a,s));let d=tu(ft(),n);KC(r,s,d,o,i)}}function Xh(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,p=n[o+1];p===Me&&(p=d?de:void 0);let f=d?ns(p,r):l===r?p:void 0;if(u&&!Us(f)&&(f=ns(c,r)),Us(f)&&(a=f,s))return a;let g=e[o+1];o=s?Vn(g):yr(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ns(c,r))}return a}function Us(e){return e!==void 0}function sM(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=wt(Fe(e)))),e}function lv(e,t){return(e.flags&(t?8:16))!==0}function aM(e,t=""){let n=b(),r=q(),o=e+x,i=r.firstCreatePass?Mr(r,o,1,t,null):r.data[o],s=dv(r,n,i,t,e);n[o]=s,ps()&&ud(r,n,s,i),or(i,!1)}var dv=(e,t,n,r,o)=>(Mt(!0),im(t[k],r));function cM(e,t,n,r,o){let i=!Ks(t,n);if(Mt(i),i)return im(t[k],r);let s=t[Te];return ia(s,e,t,n)}function fv(){dv=cM}function pv(e,t,n,r=""){return Ke(e,Wt(),n)?t+bt(n)+r:Me}function uM(e,t,n,r,o,i=""){let s=ih(),a=iy(e,s,n,o);return cs(2),a?t+bt(n)+r+bt(o)+i:Me}function hv(e){return Yd("",e),hv}function Yd(e,t,n){let r=b(),o=pv(r,e,t,n);return o!==Me&&mv(r,ft(),o),Yd}function gv(e,t,n,r,o){let i=b(),s=uM(i,e,t,n,r,o);return s!==Me&&mv(i,ft(),s),gv}function mv(e,t,n){let r=tu(t,e);xC(e[k],r,n)}function yv(e,t,n){wu(t)&&(t=t());let r=b(),o=Wt();if(Ke(r,o,t)){let i=q(),s=mo();_m(s,r,e,t,r[k],n)}return yv}function lM(e,t){let n=wu(e);return n&&e.set(t),n}function vv(e,t){let n=b(),r=q(),o=ae();return nv(r,n,n[k],o,e,t),vv}function dM(e,t,n=""){return pv(b(),e,t,n)}function fM(e,t,n){let r=q();if(r.firstCreatePass){let o=dt(e);Nl(n,r.data,r.blueprint,o,!0),Nl(t,r.data,r.blueprint,o,!1)}}function Nl(e,t,n,r,o){if(e=le(e),Array.isArray(e))for(let i=0;i<e.length;i++)Nl(e[i],t,n,r,o);else{let i=q(),s=b(),a=ae(),c=Dn(e)?e:le(e.provide),u=qc(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Dn(e)||!e.multi){let f=new Pn(u,o,ze,null),g=Uu(c,t,o?l:l+p,d);g===-1?($u(As(a,s),i,c),Vu(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[g]=f,s[g]=f)}else{let f=Uu(c,t,l+p,d),g=Uu(c,t,l,l+p),D=f>=0&&n[f],y=g>=0&&n[g];if(o&&!y||!o&&!D){$u(As(a,s),i,c);let m=gM(o?hM:pM,n.length,o,r,u,e);!o&&y&&(n[g].providerFactory=m),Vu(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(m),s.push(m)}else{let m=Ev(n[o?g:f],u,!o&&r);Vu(i,e,f>-1?f:g,m)}!o&&r&&y&&n[g].componentProviders++}}}function Vu(e,t,n,r){let o=Dn(t),i=$p(t);if(o||i){let c=(i?le(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function Ev(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Uu(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function pM(e,t,n,r,o){return Al(this.multi,[])}function hM(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Do(r,r[I],this.providerFactory.index,o);s=c.slice(0,a),Al(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Al(i,s);return s}function Al(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function gM(e,t,n,r,o,i){let s=new Pn(e,n,ze,null);return s.multi=[],s.index=t,s.componentProviders=0,Ev(s,o,r&&!n),s}function Dv(e,t=[]){return n=>{n.providersResolver=(r,o)=>fM(r,o?o(e):e,t)}}function mM(e,t){let n=e[t];return n===Me?void 0:n}function yM(e,t,n,r,o,i,s){let a=t+n;return iy(e,a,o,i)?yb(e,a+2,s?r.call(s,o,i):r(o,i)):mM(e,a+2)}function vM(e,t){let n=q(),r,o=e+x;n.firstCreatePass?(r=EM(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Vt(r.type,!0)),s,a=_e(ze);try{let c=Ns(!1),u=i();return Ns(c),ru(n,b(),o,u),u}finally{_e(a)}}function EM(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function DM(e,t,n,r){let o=e+x,i=b(),s=nu(i,o);return _M(i,o)?yM(i,oh(),t,s.transform,n,r,s):s.transform(n,r)}function _M(e,t){return e[I].data[t].pure}function IM(e,t){return oa(e,t)}var Es=null;function _v(e){Es!==null&&(e.defaultEncapsulation!==Es.defaultEncapsulation||e.preserveWhitespaces!==Es.preserveWhitespaces)||(Es=e)}var CM=[];var wM=new WeakMap,bM=new WeakMap;function TM(){wM=new WeakMap,bM=new WeakMap,CM.length=0,qb.clear()}var Hs=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},MM=(()=>{class e{compileModuleSync(n){return new bo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=$c(n),i=nm(o.declarations).reduce((s,a)=>{let c=ot(a);return c&&s.push(new jn(c)),s},[]);return new Hs(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Iv=new v("");var SM=(()=>{class e{zone=h(F);changeDetectionScheduler=h(Ae);applicationRef=h(Qe);applicationErrorHandler=h(qe);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Cv=new v("",{factory:()=>!1});function ha({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new F(Q(P({},ga()),{scheduleInRootZone:n})),[{provide:F,useFactory:e},{provide:rt,multi:!0,useFactory:()=>{let r=h(SM,{optional:!0});return()=>r.initialize()}},{provide:rt,multi:!0,useFactory:()=>{let r=h(AM);return()=>{r.initialize()}}},t===!0?{provide:bu,useValue:!0}:[],{provide:Tu,useValue:n??Py},{provide:qe,useFactory:()=>{let r=h(F),o=h(ge),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(fe),i.handleError(s))})}}}]}function NM(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=ha({ngZoneFactory:()=>{let o=ga(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&ke("NgZone_CoalesceEvent"),new F(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return it([{provide:Cv,useValue:!0},{provide:ar,useValue:!1},r])}function ga(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var AM=(()=>{class e{subscription=new Z;initialized=!1;zone=h(F);pendingTasks=h(Zt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{F.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{F.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Kd=(()=>{class e{applicationErrorHandler=h(qe);appRef=h(Qe);taskService=h(Zt);ngZone=h(F);zonelessEnabled=h(ar);tracing=h(Jt,{optional:!0});disableScheduling=h(bu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Z;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(js):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(h(Tu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Mo||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Wh:Ly;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(js+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Wh(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function RM(){return typeof $localize<"u"&&$localize.locale||Bo}var Vo=new v("",{providedIn:"root",factory:()=>h(Vo,{optional:!0,skipSelf:!0})||RM()});function wv(e){return bp(e)}function bv(e,t){return Jr(e,t?.equal)}var Qd=class{[ie];constructor(t){this[ie]=t}destroy(){this[ie].destroy()}};function Jd(e,t){let n=t?.injector??h(Y),r=t?.manualCleanup!==!0?n.get(He):null,o,i=n.get(Rn,null,{optional:!0}),s=n.get(Ae);return i!==null?(o=FM(i.view,s,e),r instanceof oo&&r._lView===i.view&&(r=null)):o=kM(e,n.get(vo),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Qd(o)}var Tv=Q(P({},jt),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:xn,run(){if(this.dirty=!1,this.hasRun&&!yn(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=It(this),n=ir(!1);try{this.maybeCleanup(),this.fn(e)}finally{ir(n),Bt(this,t)}},maybeCleanup(){if(!this.cleanupFns?.length)return;let e=M(null);try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[],M(e)}}}),xM=Q(P({},Tv),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Qn(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),OM=Q(P({},Tv),{consumerMarkedDirty(){this.view[w]|=8192,Gt(this.view),this.notifier.notify(13)},destroy(){Qn(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Tt]?.delete(this)}});function FM(e,t,n){let r=Object.create(OM);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Tt]??=new Set,e[Tt].add(r),r.consumerMarkedDirty(r),r}function kM(e,t,n){let r=Object.create(xM);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.add(r),r.notifier.notify(12),r}var ee={AUXCLICK:"auxclick",CHANGE:"change",CLICK:"click",CLICKMOD:"clickmod",CLICKONLY:"clickonly",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEUP:"mouseup",MOUSEDOWN:"mousedown",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",MOUSEMOVE:"mousemove",POINTERUP:"pointerup",POINTERDOWN:"pointerdown",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",POINTERMOVE:"pointermove",POINTERCANCEL:"pointercancel",GOTPOINTERCAPTURE:"gotpointercapture",LOSTPOINTERCAPTURE:"lostpointercapture",ERROR:"error",LOAD:"load",UNLOAD:"unload",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",INPUT:"input",SCROLL:"scroll",TOGGLE:"toggle",CUSTOM:"_custom"},x1=[ee.MOUSEENTER,ee.MOUSELEAVE,"pointerenter","pointerleave"],PM=[ee.CLICK,ee.DBLCLICK,ee.FOCUSIN,ee.FOCUSOUT,ee.KEYDOWN,ee.KEYUP,ee.KEYPRESS,ee.MOUSEOVER,ee.MOUSEOUT,ee.SUBMIT,ee.TOUCHSTART,ee.TOUCHEND,ee.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],Mv=[ee.FOCUS,ee.BLUR,ee.ERROR,ee.LOAD,ee.TOGGLE],Sv=e=>Mv.indexOf(e)>=0,LM=PM.concat(Mv),Nv=e=>LM.indexOf(e)>=0;var O1=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);var F1=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent);var k1=ee.CLICK;var jv=Symbol("InputSignalNode#UNSET"),QM=Q(P({},Xr),{transformFn:void 0,applyValueToInputSignal(e,t){Jn(e,t)}});function Bv(e,t){let n=Object.create(QM);n.value=e,n.transformFn=t?.transform;function r(){if(mn(n),n.value===jv){let o=null;throw new _(-950,o)}return n.value}return r[ie]=n,r}var ma=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>Fl(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},JM=new v("");JM.__NG_ELEMENT_ID__=e=>{let t=ae();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new _(204,!1)};function Av(e,t){return Bv(e,t)}function XM(e){return Bv(jv,e)}var KU=(Av.required=XM,Av);function Rv(e,t){return Dd(t)}function eS(e,t){return _d(t)}var QU=(Rv.required=eS,Rv);function xv(e,t){return Dd(t)}function tS(e,t){return _d(t)}var JU=(xv.required=tS,xv);function nS(e,t,n){let r=new bo(n);return Promise.resolve(r)}function Ov(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var ya=new v(""),rS=new v("");function Uo(e){return!e.moduleRef}function Vv(e){let t=Uo(e)?e.r3Injector:e.moduleRef.injector,n=t.get(F);return n.run(()=>{Uo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(qe),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Uo(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ya);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ya);s.add(i),e.moduleRef.onDestroy(()=>{Eo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return iS(r,n,()=>{let i=t.get(Zt),s=i.add(),a=t.get(Ld);return a.runInitializers(),a.donePromise.then(()=>{try{let c=t.get(Vo,Bo);if(Xy(c||Bo),!t.get(rS,!0))return Uo(e)?t.get(Qe):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Uo(e)){let l=t.get(Qe);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return Uv?.(e.moduleRef,e.allPlatformModules),e.moduleRef}finally{i.remove(s)}})})})}var Uv;function Fv(){Uv=oS}function oS(e,t){let n=e.injector.get(Qe);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new _(-403,!1);t.push(e)}function iS(e,t,n){try{let r=n();return da(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Hv=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,r){let o=r?.scheduleInRootZone,i=()=>By(r?.ngZone,Q(P({},ga({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[ha({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:Ae,useExisting:Kd},Iu],c=by(n.moduleType,this.injector,a);return Fv(),Vv({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let o=Bd({},r);return Fv(),nS(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new _(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(ya,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(C(Y))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),Xt=null,$v=new v("");function sS(e){if(Xt&&!Xt.get($v,!1))throw new _(400,!1);jd(),Xt=e;let t=e.get(Hv);return qv(e),t}function zv(e,t,n=[]){let r=`Platform: ${t}`,o=new v(r);return(i=[])=>{let s=Wv();if(!s||s.injector.get($v,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):sS(Gv(a,r))}return aS(o)}}function Gv(e=[],t){return Y.create({name:t,providers:[{provide:ao,useValue:"platform"},{provide:ya,useValue:new Set([()=>Xt=null])},...e]})}function aS(e){let t=Wv();if(!t)throw new _(401,!1);return t}function Wv(){return Xt?.get(Hv)??null}function cS(e=[]){if(Xt)return Xt;let t=Gv(e);return Xt=t,jd(),qv(t),t}function qv(e){let t=e.get(zs,null);nr(e,()=>{t?.forEach(n=>n())})}var Zv=(()=>{class e{static __NG_ELEMENT_ID__=uS}return e})();function uS(e){return lS(ae(),b(),(e&16)===16)}function lS(e,t,n){if(lt(e)&&!n){let r=Ue(e.index,t);return new Kt(r,r)}else if(e.type&175){let r=t[ce];return new Kt(r,t)}return null}var dS=zv(null,"core",[]);function Yv(e){V(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=cS(r),i=[ha({}),{provide:Ae,useExisting:Kd},Iu,...n||[]],s=new To({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Vv({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{V(9)}}function Kv(){return[{provide:zl,useFactory:()=>{let t=!0;return t&&ke("NgEventReplay"),t}}]}function fS(e,t,n){let r=new Map,o=t[Cn],i=e.cleanup;if(!i||!o)return r;for(let s=0;s<i.length;){let a=i[s++],c=i[s++];if(typeof a!="string")continue;let u=a;if(!Nv(u))continue;Sv(u)?n.capture.add(u):n.regular.add(u);let l=z(t[c]);s++;let d=i[s++];(typeof d=="boolean"||d>=0)&&(r.has(l)?r.get(l).push(u):r.set(l,[u]))}return r}var tf=class{views=[];indexByContent=new Map;add(t){let n=JSON.stringify(t);if(!this.indexByContent.has(n)){let r=this.views.length;return this.views.push(t),this.indexByContent.set(n,r),r}return this.indexByContent.get(n)}getAll(){return this.views}},pS=0;function Qv(e){return e.ssrId||(e.ssrId=`t${pS++}`),e.ssrId}function Jv(e,t,n){let r=[];return hr(e,t,n,r),r.length}function hS(e){let t=[];return ra(e,t),t.length}function Xv(e,t,n){let r=e[J];return r&&!r.hasAttribute(Dr)?va(r,e,null,t):null}function eE(e,t,n){let r=os(e[J]),o=Xv(r,t);if(o===null)return;let i=z(r[J]),s=e[te],a=va(i,s,null,t),c=r[k],u=`${o}|${a}`;c.setAttribute(i,lr,u)}function XU(e,t){let n=e.injector,r=Gm(n),o=Bg(n),i=new tf,s=new Map,a=e._views,c=n.get(zl,Ag),u={regular:new Set,capture:new Set},l=new Map,d=e.injector.get(At);for(let g of a){let D=jg(g);if(D!==null){let y={serializedViewCollection:i,corruptedTextNodes:s,isI18nHydrationEnabled:r,isIncrementalHydrationEnabled:o,i18nChildren:new Map,eventTypesToReplay:u,shouldReplayEvents:c,appId:d,deferBlocks:l};me(D)?eE(D,y):Xv(D,y),ES(s,t)}}let p=i.getAll(),f=n.get(Cr);if(f.set(Wl,p),l.size>0){let g={};for(let[D,y]of l.entries())g[D]=y;f.set(Og,g)}return u}function gS(e,t,n,r,o){let i=[],s="";for(let a=se;a<e.length;a++){let c=e[a],u,l,d;if(zt(c)&&(c=c[x],me(c))){l=hS(c)+1,eE(c,o);let f=os(c[J]);d={[qs]:f[I].ssrId,[Yt]:l}}if(!d){let f=c[I];f.type===1?(u=f.ssrId,l=1):(u=Qv(f),l=Jv(f,c,f.firstChild)),d={[qs]:u,[Yt]:l};let g=!1;if(Uy(n[I],t)){let D=Vy(n,t),y=Rd(n[I],t);if(o.isIncrementalHydrationEnabled&&y.hydrateTriggers!==null){let m=`d${o.deferBlocks.size}`;y.hydrateTriggers.has(7)&&(g=!0);let L=[];ra(e,L);let Ee={[Yt]:L.length,[Hl]:D[Cd]},j=mS(y.hydrateTriggers);j.length>0&&(Ee[Mg]=j),r!==null&&(Ee[Tg]=r),o.deferBlocks.set(m,Ee);let Le=z(e);Le!==void 0?Le.nodeType===Node.COMMENT_NODE&&kv(Le,m):kv(Le,m),g||_S(y,L,m,o),r=m,d[Ul]=m}d[Hl]=D[Cd]}g||Object.assign(d,tE(e[a],r,o))}let p=JSON.stringify(d);if(i.length>0&&p===s){let f=i[i.length-1];f[Ro]??=1,f[Ro]++}else s=p,i.push(d)}return i}function mS(e){let t=new Set([0,1,2,5]),n=[];for(let[r,o]of e)t.has(r)&&(o===null?n.push(r):n.push({trigger:r,delay:o.delay}));return n}function Ho(e,t,n,r){let o=t.index-x;e[Zs]??={},e[Zs][o]??=Um(t,n,r)}function Xd(e,t){let n=typeof t=="number"?t:t.index-x;e[br]??=[],e[br].includes(n)||e[br].push(n)}function tE(e,t=null,n){let r={},o=e[I],i=Wm(o,n),s=n.shouldReplayEvents?fS(o,e,n.eventTypesToReplay):null;for(let a=x;a<o.bindingStartIndex;a++){let c=o.data[a],u=a-x,l=qm(e,a,n);if(l){r[Vl]??={},r[Vl][u]=l.caseQueue;for(let d of l.disconnectedNodes)Xd(r,d);for(let d of l.disjointNodes){let p=o.data[d+x];Ho(r,p,e,i)}continue}if(xl(c)&&!Tr(c)){if(me(e[a])&&c.tView&&(r[Ws]??={},r[Ws][u]=Qv(c.tView)),Sr(c,e)&&DS(c)){Xd(r,c);continue}if(Array.isArray(c.projection)){for(let d of c.projection)if(d)if(!Array.isArray(d))!Xc(d)&&!_r(d)&&(Sr(d,e)?Xd(r,d):Ho(r,d,e,i));else throw jm(z(e[a]))}if(yS(r,c,e,i),me(e[a])){let d=e[a][J];if(Array.isArray(d)){let p=z(d);p.hasAttribute(Dr)||va(p,d,t,n)}r[wr]??={},r[wr][u]=gS(e[a],c,e,t,n)}else if(Array.isArray(e[a])&&!ag(c)){let d=z(e[a][J]);d.hasAttribute(Dr)||va(d,e[a],t,n)}else if(c.type&8)r[Gs]??={},r[Gs][u]=Jv(o,e,c.child);else if(c.type&144){let d=c.next;for(;d!==null&&d.type&144;)d=d.next;d&&!_r(d)&&Ho(r,d,e,i)}else if(c.type&1){let d=z(e[a]);Zl(n,d)}if(s&&c.type&2){let d=z(e[a]);s.has(d)&&Gl(d,s.get(d),t)}}}return r}function yS(e,t,n,r){Xc(t)||(t.projectionNext&&t.projectionNext!==t.next&&!_r(t.projectionNext)&&Ho(e,t.projectionNext,n,r),t.prev===null&&t.parent!==null&&Sr(t.parent,n)&&!Sr(t,n)&&Ho(e,t,n,r))}function vS(e){let t=e[K];return t?.constructor?ot(t.constructor)?.encapsulation===$e.ShadowDom:!1}function va(e,t,n,r){let o=t[k];if(zp(t)&&!zm()||vS(t))return o.setAttribute(e,Dr,""),null;{let i=tE(t,n,r),s=r.serializedViewCollection.add(i);return o.setAttribute(e,lr,s.toString()),s}}function kv(e,t){e.textContent=`ngh=${t}`}function ES(e,t){for(let[n,r]of e)n.after(t.createComment(r))}function DS(e){let t=e;for(;t!=null;){if(lt(t))return!0;t=t.parent}return!1}function _S(e,t,n,r){let o=Hg(e.hydrateTriggers);for(let i of o)r.eventTypesToReplay.regular.add(i);if(o.length>0){let i=t.filter(s=>s.nodeType===Node.ELEMENT_NODE);for(let s of i)Gl(s,o,n)}}var Pv=!1;function IS(){Pv||(Pv=!0,Pg(),Ky(),fv(),Qy(),ky(),fy(),Km(),Dm())}function nE(){return it([{provide:$l,useFactory:()=>{let t=!0;return t&&ke("NgHydration"),t}},{provide:rt,useValue:()=>{$m(!1)},multi:!0}])}function Ar(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function CS(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var wS="\u{1F170}\uFE0F",rE=!1;function eH(e){if(!rE)return;let{startLabel:t}=oE(e);performance.mark(t)}function tH(e){if(!rE)return;let{startLabel:t,labelName:n,endLabel:r}=oE(e);performance.mark(r),performance.measure(n,t,r),performance.clearMarks(t),performance.clearMarks(r)}function oE(e){let t=`${wS}:${e}`;return{labelName:t,startLabel:`start:${t}`,endLabel:`end:${t}`}}var ef=Symbol("NOT_SET"),iE=new Set,bS=Q(P({},Xr),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,value:ef,cleanup:null,consumerMarkedDirty(){if(this.sequence.impl.executing){if(this.sequence.lastPhase===null||this.sequence.lastPhase<this.phase)return;this.sequence.erroredOrDestroyed=!0}this.sequence.scheduler.notify(7)},phaseFn(e){if(this.sequence.lastPhase=this.phase,!this.dirty)return this.signal;if(this.dirty=!1,this.value!==ef&&!yn(this))return this.signal;try{for(let o of this.cleanup??iE)o()}finally{this.cleanup?.clear()}let t=[];e!==void 0&&t.push(e),t.push(this.registerCleanupFn);let n=It(this),r;try{r=this.userFn.apply(null,t)}finally{Bt(this,n)}return(this.value===ef||!this.equal(this.value,r))&&(this.value=r,this.version++),this.signal}}),nf=class extends So{scheduler;lastPhase=null;nodes=[void 0,void 0,void 0,void 0];constructor(t,n,r,o,i,s=null){super(t,[void 0,void 0,void 0,void 0],r,!1,i,s),this.scheduler=o;for(let a of Td){let c=n[a];if(c===void 0)continue;let u=Object.create(bS);u.sequence=this,u.phase=a,u.userFn=c,u.dirty=!0,u.signal=()=>(mn(u),u.value),u.signal[ie]=u,u.registerCleanupFn=l=>(u.cleanup??=new Set).add(l),this.nodes[a]=u,this.hooks[a]=l=>u.phaseFn(l)}}afterRun(){super.afterRun(),this.lastPhase=null}destroy(){super.destroy();for(let t of this.nodes)for(let n of t?.cleanup??iE)n()}};function nH(e,t){return Nd}function sE(e,t){let n=ot(e),r=t.elementInjector||tr();return new jn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}function rH(...e){return e.reduce((t,n)=>Object.assign(t,n,{providers:[...t.providers,...n.providers]}),{providers:[]})}var oH=new v("",{providedIn:"platform",factory:()=>null}),iH=new v("",{providedIn:"platform",factory:()=>null}),sH=new v("",{providedIn:"platform",factory:()=>null});var uE=null;function xt(){return uE}function rf(e){uE??=e}var $o=class{},of=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>h(lE),providedIn:"platform"})}return e})();var lE=(()=>{class e extends of{_location;_history;_doc=h(O);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return xt().getBaseHref(this._doc)}onPopState(n){let r=xt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=xt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function dE(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function aE(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function en(e){return e&&e[0]!=="?"?`?${e}`:e}var Ea=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>h(pE),providedIn:"root"})}return e})(),fE=new v(""),pE=(()=>{class e extends Ea{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??h(O).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return dE(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+en(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+en(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+en(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(of),C(fE,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),hE=(()=>{class e{_subject=new H;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=SS(aE(cE(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+en(r))}normalize(n){return e.stripTrailingSlash(MS(this._basePath,cE(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+en(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+en(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=en;static joinWithSlash=dE;static stripTrailingSlash=aE;static \u0275fac=function(r){return new(r||e)(C(Ea))};static \u0275prov=E({token:e,factory:()=>TS(),providedIn:"root"})}return e})();function TS(){return new hE(C(Ea))}function MS(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function cE(e){return e.replace(/\/index.html$/,"")}function SS(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var ve=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(ve||{}),U=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(U||{}),Se=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Se||{}),Ft={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function yE(e){return Pe(e)[X.LocaleId]}function vE(e,t,n){let r=Pe(e),o=[r[X.DayPeriodsFormat],r[X.DayPeriodsStandalone]],i=Ge(o,t);return Ge(i,n)}function EE(e,t,n){let r=Pe(e),o=[r[X.DaysFormat],r[X.DaysStandalone]],i=Ge(o,t);return Ge(i,n)}function DE(e,t,n){let r=Pe(e),o=[r[X.MonthsFormat],r[X.MonthsStandalone]],i=Ge(o,t);return Ge(i,n)}function _E(e,t){let r=Pe(e)[X.Eras];return Ge(r,t)}function zo(e,t){let n=Pe(e);return Ge(n[X.DateFormat],t)}function Go(e,t){let n=Pe(e);return Ge(n[X.TimeFormat],t)}function Wo(e,t){let r=Pe(e)[X.DateTimeFormat];return Ge(r,t)}function qo(e,t){let n=Pe(e),r=n[X.NumberSymbols][t];if(typeof r>"u"){if(t===Ft.CurrencyDecimal)return n[X.NumberSymbols][Ft.Decimal];if(t===Ft.CurrencyGroup)return n[X.NumberSymbols][Ft.Group]}return r}function IE(e){if(!e[X.ExtraData])throw new _(2303,!1)}function CE(e){let t=Pe(e);return IE(t),(t[X.ExtraData][2]||[]).map(r=>typeof r=="string"?sf(r):[sf(r[0]),sf(r[1])])}function wE(e,t,n){let r=Pe(e);IE(r);let o=[r[X.ExtraData][0],r[X.ExtraData][1]],i=Ge(o,t)||[];return Ge(i,n)||[]}function Ge(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new _(2304,!1)}function sf(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var NS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Da={},AS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function bE(e,t,n,r){let o=BS(e);t=Ot(n,t)||t;let s=[],a;for(;t;)if(a=AS.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=ME(r,c),o=jS(o,r));let u="";return s.forEach(l=>{let d=PS(l);u+=d?d(o,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function ba(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Ot(e,t){let n=yE(e);if(Da[n]??={},Da[n][t])return Da[n][t];let r="";switch(t){case"shortDate":r=zo(e,Se.Short);break;case"mediumDate":r=zo(e,Se.Medium);break;case"longDate":r=zo(e,Se.Long);break;case"fullDate":r=zo(e,Se.Full);break;case"shortTime":r=Go(e,Se.Short);break;case"mediumTime":r=Go(e,Se.Medium);break;case"longTime":r=Go(e,Se.Long);break;case"fullTime":r=Go(e,Se.Full);break;case"short":let o=Ot(e,"shortTime"),i=Ot(e,"shortDate");r=_a(Wo(e,Se.Short),[o,i]);break;case"medium":let s=Ot(e,"mediumTime"),a=Ot(e,"mediumDate");r=_a(Wo(e,Se.Medium),[s,a]);break;case"long":let c=Ot(e,"longTime"),u=Ot(e,"longDate");r=_a(Wo(e,Se.Long),[c,u]);break;case"full":let l=Ot(e,"fullTime"),d=Ot(e,"fullDate");r=_a(Wo(e,Se.Full),[l,d]);break}return r&&(Da[n][t]=r),r}function _a(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Je(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function RS(e,t){return Je(e,3).substring(0,t)}function re(e,t,n=0,r=!1,o=!1){return function(i,s){let a=xS(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return RS(a,t);let c=qo(s,Ft.MinusSign);return Je(a,t,c,r,o)}}function xS(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new _(2301,!1)}}function G(e,t,n=ve.Format,r=!1){return function(o,i){return OS(o,i,e,t,n,r)}}function OS(e,t,n,r,o,i){switch(n){case 2:return DE(t,o,r)[e.getMonth()];case 1:return EE(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=CE(t),l=wE(t,o,r),d=u.findIndex(p=>{if(Array.isArray(p)){let[f,g]=p,D=s>=f.hours&&a>=f.minutes,y=s<g.hours||s===g.hours&&a<g.minutes;if(f.hours<g.hours){if(D&&y)return!0}else if(D||y)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return vE(t,o,r)[s<12?0:1];case 3:return _E(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new _(2302,!1)}}function Ia(e){return function(t,n,r){let o=-1*r,i=qo(n,Ft.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+Je(s,2,i)+Je(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+Je(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+Je(s,2,i)+":"+Je(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+Je(s,2,i)+":"+Je(Math.abs(o%60),2,i);default:throw new _(2302,!1)}}}var FS=0,wa=4;function kS(e){let t=ba(e,FS,1).getDay();return ba(e,0,1+(t<=wa?wa:wa+7)-t)}function TE(e){let t=e.getDay(),n=t===0?-3:wa-t;return ba(e.getFullYear(),e.getMonth(),e.getDate()+n)}function af(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=TE(n),s=kS(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Je(o,e,qo(r,Ft.MinusSign))}}function Ca(e,t=!1){return function(n,r){let i=TE(n).getFullYear();return Je(i,e,qo(r,Ft.MinusSign),t)}}var cf={};function PS(e){if(cf[e])return cf[e];let t;switch(e){case"G":case"GG":case"GGG":t=G(3,U.Abbreviated);break;case"GGGG":t=G(3,U.Wide);break;case"GGGGG":t=G(3,U.Narrow);break;case"y":t=re(0,1,0,!1,!0);break;case"yy":t=re(0,2,0,!0,!0);break;case"yyy":t=re(0,3,0,!1,!0);break;case"yyyy":t=re(0,4,0,!1,!0);break;case"Y":t=Ca(1);break;case"YY":t=Ca(2,!0);break;case"YYY":t=Ca(3);break;case"YYYY":t=Ca(4);break;case"M":case"L":t=re(1,1,1);break;case"MM":case"LL":t=re(1,2,1);break;case"MMM":t=G(2,U.Abbreviated);break;case"MMMM":t=G(2,U.Wide);break;case"MMMMM":t=G(2,U.Narrow);break;case"LLL":t=G(2,U.Abbreviated,ve.Standalone);break;case"LLLL":t=G(2,U.Wide,ve.Standalone);break;case"LLLLL":t=G(2,U.Narrow,ve.Standalone);break;case"w":t=af(1);break;case"ww":t=af(2);break;case"W":t=af(1,!0);break;case"d":t=re(2,1);break;case"dd":t=re(2,2);break;case"c":case"cc":t=re(7,1);break;case"ccc":t=G(1,U.Abbreviated,ve.Standalone);break;case"cccc":t=G(1,U.Wide,ve.Standalone);break;case"ccccc":t=G(1,U.Narrow,ve.Standalone);break;case"cccccc":t=G(1,U.Short,ve.Standalone);break;case"E":case"EE":case"EEE":t=G(1,U.Abbreviated);break;case"EEEE":t=G(1,U.Wide);break;case"EEEEE":t=G(1,U.Narrow);break;case"EEEEEE":t=G(1,U.Short);break;case"a":case"aa":case"aaa":t=G(0,U.Abbreviated);break;case"aaaa":t=G(0,U.Wide);break;case"aaaaa":t=G(0,U.Narrow);break;case"b":case"bb":case"bbb":t=G(0,U.Abbreviated,ve.Standalone,!0);break;case"bbbb":t=G(0,U.Wide,ve.Standalone,!0);break;case"bbbbb":t=G(0,U.Narrow,ve.Standalone,!0);break;case"B":case"BB":case"BBB":t=G(0,U.Abbreviated,ve.Format,!0);break;case"BBBB":t=G(0,U.Wide,ve.Format,!0);break;case"BBBBB":t=G(0,U.Narrow,ve.Format,!0);break;case"h":t=re(3,1,-12);break;case"hh":t=re(3,2,-12);break;case"H":t=re(3,1);break;case"HH":t=re(3,2);break;case"m":t=re(4,1);break;case"mm":t=re(4,2);break;case"s":t=re(5,1);break;case"ss":t=re(5,2);break;case"S":t=re(6,1);break;case"SS":t=re(6,2);break;case"SSS":t=re(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Ia(0);break;case"ZZZZZ":t=Ia(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Ia(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Ia(2);break;default:return null}return cf[e]=t,t}function ME(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function LS(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function jS(e,t,n){let o=e.getTimezoneOffset(),i=ME(t,o);return LS(e,-1*(i-o))}function BS(e){if(gE(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return ba(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(NS))return VS(r)}let t=new Date(e);if(!gE(t))throw new _(2302,!1);return t}function VS(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,u),t}function gE(e){return e instanceof Date&&!isNaN(e.valueOf())}var uf=/\s+/,mE=[],US=(()=>{class e{_ngEl;_renderer;initialClasses=mE;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(uf):mE}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(uf):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(uf).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(ze(he),ze(ca))};static \u0275dir=yt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var HS=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(ze(Nr))};static \u0275dir=yt({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[$s]})}return e})();function $S(e,t){return new _(2100,!1)}var zS="mediumDate",SE=new v(""),NE=new v(""),GS=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??zS,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return bE(n,s,i||this.locale,a)}catch(s){throw $S(e,s.message)}}static \u0275fac=function(r){return new(r||e)(ze(Vo,16),ze(SE,24),ze(NE,24))};static \u0275pipe=Id({name:"date",type:e,pure:!0})}return e})();var AE=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({})}return e})();function Zo(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Un=class{};var df="browser",M2="server";function RE(e){return e===df}var S2=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new lf})}return e})();var lf=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}};var xr=class{},Or=class{},Xe=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Sa=class{encodeKey(t){return xE(t)}encodeValue(t){return xE(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function ZS(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var YS=/%(\d[a-f0-9])/gi,KS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function xE(e){return encodeURIComponent(e).replace(YS,(t,n)=>KS[n]??t)}function Ta(e){return`${e}`}var kt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Sa,t.fromString){if(t.fromObject)throw new _(2805,!1);this.map=ZS(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Ta):[Ta(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Ta(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Ta(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Na=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function QS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function OE(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function FE(e){return typeof Blob<"u"&&e instanceof Blob}function kE(e){return typeof FormData<"u"&&e instanceof FormData}function JS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Yo="Content-Type",Aa="Accept",mf="X-Request-URL",LE="text/plain",jE="application/json",BE=`${jE}, ${LE}, */*`,Rr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;credentials;keepalive=!1;cache;priority;mode;redirect;responseType="json";method;params;urlWithParams;transferCache;timeout;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(QS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i){if(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),i.priority&&(this.priority=i.priority),i.cache&&(this.cache=i.cache),i.credentials&&(this.credentials=i.credentials),typeof i.timeout=="number"){if(i.timeout<1||!Number.isInteger(i.timeout))throw new Error("");this.timeout=i.timeout}i.mode&&(this.mode=i.mode),i.redirect&&(this.redirect=i.redirect),this.transferCache=i.transferCache}if(this.headers??=new Xe,this.context??=new Na,!this.params)this.params=new kt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||OE(this.body)||FE(this.body)||kE(this.body)||JS(this.body)?this.body:this.body instanceof kt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||kE(this.body)?null:FE(this.body)?this.body.type||null:OE(this.body)?null:typeof this.body=="string"?LE:this.body instanceof kt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?jE:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.keepalive??this.keepalive,s=t.priority||this.priority,a=t.cache||this.cache,c=t.mode||this.mode,u=t.redirect||this.redirect,l=t.credentials||this.credentials,d=t.transferCache??this.transferCache,p=t.timeout??this.timeout,f=t.body!==void 0?t.body:this.body,g=t.withCredentials??this.withCredentials,D=t.reportProgress??this.reportProgress,y=t.headers||this.headers,m=t.params||this.params,L=t.context??this.context;return t.setHeaders!==void 0&&(y=Object.keys(t.setHeaders).reduce((Ee,j)=>Ee.set(j,t.setHeaders[j]),y)),t.setParams&&(m=Object.keys(t.setParams).reduce((Ee,j)=>Ee.set(j,t.setParams[j]),m)),new e(n,r,f,{params:m,headers:y,context:L,reportProgress:D,responseType:o,withCredentials:g,transferCache:d,keepalive:i,cache:a,priority:s,timeout:p,mode:c,redirect:u,credentials:l})}},Pt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Pt||{}),Fr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Xe,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Ko=class e extends Fr{constructor(t={}){super(t)}type=Pt.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},tn=class e extends Fr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Pt.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},vt=class extends Fr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},VE=200,XS=204;function ff(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive,priority:e.priority,cache:e.cache,mode:e.mode,redirect:e.redirect}}var xa=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Rr)i=n;else{let c;o.headers instanceof Xe?c=o.headers:c=new Xe(o.headers);let u;o.params&&(o.params instanceof kt?u=o.params:u=new kt({fromObject:o.params})),i=new Rr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive,priority:o.priority,cache:o.cache,mode:o.mode,redirect:o.redirect,credentials:o.credentials})}let s=be(i).pipe(sc(c=>this.handler.handle(c)));if(n instanceof Rr||o.observe==="events")return s;let a=s.pipe(Ne(c=>c instanceof tn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(W(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new _(2806,!1);return c.body}));case"blob":return a.pipe(W(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new _(2807,!1);return c.body}));case"text":return a.pipe(W(c=>{if(c.body!==null&&typeof c.body!="string")throw new _(2808,!1);return c.body}));case"json":default:return a.pipe(W(c=>c.body))}case"response":return a;default:throw new _(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new kt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,ff(o,r))}post(n,r,o={}){return this.request("POST",n,ff(o,r))}put(n,r,o={}){return this.request("PUT",n,ff(o,r))}static \u0275fac=function(r){return new(r||e)(C(xr))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),eN=/^\)\]\}',?\n/;function PE(e){if(e.url)return e.url;let t=mf.toLocaleLowerCase();return e.headers.get(t)}var UE=new v(""),Ma=(()=>{class e{fetchImpl=h(pf,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=h(F);destroyRef=h(He);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new R(r=>{let o=new AbortController;this.doRequest(n,o.signal,r).then(hf,s=>r.error(new vt({error:s})));let i;return n.timeout&&(i=this.ngZone.runOutsideAngular(()=>setTimeout(()=>{o.signal.aborted||o.abort(new DOMException("signal timed out","TimeoutError"))},n.timeout))),()=>{i!==void 0&&clearTimeout(i),o.abort()}})}doRequest(n,r,o){return $n(this,null,function*(){let i=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,P({signal:r},i)));tN(f),o.next({type:Pt.Sent}),s=yield f}catch(f){o.error(new vt({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new Xe(s.headers),c=s.statusText,u=PE(s)??n.urlWithParams,l=s.status,d=null;if(n.reportProgress&&o.next(new Ko({headers:a,status:l,statusText:c,url:u})),s.body){let f=s.headers.get("content-length"),g=[],D=s.body.getReader(),y=0,m,L,Ee=typeof Zone<"u"&&Zone.current,j=!1;if(yield this.ngZone.runOutsideAngular(()=>$n(this,null,function*(){for(;;){if(this.destroyed){yield D.cancel(),j=!0;break}let{done:rn,value:Br}=yield D.read();if(rn)break;if(g.push(Br),y+=Br.length,n.reportProgress){L=n.responseType==="text"?(L??"")+(m??=new TextDecoder).decode(Br,{stream:!0}):void 0;let $f=()=>o.next({type:Pt.DownloadProgress,total:f?+f:void 0,loaded:y,partialText:L});Ee?Ee.run($f):$f()}}})),j){o.complete();return}let Le=this.concatChunks(g,y);try{let rn=s.headers.get(Yo)??"";d=this.parseBody(n,Le,rn)}catch(rn){o.error(new vt({error:rn,headers:new Xe(s.headers),status:s.status,statusText:s.statusText,url:PE(s)??n.urlWithParams}));return}}l===0&&(l=d?VE:0),l>=200&&l<300?(o.next(new tn({body:d,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new vt({error:d,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(eN,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o;if(o=n.credentials,n.withCredentials&&(o="include"),n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(Aa)||(r[Aa]=BE),!n.headers.has(Yo)){let i=n.detectContentTypeHeader();i!==null&&(r[Yo]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o,keepalive:n.keepalive,cache:n.cache,priority:n.priority,mode:n.mode,redirect:n.redirect}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),pf=class{};function hf(){}function tN(e){e.then(hf,hf)}function nN(e,t){return t(e)}function rN(e,t,n){return(r,o)=>nr(n,()=>t(r,i=>e(i,o)))}var HE=new v(""),yf=new v(""),$E=new v("",{providedIn:"root",factory:()=>!0});var Ra=(()=>{class e extends xr{backend;injector;chain=null;pendingTasks=h(yo);contributeToStability=h($E);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(HE),...this.injector.get(yf,[])]));this.chain=r.reduceRight((o,i)=>rN(o,i,this.injector),nN)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Wr(r))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(C(Or),C(ge))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var oN=/^\)\]\}',?\n/,iN=RegExp(`^${mf}:`,"m");function sN(e){return"responseURL"in e&&e.responseURL?e.responseURL:iN.test(e.getAllResponseHeaders())?e.getResponseHeader(mf):null}var gf=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new _(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?je(r.\u0275loadImpl()):be(null)).pipe(dc(()=>new R(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((y,m)=>s.setRequestHeader(y,m.join(","))),n.headers.has(Aa)||s.setRequestHeader(Aa,BE),!n.headers.has(Yo)){let y=n.detectContentTypeHeader();y!==null&&s.setRequestHeader(Yo,y)}if(n.timeout&&(s.timeout=n.timeout),n.responseType){let y=n.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let y=s.statusText||"OK",m=new Xe(s.getAllResponseHeaders()),L=sN(s)||n.url;return c=new Ko({headers:m,status:s.status,statusText:y,url:L}),c},l=()=>{let{headers:y,status:m,statusText:L,url:Ee}=u(),j=null;m!==XS&&(j=typeof s.response>"u"?s.responseText:s.response),m===0&&(m=j?VE:0);let Le=m>=200&&m<300;if(n.responseType==="json"&&typeof j=="string"){let rn=j;j=j.replace(oN,"");try{j=j!==""?JSON.parse(j):null}catch(Br){j=rn,Le&&(Le=!1,j={error:Br,text:j})}}Le?(i.next(new tn({body:j,headers:y,status:m,statusText:L,url:Ee||void 0})),i.complete()):i.error(new vt({error:j,headers:y,status:m,statusText:L,url:Ee||void 0}))},d=y=>{let{url:m}=u(),L=new vt({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:m||void 0});i.error(L)},p=d;n.timeout&&(p=y=>{let{url:m}=u(),L=new vt({error:new DOMException("Request timed out","TimeoutError"),status:s.status||0,statusText:s.statusText||"Request timeout",url:m||void 0});i.error(L)});let f=!1,g=y=>{f||(i.next(u()),f=!0);let m={type:Pt.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(m.total=y.total),n.responseType==="text"&&s.responseText&&(m.partialText=s.responseText),i.next(m)},D=y=>{let m={type:Pt.UploadProgress,loaded:y.loaded};y.lengthComputable&&(m.total=y.total),i.next(m)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",p),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",g),a!==null&&s.upload&&s.upload.addEventListener("progress",D)),s.send(a),i.next({type:Pt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",p),n.reportProgress&&(s.removeEventListener("progress",g),a!==null&&s.upload&&s.upload.removeEventListener("progress",D)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(C(Un))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),zE=new v(""),aN="XSRF-TOKEN",cN=new v("",{providedIn:"root",factory:()=>aN}),uN="X-XSRF-TOKEN",lN=new v("",{providedIn:"root",factory:()=>uN}),Qo=class{},dN=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){return null}static \u0275fac=function(r){return new(r||e)(C(O),C(cN))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function fN(e,t){let n=e.url.toLowerCase();if(!h(zE)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=h(Qo).getToken(),o=h(lN);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var vf=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(vf||{});function pN(e,t){return{\u0275kind:e,\u0275providers:t}}function hN(...e){let t=[xa,gf,Ra,{provide:xr,useExisting:Ra},{provide:Or,useFactory:()=>h(UE,{optional:!0})??h(gf)},{provide:HE,useValue:fN,multi:!0},{provide:zE,useValue:!0},{provide:Qo,useClass:dN}];for(let n of e)t.push(...n.\u0275providers);return it(t)}function gN(){return pN(vf.Fetch,[Ma,{provide:UE,useExisting:Ma},{provide:Or,useExisting:Ma}])}var mN=new v(""),GE="b",WE="h",qE="s",ZE="st",YE="u",KE="rt",Ef=new v(""),yN=["GET","HEAD"];function vN(e,t){let f=h(Ef),{isCacheActive:n}=f,r=zf(f,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||o===!1||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!yN.includes(i)||!r.includeRequestsWithAuthHeaders&&EN(e)||r.filter?.(e)===!1)return t(e);let s=h(Cr),a=h(mN,{optional:!0}),c=a?CN(e.url,a):e.url,u=_N(e,c),l=s.get(u,null),d=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(d=o.includeHeaders),l){let{[GE]:g,[KE]:D,[WE]:y,[qE]:m,[ZE]:L,[YE]:Ee}=l,j=g;switch(D){case"arraybuffer":j=new TextEncoder().encode(g).buffer;break;case"blob":j=new Blob([g]);break}let Le=new Xe(y);return be(new tn({body:j,headers:Le,status:m,statusText:L,url:Ee}))}let p=t(e);return p.pipe(_t(g=>{g instanceof tn&&s.set(u,{[GE]:g.body,[WE]:DN(g.headers,d),[qE]:g.status,[ZE]:g.statusText,[YE]:c,[KE]:e.responseType})}))}function EN(e){return e.headers.has("authorization")||e.headers.has("proxy-authorization")}function DN(e,t){if(!t)return{};let n={};for(let r of t){let o=e.getAll(r);o!==null&&(n[r]=o)}return n}function QE(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function _N(e,t){let{params:n,method:r,responseType:o}=e,i=QE(n),s=e.serializeBody();s instanceof URLSearchParams?s=QE(s):typeof s!="string"&&(s="");let a=[r,o,t,s,i].join("|"),c=IN(a);return c}function IN(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=2147483648,t.toString()}function JE(e){return[{provide:Ef,useFactory:()=>(ke("NgHttpTransferCache"),P({isCacheActive:!0},e))},{provide:yf,useValue:vN,multi:!0},{provide:fa,multi:!0,useFactory:()=>{let t=h(Qe),n=h(Ef);return()=>{t.whenStable().then(()=>{n.isCacheActive=!1})}}}]}function CN(e,t){let n=new URL(e,"resolve://").origin,r=t[n];return r?e.replace(n,r):e}var ei=new v(""),Cf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new _(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(ei),C(F))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),kr=class{_doc;constructor(t){this._doc=t}manager},Oa="ng-app-id";function XE(e){for(let t of e)t.remove()}function eD(e,t){let n=t.createElement("style");return n.textContent=e,n}function bN(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Oa}="${t}"],link[${Oa}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Oa),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function _f(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var wf=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,bN(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,eD);r?.forEach(o=>this.addUsage(o,this.external,_f))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(XE(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])XE(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,eD(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,_f(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),r.setAttribute(Oa,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(O),C(At),C(Ir,8),C(Qt))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Df={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},bf=/%COMP%/g;var nD="%COMP%",TN=`_nghost-${nD}`,MN=`_ngcontent-${nD}`,SN=!0,NN=new v("",{providedIn:"root",factory:()=>SN});function AN(e){return MN.replace(bf,e)}function RN(e){return TN.replace(bf,e)}function rD(e,t){return t.map(n=>n.replace(bf,e))}var Tf=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!0,this.defaultRenderer=new Jo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;r.encapsulation===$e.ShadowDom&&(r=Q(P({},r),{encapsulation:$e.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Fa?o.applyToHost(n):o instanceof Xo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case $e.Emulated:i=new Fa(c,u,r,this.appId,l,s,a,d,p);break;case $e.ShadowDom:return new If(c,u,n,r,s,a,this.nonce,d,p);default:i=new Xo(c,u,r,l,s,a,d,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(Cf),C(wf),C(At),C(NN),C(O),C(Qt),C(F),C(Ir),C(Jt,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Jo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Df[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(tD(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(tD(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new _(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Df[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Df[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(gt.DashCase|gt.Important)?t.style.setProperty(n,r,o&gt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&gt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=xt().getGlobalEventTarget(this.doc,t),!t))throw new _(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;this.ngZone.runGuarded(()=>t(n))===!1&&n.preventDefault()}}};function tD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var If=class extends Jo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=rD(o.id,l);for(let p of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let p of d){let f=_f(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Xo=class extends Jo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?rD(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Fa=class extends Xo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=AN(l),this.hostAttr=RN(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var ka=class e extends $o{supportsDOMEvents=!0;static makeCurrent(){rf(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=xN();return n==null?null:ON(n)}resetBaseElement(){ti=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Zo(document.cookie,t)}},ti=null;function xN(){return ti=ti||document.head.querySelector("base"),ti?ti.getAttribute("href"):null}function ON(e){return new URL(e,document.baseURI).pathname}var FN=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),iD=(()=>{class e extends kr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),oD=["alt","control","meta","shift"],kN={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},PN={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},sD=(()=>{class e extends kr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>xt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),oD.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=kN[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),oD.forEach(s=>{if(s!==o){let a=PN[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function LN(e,t){return Yv(P({rootComponent:e},jN(t)))}function jN(e){return{appProviders:[...$N,...e?.providers??[]],platformProviders:HN}}function BN(){ka.makeCurrent()}function VN(){return new fe}function UN(){return Ll(document),document}var HN=[{provide:Qt,useValue:df},{provide:zs,useValue:BN,multi:!0},{provide:O,useFactory:UN}];var $N=[{provide:ao,useValue:"root"},{provide:fe,useFactory:VN},{provide:ei,useClass:iD,multi:!0,deps:[O]},{provide:ei,useClass:sD,multi:!0,deps:[O]},Tf,wf,Cf,{provide:Nt,useExisting:Tf},{provide:Un,useClass:FN},[]];var G$=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Mf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=C(zN),o},providedIn:"root"})}return e})(),zN=(()=>{class e extends Mf{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case ue.NONE:return r;case ue.HTML:return mt(r,"HTML")?Fe(r):Js(this._doc,String(r)).toString();case ue.STYLE:return mt(r,"Style")?Fe(r):r;case ue.SCRIPT:if(mt(r,"Script"))return Fe(r);throw new _(5200,!1);case ue.URL:return mt(r,"URL")?Fe(r):xo(String(r));case ue.RESOURCE_URL:if(mt(r,"ResourceURL"))return Fe(r);throw new _(5201,!1);default:throw new _(5202,!1)}}bypassSecurityTrustHtml(n){return Yl(n)}bypassSecurityTrustStyle(n){return Kl(n)}bypassSecurityTrustScript(n){return Ql(n)}bypassSecurityTrustUrl(n){return Jl(n)}bypassSecurityTrustResourceUrl(n){return Xl(n)}static \u0275fac=function(r){return new(r||e)(C(O))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Pa=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e[e.I18nSupport=2]="I18nSupport",e[e.EventReplay=3]="EventReplay",e[e.IncrementalHydration=4]="IncrementalHydration",e}(Pa||{});function GN(e,t=[],n={}){return{\u0275kind:e,\u0275providers:t}}function W$(){return GN(Pa.EventReplay,Kv())}function q$(...e){let t=[],n=new Set;for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);let r=n.has(Pa.HttpTransferCacheOptions);return it([[],nE(),n.has(Pa.NoHttpTransferCache)||r?[]:JE({}),t])}var La;function qN(){if(La===void 0&&(La=null,typeof window<"u")){let e=window;e.trustedTypes!==void 0&&(La=e.trustedTypes.createPolicy("angular#components",{createHTML:t=>t}))}return La}function ni(e){return qN()?.createHTML(e)||e}function aD(e){return Error(`Unable to find icon with the name "${e}"`)}function ZN(){return Error("Could not find HttpClient for use with Angular Material icons. Please add provideHttpClient() to your providers.")}function cD(e){return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL via Angular's DomSanitizer. Attempted URL was "${e}".`)}function uD(e){return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by Angular's DomSanitizer. Attempted literal was "${e}".`)}var Lt=class{url;svgText;options;svgElement;constructor(t,n,r){this.url=t,this.svgText=n,this.options=r}},dD=(()=>{class e{_httpClient;_sanitizer;_errorHandler;_document;_svgIconConfigs=new Map;_iconSetConfigs=new Map;_cachedIconsByUrl=new Map;_inProgressUrlFetches=new Map;_fontCssClassesByAlias=new Map;_resolvers=[];_defaultFontSetClass=["material-icons","mat-ligature-font"];constructor(n,r,o,i){this._httpClient=n,this._sanitizer=r,this._errorHandler=i,this._document=o}addSvgIcon(n,r,o){return this.addSvgIconInNamespace("",n,r,o)}addSvgIconLiteral(n,r,o){return this.addSvgIconLiteralInNamespace("",n,r,o)}addSvgIconInNamespace(n,r,o,i){return this._addSvgIconConfig(n,r,new Lt(o,null,i))}addSvgIconResolver(n){return this._resolvers.push(n),this}addSvgIconLiteralInNamespace(n,r,o,i){let s=this._sanitizer.sanitize(ue.HTML,o);if(!s)throw uD(o);let a=ni(s);return this._addSvgIconConfig(n,r,new Lt("",a,i))}addSvgIconSet(n,r){return this.addSvgIconSetInNamespace("",n,r)}addSvgIconSetLiteral(n,r){return this.addSvgIconSetLiteralInNamespace("",n,r)}addSvgIconSetInNamespace(n,r,o){return this._addSvgIconSetConfig(n,new Lt(r,null,o))}addSvgIconSetLiteralInNamespace(n,r,o){let i=this._sanitizer.sanitize(ue.HTML,r);if(!i)throw uD(r);let s=ni(i);return this._addSvgIconSetConfig(n,new Lt("",s,o))}registerFontClassAlias(n,r=n){return this._fontCssClassesByAlias.set(n,r),this}classNameForFontAlias(n){return this._fontCssClassesByAlias.get(n)||n}setDefaultFontSetClass(...n){return this._defaultFontSetClass=n,this}getDefaultFontSetClass(){return this._defaultFontSetClass}getSvgIconFromUrl(n){let r=this._sanitizer.sanitize(ue.RESOURCE_URL,n);if(!r)throw cD(n);let o=this._cachedIconsByUrl.get(r);return o?be(ja(o)):this._loadSvgIconFromConfig(new Lt(n,null)).pipe(_t(i=>this._cachedIconsByUrl.set(r,i)),W(i=>ja(i)))}getNamedSvgIcon(n,r=""){let o=lD(r,n),i=this._svgIconConfigs.get(o);if(i)return this._getSvgFromConfig(i);if(i=this._getIconConfigFromResolvers(r,n),i)return this._svgIconConfigs.set(o,i),this._getSvgFromConfig(i);let s=this._iconSetConfigs.get(r);return s?this._getSvgFromIconSetConfigs(n,s):rc(aD(o))}ngOnDestroy(){this._resolvers=[],this._svgIconConfigs.clear(),this._iconSetConfigs.clear(),this._cachedIconsByUrl.clear()}_getSvgFromConfig(n){return n.svgText?be(ja(this._svgElementFromConfig(n))):this._loadSvgIconFromConfig(n).pipe(W(r=>ja(r)))}_getSvgFromIconSetConfigs(n,r){let o=this._extractIconWithNameFromAnySet(n,r);if(o)return be(o);let i=r.filter(s=>!s.svgText).map(s=>this._loadSvgIconSetFromConfig(s).pipe(xi(a=>{let u=`Loading icon set URL: ${this._sanitizer.sanitize(ue.RESOURCE_URL,s.url)} failed: ${a.message}`;return this._errorHandler.handleError(new Error(u)),be(null)})));return ic(i).pipe(W(()=>{let s=this._extractIconWithNameFromAnySet(n,r);if(!s)throw aD(n);return s}))}_extractIconWithNameFromAnySet(n,r){for(let o=r.length-1;o>=0;o--){let i=r[o];if(i.svgText&&i.svgText.toString().indexOf(n)>-1){let s=this._svgElementFromConfig(i),a=this._extractSvgIconFromSet(s,n,i.options);if(a)return a}}return null}_loadSvgIconFromConfig(n){return this._fetchIcon(n).pipe(_t(r=>n.svgText=r),W(()=>this._svgElementFromConfig(n)))}_loadSvgIconSetFromConfig(n){return n.svgText?be(null):this._fetchIcon(n).pipe(_t(r=>n.svgText=r))}_extractSvgIconFromSet(n,r,o){let i=n.querySelector(`[id="${r}"]`);if(!i)return null;let s=i.cloneNode(!0);if(s.removeAttribute("id"),s.nodeName.toLowerCase()==="svg")return this._setSvgAttributes(s,o);if(s.nodeName.toLowerCase()==="symbol")return this._setSvgAttributes(this._toSvgElement(s),o);let a=this._svgElementFromString(ni("<svg></svg>"));return a.appendChild(s),this._setSvgAttributes(a,o)}_svgElementFromString(n){let r=this._document.createElement("DIV");r.innerHTML=n;let o=r.querySelector("svg");if(!o)throw Error("<svg> tag not found");return o}_toSvgElement(n){let r=this._svgElementFromString(ni("<svg></svg>")),o=n.attributes;for(let i=0;i<o.length;i++){let{name:s,value:a}=o[i];s!=="id"&&r.setAttribute(s,a)}for(let i=0;i<n.childNodes.length;i++)n.childNodes[i].nodeType===this._document.ELEMENT_NODE&&r.appendChild(n.childNodes[i].cloneNode(!0));return r}_setSvgAttributes(n,r){return n.setAttribute("fit",""),n.setAttribute("height","100%"),n.setAttribute("width","100%"),n.setAttribute("preserveAspectRatio","xMidYMid meet"),n.setAttribute("focusable","false"),r&&r.viewBox&&n.setAttribute("viewBox",r.viewBox),n}_fetchIcon(n){let{url:r,options:o}=n,i=o?.withCredentials??!1;if(!this._httpClient)throw ZN();if(r==null)throw Error(`Cannot fetch icon from URL "${r}".`);let s=this._sanitizer.sanitize(ue.RESOURCE_URL,r);if(!s)throw cD(r);let a=this._inProgressUrlFetches.get(s);if(a)return a;let c=this._httpClient.get(s,{responseType:"text",withCredentials:i}).pipe(W(u=>ni(u)),Wr(()=>this._inProgressUrlFetches.delete(s)),qr());return this._inProgressUrlFetches.set(s,c),c}_addSvgIconConfig(n,r,o){return this._svgIconConfigs.set(lD(n,r),o),this}_addSvgIconSetConfig(n,r){let o=this._iconSetConfigs.get(n);return o?o.push(r):this._iconSetConfigs.set(n,[r]),this}_svgElementFromConfig(n){if(!n.svgElement){let r=this._svgElementFromString(n.svgText);this._setSvgAttributes(r,n.options),n.svgElement=r}return n.svgElement}_getIconConfigFromResolvers(n,r){for(let o=0;o<this._resolvers.length;o++){let i=this._resolvers[o](r,n);if(i)return YN(i)?new Lt(i.url,null,i.options):new Lt(i,null)}}static \u0275fac=function(r){return new(r||e)(C(xa,8),C(Mf),C(O,8),C(fe))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ja(e){return e.cloneNode(!0)}function lD(e,t){return e+":"+t}function YN(e){return!!(e.url&&e.options)}function Sf(e){return e.buttons===0||e.detail===0}function Nf(e){let t=e.touches&&e.touches[0]||e.changedTouches&&e.changedTouches[0];return!!t&&t.identifier===-1&&(t.radiusX==null||t.radiusX===1)&&(t.radiusY==null||t.radiusY===1)}var Af;function KN(){if(Af==null){let e=typeof document<"u"?document.head:null;Af=!!(e&&(e.createShadowRoot||e.attachShadow))}return Af}function fD(e){if(KN()){let t=e.getRootNode?e.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}function n8(){let e=typeof document<"u"&&document?document.activeElement:null;for(;e&&e.shadowRoot;){let t=e.shadowRoot.activeElement;if(t===e)break;e=t}return e}function Pr(e){return e.composedPath?e.composedPath()[0]:e.target}var Rf;try{Rf=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Rf=!1}var Et=(()=>{class e{_platformId=h(Qt);isBrowser=this._platformId?RE(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||Rf)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ri;function QN(){if(ri==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>ri=!0}))}finally{ri=ri||!1}return ri}function pD(e){return QN()?e:!!e.capture}function hD(e,t=0){return JN(e)?Number(e):arguments.length===2?t:0}function JN(e){return!isNaN(parseFloat(e))&&!isNaN(Number(e))}function Lr(e){return e instanceof he?e.nativeElement:e}var gD=new v("cdk-input-modality-detector-options"),mD={ignoreKeys:[18,17,224,91,16]},yD=650,xf={passive:!0,capture:!0},vD=(()=>{class e{_platform=h(Et);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new un(null);_options;_lastTouchMs=0;_onKeydown=n=>{this._options?.ignoreKeys?.some(r=>r===n.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=Pr(n))};_onMousedown=n=>{Date.now()-this._lastTouchMs<yD||(this._modality.next(Sf(n)?"keyboard":"mouse"),this._mostRecentTarget=Pr(n))};_onTouchstart=n=>{if(Nf(n)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=Pr(n)};constructor(){let n=h(F),r=h(O),o=h(gD,{optional:!0});if(this._options=P(P({},mD),o),this.modalityDetected=this._modality.pipe(Zr(1)),this.modalityChanged=this.modalityDetected.pipe(ac()),this._platform.isBrowser){let i=h(Nt).createRenderer(null,null);this._listenerCleanups=n.runOutsideAngular(()=>[i.listen(r,"keydown",this._onKeydown,xf),i.listen(r,"mousedown",this._onMousedown,xf),i.listen(r,"touchstart",this._onTouchstart,xf)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(n=>n())}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),oi=function(e){return e[e.IMMEDIATE=0]="IMMEDIATE",e[e.EVENTUAL=1]="EVENTUAL",e}(oi||{}),ED=new v("cdk-focus-monitor-default-options"),Ba=pD({passive:!0,capture:!0}),DD=(()=>{class e{_ngZone=h(F);_platform=h(Et);_inputModalityDetector=h(vD);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=h(O);_stopInputModalityDetector=new H;constructor(){let n=h(ED,{optional:!0});this._detectionMode=n?.detectionMode||oi.IMMEDIATE}_rootNodeFocusAndBlurListener=n=>{let r=Pr(n);for(let o=r;o;o=o.parentElement)n.type==="focus"?this._onFocus(n,o):this._onBlur(n,o)};monitor(n,r=!1){let o=Lr(n);if(!this._platform.isBrowser||o.nodeType!==1)return be();let i=fD(o)||this._document,s=this._elementInfo.get(o);if(s)return r&&(s.checkChildren=!0),s.subject;let a={checkChildren:r,subject:new H,rootNode:i};return this._elementInfo.set(o,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(n){let r=Lr(n),o=this._elementInfo.get(r);o&&(o.subject.complete(),this._setClasses(r),this._elementInfo.delete(r),this._removeGlobalListeners(o))}focusVia(n,r,o){let i=Lr(n),s=this._document.activeElement;i===s?this._getClosestElementsInfo(i).forEach(([a,c])=>this._originChanged(a,r,c)):(this._setOrigin(r),typeof i.focus=="function"&&i.focus(o))}ngOnDestroy(){this._elementInfo.forEach((n,r)=>this.stopMonitoring(r))}_getWindow(){return this._document.defaultView||window}_getFocusOrigin(n){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(n)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:n&&this._isLastInteractionFromInputLabel(n)?"mouse":"program"}_shouldBeAttributedToTouch(n){return this._detectionMode===oi.EVENTUAL||!!n?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(n,r){n.classList.toggle("cdk-focused",!!r),n.classList.toggle("cdk-touch-focused",r==="touch"),n.classList.toggle("cdk-keyboard-focused",r==="keyboard"),n.classList.toggle("cdk-mouse-focused",r==="mouse"),n.classList.toggle("cdk-program-focused",r==="program")}_setOrigin(n,r=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=n,this._originFromTouchInteraction=n==="touch"&&r,this._detectionMode===oi.IMMEDIATE){clearTimeout(this._originTimeoutId);let o=this._originFromTouchInteraction?yD:1;this._originTimeoutId=setTimeout(()=>this._origin=null,o)}})}_onFocus(n,r){let o=this._elementInfo.get(r),i=Pr(n);!o||!o.checkChildren&&r!==i||this._originChanged(r,this._getFocusOrigin(i),o)}_onBlur(n,r){let o=this._elementInfo.get(r);!o||o.checkChildren&&n.relatedTarget instanceof Node&&r.contains(n.relatedTarget)||(this._setClasses(r),this._emitOrigin(o,null))}_emitOrigin(n,r){n.subject.observers.length&&this._ngZone.run(()=>n.subject.next(r))}_registerGlobalListeners(n){if(!this._platform.isBrowser)return;let r=n.rootNode,o=this._rootNodeFocusListenerCount.get(r)||0;o||this._ngZone.runOutsideAngular(()=>{r.addEventListener("focus",this._rootNodeFocusAndBlurListener,Ba),r.addEventListener("blur",this._rootNodeFocusAndBlurListener,Ba)}),this._rootNodeFocusListenerCount.set(r,o+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Yr(this._stopInputModalityDetector)).subscribe(i=>{this._setOrigin(i,!0)}))}_removeGlobalListeners(n){let r=n.rootNode;if(this._rootNodeFocusListenerCount.has(r)){let o=this._rootNodeFocusListenerCount.get(r);o>1?this._rootNodeFocusListenerCount.set(r,o-1):(r.removeEventListener("focus",this._rootNodeFocusAndBlurListener,Ba),r.removeEventListener("blur",this._rootNodeFocusAndBlurListener,Ba),this._rootNodeFocusListenerCount.delete(r))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(n,r,o){this._setClasses(n,r),this._emitOrigin(o,r),this._lastFocusOrigin=r}_getClosestElementsInfo(n){let r=[];return this._elementInfo.forEach((o,i)=>{(i===n||o.checkChildren&&i.contains(n))&&r.push([i,o])}),r}_isLastInteractionFromInputLabel(n){let{_mostRecentTarget:r,mostRecentModality:o}=this._inputModalityDetector;if(o!=="mouse"||!r||r===n||n.nodeName!=="INPUT"&&n.nodeName!=="TEXTAREA"||n.disabled)return!1;let i=n.labels;if(i){for(let s=0;s<i.length;s++)if(i[s].contains(r))return!0}return!1}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),XN=(()=>{class e{_elementRef=h(he);_focusMonitor=h(DD);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new Ce;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let n=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(n,n.nodeType===1&&n.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(r=>{this._focusOrigin=r,this.cdkFocusChange.emit(r)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=yt({type:e,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return e})();var Va=new WeakMap,ii=(()=>{class e{_appRef;_injector=h(Y);_environmentInjector=h(ge);load(n){let r=this._appRef=this._appRef||this._injector.get(Qe),o=Va.get(r);o||(o={loaders:new Set,refs:[]},Va.set(r,o),r.onDestroy(()=>{Va.get(r)?.refs.forEach(i=>i.destroy()),Va.delete(r)})),o.loaders.has(n)||(o.loaders.add(n),o.refs.push(sE(n,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Ua=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Lo({type:e,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(r,o){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return e})();function Of(e){return Array.isArray(e)?e:[e]}var _D=new Set,Hn,eA=(()=>{class e{_platform=h(Et);_nonce=h(Ir,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):nA}matchMedia(n){return(this._platform.WEBKIT||this._platform.BLINK)&&tA(n,this._nonce),this._matchMedia(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function tA(e,t){if(!_D.has(e))try{Hn||(Hn=document.createElement("style"),t&&Hn.setAttribute("nonce",t),Hn.setAttribute("type","text/css"),document.head.appendChild(Hn)),Hn.sheet&&(Hn.sheet.insertRule(`@media ${e} {body{ }}`,0),_D.add(e))}catch(n){console.error(n)}}function nA(e){return{matches:e==="all"||e==="",media:e,addListener:()=>{},removeListener:()=>{}}}var CD=(()=>{class e{_mediaMatcher=h(eA);_zone=h(F);_queries=new Map;_destroySubject=new H;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(n){return ID(Of(n)).some(o=>this._registerQuery(o).mql.matches)}observe(n){let o=ID(Of(n)).map(s=>this._registerQuery(s).observable),i=oc(o);return i=Yn(i.pipe(gn(1)),i.pipe(Zr(1),hn(0))),i.pipe(W(s=>{let a={matches:!1,breakpoints:{}};return s.forEach(({matches:c,query:u})=>{a.matches=a.matches||c,a.breakpoints[u]=c}),a}))}_registerQuery(n){if(this._queries.has(n))return this._queries.get(n);let r=this._mediaMatcher.matchMedia(n),i={observable:new R(s=>{let a=c=>this._zone.run(()=>s.next(c));return r.addListener(a),()=>{r.removeListener(a)}}).pipe(lc(r),W(({matches:s})=>({query:n,matches:s})),Yr(this._destroySubject)),mql:r};return this._queries.set(n,i),i}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ID(e){return e.map(t=>t.split(",")).reduce((t,n)=>t.concat(n)).map(t=>t.trim())}function rA(e){if(e.type==="characterData"&&e.target instanceof Comment)return!0;if(e.type==="childList"){for(let t=0;t<e.addedNodes.length;t++)if(!(e.addedNodes[t]instanceof Comment))return!1;for(let t=0;t<e.removedNodes.length;t++)if(!(e.removedNodes[t]instanceof Comment))return!1;return!0}return!1}var wD=(()=>{class e{create(n){return typeof MutationObserver>"u"?null:new MutationObserver(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bD=(()=>{class e{_mutationObserverFactory=h(wD);_observedElements=new Map;_ngZone=h(F);constructor(){}ngOnDestroy(){this._observedElements.forEach((n,r)=>this._cleanupObserver(r))}observe(n){let r=Lr(n);return new R(o=>{let s=this._observeElement(r).pipe(W(a=>a.filter(c=>!rA(c))),Ne(a=>!!a.length)).subscribe(a=>{this._ngZone.run(()=>{o.next(a)})});return()=>{s.unsubscribe(),this._unobserveElement(r)}})}_observeElement(n){return this._ngZone.runOutsideAngular(()=>{if(this._observedElements.has(n))this._observedElements.get(n).count++;else{let r=new H,o=this._mutationObserverFactory.create(i=>r.next(i));o&&o.observe(n,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(n,{observer:o,stream:r,count:1})}return this._observedElements.get(n).stream})}_unobserveElement(n){this._observedElements.has(n)&&(this._observedElements.get(n).count--,this._observedElements.get(n).count||this._cleanupObserver(n))}_cleanupObserver(n){if(this._observedElements.has(n)){let{observer:r,stream:o}=this._observedElements.get(n);r&&r.disconnect(),o.complete(),this._observedElements.delete(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Q8=(()=>{class e{_contentObserver=h(bD);_elementRef=h(he);event=new Ce;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._disabled?this._unsubscribe():this._subscribe()}_disabled=!1;get debounce(){return this._debounce}set debounce(n){this._debounce=hD(n),this._subscribe()}_debounce;_currentSubscription=null;constructor(){}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let n=this._contentObserver.observe(this._elementRef);this._currentSubscription=(this.debounce?n.pipe(hn(this.debounce)):n).subscribe(this.event)}_unsubscribe(){this._currentSubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=yt({type:e,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[2,"cdkObserveContentDisabled","disabled",Ar],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"]})}return e})(),TD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({providers:[wD]})}return e})();var oA=(()=>{class e{_platform=h(Et);constructor(){}isDisabled(n){return n.hasAttribute("disabled")}isVisible(n){return sA(n)&&getComputedStyle(n).visibility==="visible"}isTabbable(n){if(!this._platform.isBrowser)return!1;let r=iA(hA(n));if(r&&(MD(r)===-1||!this.isVisible(r)))return!1;let o=n.nodeName.toLowerCase(),i=MD(n);return n.hasAttribute("contenteditable")?i!==-1:o==="iframe"||o==="object"||this._platform.WEBKIT&&this._platform.IOS&&!fA(n)?!1:o==="audio"?n.hasAttribute("controls")?i!==-1:!1:o==="video"?i===-1?!1:i!==null?!0:this._platform.FIREFOX||n.hasAttribute("controls"):n.tabIndex>=0}isFocusable(n,r){return pA(n)&&!this.isDisabled(n)&&(r?.ignoreVisibility||this.isVisible(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function iA(e){try{return e.frameElement}catch{return null}}function sA(e){return!!(e.offsetWidth||e.offsetHeight||typeof e.getClientRects=="function"&&e.getClientRects().length)}function aA(e){let t=e.nodeName.toLowerCase();return t==="input"||t==="select"||t==="button"||t==="textarea"}function cA(e){return lA(e)&&e.type=="hidden"}function uA(e){return dA(e)&&e.hasAttribute("href")}function lA(e){return e.nodeName.toLowerCase()=="input"}function dA(e){return e.nodeName.toLowerCase()=="a"}function AD(e){if(!e.hasAttribute("tabindex")||e.tabIndex===void 0)return!1;let t=e.getAttribute("tabindex");return!!(t&&!isNaN(parseInt(t,10)))}function MD(e){if(!AD(e))return null;let t=parseInt(e.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}function fA(e){let t=e.nodeName.toLowerCase(),n=t==="input"&&e.type;return n==="text"||n==="password"||t==="select"||t==="textarea"}function pA(e){return cA(e)?!1:aA(e)||uA(e)||e.hasAttribute("contenteditable")||AD(e)}function hA(e){return e.ownerDocument&&e.ownerDocument.defaultView||window}var kf=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(t){this._enabled=t,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_enabled=!0;constructor(t,n,r,o,i=!1,s){this._element=t,this._checker=n,this._ngZone=r,this._document=o,this._injector=s,i||this.attachAnchors()}destroy(){let t=this._startAnchor,n=this._endAnchor;t&&(t.removeEventListener("focus",this.startAnchorListener),t.remove()),n&&(n.removeEventListener("focus",this.endAnchorListener),n.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusInitialElement(t)))})}focusFirstTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusFirstTabbableElement(t)))})}focusLastTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusLastTabbableElement(t)))})}_getRegionBoundary(t){let n=this._element.querySelectorAll(`[cdk-focus-region-${t}], [cdkFocusRegion${t}], [cdk-focus-${t}]`);return t=="start"?n.length?n[0]:this._getFirstTabbableElement(this._element):n.length?n[n.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(t){let n=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(n){if(!this._checker.isFocusable(n)){let r=this._getFirstTabbableElement(n);return r?.focus(t),!!r}return n.focus(t),!0}return this.focusFirstTabbableElement(t)}focusFirstTabbableElement(t){let n=this._getRegionBoundary("start");return n&&n.focus(t),!!n}focusLastTabbableElement(t){let n=this._getRegionBoundary("end");return n&&n.focus(t),!!n}hasAttached(){return this._hasAttached}_getFirstTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=0;r<n.length;r++){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(n[r]):null;if(o)return o}return null}_getLastTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=n.length-1;r>=0;r--){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(n[r]):null;if(o)return o}return null}_createAnchor(){let t=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,t),t.classList.add("cdk-visually-hidden"),t.classList.add("cdk-focus-trap-anchor"),t.setAttribute("aria-hidden","true"),t}_toggleAnchorTabIndex(t,n){t?n.setAttribute("tabindex","0"):n.removeAttribute("tabindex")}toggleAnchors(t){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_executeOnStable(t){this._injector?Sd(t,{injector:this._injector}):setTimeout(t)}},gA=(()=>{class e{_checker=h(oA);_ngZone=h(F);_document=h(O);_injector=h(Y);constructor(){h(ii).load(Ua)}create(n,r=!1){return new kf(n,this._checker,this._ngZone,this._document,r,this._injector)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var RD=new v("liveAnnouncerElement",{providedIn:"root",factory:xD});function xD(){return null}var OD=new v("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),mA=0,yA=(()=>{class e{_ngZone=h(F);_defaultOptions=h(OD,{optional:!0});_liveElement;_document=h(O);_previousTimeout;_currentPromise;_currentResolve;constructor(){let n=h(RD,{optional:!0});this._liveElement=n||this._createLiveElement()}announce(n,...r){let o=this._defaultOptions,i,s;return r.length===1&&typeof r[0]=="number"?s=r[0]:[i,s]=r,this.clear(),clearTimeout(this._previousTimeout),i||(i=o&&o.politeness?o.politeness:"polite"),s==null&&o&&(s=o.duration),this._liveElement.setAttribute("aria-live",i),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(a=>this._currentResolve=a)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=n,typeof s=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),s)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let n="cdk-live-announcer-element",r=this._document.getElementsByClassName(n),o=this._document.createElement("div");for(let i=0;i<r.length;i++)r[i].remove();return o.classList.add(n),o.classList.add("cdk-visually-hidden"),o.setAttribute("aria-atomic","true"),o.setAttribute("aria-live","polite"),o.id=`cdk-live-announcer-${mA++}`,this._document.body.appendChild(o),o}_exposeAnnouncerToModals(n){let r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let o=0;o<r.length;o++){let i=r[o],s=i.getAttribute("aria-owns");s?s.indexOf(n)===-1&&i.setAttribute("aria-owns",s+" "+n):i.setAttribute("aria-owns",n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var nn=function(e){return e[e.NONE=0]="NONE",e[e.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",e[e.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",e}(nn||{}),SD="cdk-high-contrast-black-on-white",ND="cdk-high-contrast-white-on-black",Ff="cdk-high-contrast-active",Ha=(()=>{class e{_platform=h(Et);_hasCheckedHighContrastMode;_document=h(O);_breakpointSubscription;constructor(){this._breakpointSubscription=h(CD).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return nn.NONE;let n=this._document.createElement("div");n.style.backgroundColor="rgb(1,2,3)",n.style.position="absolute",this._document.body.appendChild(n);let r=this._document.defaultView||window,o=r&&r.getComputedStyle?r.getComputedStyle(n):null,i=(o&&o.backgroundColor||"").replace(/ /g,"");switch(n.remove(),i){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return nn.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return nn.BLACK_ON_WHITE}return nn.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let n=this._document.body.classList;n.remove(Ff,SD,ND),this._hasCheckedHighContrastMode=!0;let r=this.getHighContrastMode();r===nn.BLACK_ON_WHITE?n.add(Ff,SD):r===nn.WHITE_ON_BLACK&&n.add(Ff,ND)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vA=(()=>{class e{constructor(){h(Ha)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({imports:[TD]})}return e})();var Pf={},EA=(()=>{class e{_appId=h(At);getId(n){return this._appId!=="ng"&&(n+=this._appId),Pf.hasOwnProperty(n)||(Pf[n]=0),`${n}${Pf[n]++}`}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var DA=200,$a=class{_letterKeyStream=new H;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new H;selectedItem=this._selectedItem;constructor(t,n){let r=typeof n?.debounceInterval=="number"?n.debounceInterval:DA;n?.skipPredicate&&(this._skipPredicateFn=n.skipPredicate),this.setItems(t),this._setupKeyHandler(r)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){let n=t.keyCode;t.key&&t.key.length===1?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(n>=65&&n<=90||n>=48&&n<=57)&&this._letterKeyStream.next(String.fromCharCode(n))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe(_t(n=>this._pressedLetters.push(n)),hn(t),Ne(()=>this._pressedLetters.length>0),W(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(n=>{for(let r=1;r<this._items.length+1;r++){let o=(this._selectedItemIndex+r)%this._items.length,i=this._items[o];if(!this._skipPredicateFn?.(i)&&i.getLabel?.().toLocaleUpperCase().trim().indexOf(n)===0){this._selectedItem.next(i);break}}this._pressedLetters=[]})}};function FD(e,...t){return t.length?t.some(n=>e[n]):e.altKey||e.shiftKey||e.ctrlKey||e.metaKey}var jr=class{_items;_activeItemIndex=pt(-1);_activeItem=pt(null);_wrap=!1;_typeaheadSubscription=Z.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=t=>t.disabled;constructor(t,n){this._items=t,t instanceof Ln?this._itemChangesSubscription=t.changes.subscribe(r=>this._itemsChanged(r.toArray())):sr(t)&&(this._effectRef=Jd(()=>this._itemsChanged(t()),{injector:n}))}tabOut=new H;change=new H;skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();let n=this._getItemsArray();return this._typeahead=new $a(n,{debounceInterval:typeof t=="number"?t:void 0,skipPredicate:r=>this._skipPredicateFn(r)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(r=>{this.setActiveItem(r)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,n=10){return this._pageUpAndDown={enabled:t,delta:n},this}setActiveItem(t){let n=this._activeItem();this.updateActiveItem(t),this._activeItem()!==n&&this.change.next(this._activeItemIndex())}onKeydown(t){let n=t.keyCode,o=["altKey","ctrlKey","metaKey","shiftKey"].every(i=>!t[i]||this._allowedModifierKeys.indexOf(i)>-1);switch(n){case 9:this.tabOut.next();return;case 40:if(this._vertical&&o){this.setNextItemActive();break}else return;case 38:if(this._vertical&&o){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&o){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&o){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&o){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&o){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex()-this._pageUpAndDown.delta;this._setActiveItemByIndex(i>0?i:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex()+this._pageUpAndDown.delta,s=this._getItemsArray().length;this._setActiveItemByIndex(i<s?i:s-1,-1);break}else return;default:(o||FD(t,"shiftKey"))&&this._typeahead?.handleKey(t);return}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex()}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex()<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex()<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){let n=this._getItemsArray(),r=typeof t=="number"?t:n.indexOf(t),o=n[r];this._activeItem.set(o??null),this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){let n=this._getItemsArray();for(let r=1;r<=n.length;r++){let o=(this._activeItemIndex()+t*r+n.length)%n.length,i=n[o];if(!this._skipPredicateFn(i)){this.setActiveItem(o);return}}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex()+t,t)}_setActiveItemByIndex(t,n){let r=this._getItemsArray();if(r[t]){for(;this._skipPredicateFn(r[t]);)if(t+=n,!r[t])return;this.setActiveItem(t)}}_getItemsArray(){return sr(this._items)?this._items():this._items instanceof Ln?this._items.toArray():this._items}_itemsChanged(t){this._typeahead?.setItems(t);let n=this._activeItem();if(n){let r=t.indexOf(n);r>-1&&r!==this._activeItemIndex()&&(this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r))}}};var Lf=class extends jr{setActiveItem(t){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(t),this.activeItem&&this.activeItem.setActiveStyles()}};var jf=class extends jr{_origin="program";setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}};var PD=" ";function _A(e,t,n){let r=Ga(e,t);n=n.trim(),!r.some(o=>o.trim()===n)&&(r.push(n),e.setAttribute(t,r.join(PD)))}function IA(e,t,n){let r=Ga(e,t);n=n.trim();let o=r.filter(i=>i!==n);o.length?e.setAttribute(t,o.join(PD)):e.removeAttribute(t)}function Ga(e,t){return e.getAttribute(t)?.match(/\S+/g)??[]}var LD="cdk-describedby-message",za="cdk-describedby-host",Vf=0,qz=(()=>{class e{_platform=h(Et);_document=h(O);_messageRegistry=new Map;_messagesContainer=null;_id=`${Vf++}`;constructor(){h(ii).load(Ua),this._id=h(At)+"-"+Vf++}describe(n,r,o){if(!this._canBeDescribed(n,r))return;let i=Bf(r,o);typeof r!="string"?(kD(r,this._id),this._messageRegistry.set(i,{messageElement:r,referenceCount:0})):this._messageRegistry.has(i)||this._createMessageElement(r,o),this._isElementDescribedByMessage(n,i)||this._addMessageReference(n,i)}removeDescription(n,r,o){if(!r||!this._isElementNode(n))return;let i=Bf(r,o);if(this._isElementDescribedByMessage(n,i)&&this._removeMessageReference(n,i),typeof r=="string"){let s=this._messageRegistry.get(i);s&&s.referenceCount===0&&this._deleteMessageElement(i)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let n=this._document.querySelectorAll(`[${za}="${this._id}"]`);for(let r=0;r<n.length;r++)this._removeCdkDescribedByReferenceIds(n[r]),n[r].removeAttribute(za);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(n,r){let o=this._document.createElement("div");kD(o,this._id),o.textContent=n,r&&o.setAttribute("role",r),this._createMessagesContainer(),this._messagesContainer.appendChild(o),this._messageRegistry.set(Bf(n,r),{messageElement:o,referenceCount:0})}_deleteMessageElement(n){this._messageRegistry.get(n)?.messageElement?.remove(),this._messageRegistry.delete(n)}_createMessagesContainer(){if(this._messagesContainer)return;let n="cdk-describedby-message-container",r=this._document.querySelectorAll(`.${n}[platform="server"]`);for(let i=0;i<r.length;i++)r[i].remove();let o=this._document.createElement("div");o.style.visibility="hidden",o.classList.add(n),o.classList.add("cdk-visually-hidden"),this._platform.isBrowser||o.setAttribute("platform","server"),this._document.body.appendChild(o),this._messagesContainer=o}_removeCdkDescribedByReferenceIds(n){let r=Ga(n,"aria-describedby").filter(o=>o.indexOf(LD)!=0);n.setAttribute("aria-describedby",r.join(" "))}_addMessageReference(n,r){let o=this._messageRegistry.get(r);_A(n,"aria-describedby",o.messageElement.id),n.setAttribute(za,this._id),o.referenceCount++}_removeMessageReference(n,r){let o=this._messageRegistry.get(r);o.referenceCount--,IA(n,"aria-describedby",o.messageElement.id),n.removeAttribute(za)}_isElementDescribedByMessage(n,r){let o=Ga(n,"aria-describedby"),i=this._messageRegistry.get(r),s=i&&i.messageElement.id;return!!s&&o.indexOf(s)!=-1}_canBeDescribed(n,r){if(!this._isElementNode(n))return!1;if(r&&typeof r=="object")return!0;let o=r==null?"":`${r}`.trim(),i=n.getAttribute("aria-label");return o?!i||i.trim()!==o:!1}_isElementNode(n){return n.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Bf(e,t){return typeof e=="string"?`${t||""}/${e}`:e}function kD(e,t){e.id||(e.id=`${LD}-${t}-${Vf++}`)}var CA=new v("cdk-dir-doc",{providedIn:"root",factory:wA});function wA(){return h(O)}var bA=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function jD(e){let t=e?.toLowerCase()||"";return t==="auto"&&typeof navigator<"u"&&navigator?.language?bA.test(navigator.language)?"rtl":"ltr":t==="rtl"?"rtl":"ltr"}var TA=(()=>{class e{get value(){return this.valueSignal()}valueSignal=pt("ltr");change=new Ce;constructor(){let n=h(CA,{optional:!0});if(n){let r=n.body?n.body.dir:null,o=n.documentElement?n.documentElement.dir:null;this.valueSignal.set(jD(r||o||"ltr"))}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Uf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({})}return e})();var Hf=(()=>{class e{constructor(){h(Ha)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({imports:[Uf,Uf]})}return e})();var MA=["*"],SA=new v("MAT_ICON_DEFAULT_OPTIONS"),NA=new v("mat-icon-location",{providedIn:"root",factory:AA});function AA(){let e=h(O),t=e?e.location:null;return{getPathname:()=>t?t.pathname+t.search:""}}var BD=["clip-path","color-profile","src","cursor","fill","filter","marker","marker-start","marker-mid","marker-end","mask","stroke"],RA=BD.map(e=>`[${e}]`).join(", "),xA=/^url\(['"]?#(.*?)['"]?\)$/,_G=(()=>{class e{_elementRef=h(he);_iconRegistry=h(dD);_location=h(NA);_errorHandler=h(fe);_defaultColor;get color(){return this._color||this._defaultColor}set color(n){this._color=n}_color;inline=!1;get svgIcon(){return this._svgIcon}set svgIcon(n){n!==this._svgIcon&&(n?this._updateSvgIcon(n):this._svgIcon&&this._clearSvgElement(),this._svgIcon=n)}_svgIcon;get fontSet(){return this._fontSet}set fontSet(n){let r=this._cleanupFontValue(n);r!==this._fontSet&&(this._fontSet=r,this._updateFontIconClasses())}_fontSet;get fontIcon(){return this._fontIcon}set fontIcon(n){let r=this._cleanupFontValue(n);r!==this._fontIcon&&(this._fontIcon=r,this._updateFontIconClasses())}_fontIcon;_previousFontSetClass=[];_previousFontIconClass;_svgName;_svgNamespace;_previousPath;_elementsWithExternalReferences;_currentIconFetch=Z.EMPTY;constructor(){let n=h(new ma("aria-hidden"),{optional:!0}),r=h(SA,{optional:!0});r&&(r.color&&(this.color=this._defaultColor=r.color),r.fontSet&&(this.fontSet=r.fontSet)),n||this._elementRef.nativeElement.setAttribute("aria-hidden","true")}_splitIconName(n){if(!n)return["",""];let r=n.split(":");switch(r.length){case 1:return["",r[0]];case 2:return r;default:throw Error(`Invalid icon name: "${n}"`)}}ngOnInit(){this._updateFontIconClasses()}ngAfterViewChecked(){let n=this._elementsWithExternalReferences;if(n&&n.size){let r=this._location.getPathname();r!==this._previousPath&&(this._previousPath=r,this._prependPathToReferences(r))}}ngOnDestroy(){this._currentIconFetch.unsubscribe(),this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear()}_usingFontIcon(){return!this.svgIcon}_setSvgElement(n){this._clearSvgElement();let r=this._location.getPathname();this._previousPath=r,this._cacheChildrenWithExternalReferences(n),this._prependPathToReferences(r),this._elementRef.nativeElement.appendChild(n)}_clearSvgElement(){let n=this._elementRef.nativeElement,r=n.childNodes.length;for(this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear();r--;){let o=n.childNodes[r];(o.nodeType!==1||o.nodeName.toLowerCase()==="svg")&&o.remove()}}_updateFontIconClasses(){if(!this._usingFontIcon())return;let n=this._elementRef.nativeElement,r=(this.fontSet?this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/):this._iconRegistry.getDefaultFontSetClass()).filter(o=>o.length>0);this._previousFontSetClass.forEach(o=>n.classList.remove(o)),r.forEach(o=>n.classList.add(o)),this._previousFontSetClass=r,this.fontIcon!==this._previousFontIconClass&&!r.includes("mat-ligature-font")&&(this._previousFontIconClass&&n.classList.remove(this._previousFontIconClass),this.fontIcon&&n.classList.add(this.fontIcon),this._previousFontIconClass=this.fontIcon)}_cleanupFontValue(n){return typeof n=="string"?n.trim().split(" ")[0]:n}_prependPathToReferences(n){let r=this._elementsWithExternalReferences;r&&r.forEach((o,i)=>{o.forEach(s=>{i.setAttribute(s.name,`url('${n}#${s.value}')`)})})}_cacheChildrenWithExternalReferences(n){let r=n.querySelectorAll(RA),o=this._elementsWithExternalReferences=this._elementsWithExternalReferences||new Map;for(let i=0;i<r.length;i++)BD.forEach(s=>{let a=r[i],c=a.getAttribute(s),u=c?c.match(xA):null;if(u){let l=o.get(a);l||(l=[],o.set(a,l)),l.push({name:s,value:u[1]})}})}_updateSvgIcon(n){if(this._svgNamespace=null,this._svgName=null,this._currentIconFetch.unsubscribe(),n){let[r,o]=this._splitIconName(n);r&&(this._svgNamespace=r),o&&(this._svgName=o),this._currentIconFetch=this._iconRegistry.getNamedSvgIcon(o,r).pipe(gn(1)).subscribe(i=>this._setSvgElement(i),i=>{let s=`Error retrieving icon ${r}:${o}! ${i.message}`;this._errorHandler.handleError(new Error(s))})}}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Lo({type:e,selectors:[["mat-icon"]],hostAttrs:["role","img",1,"mat-icon","notranslate"],hostVars:10,hostBindings:function(r,o){r&2&&(jo("data-mat-icon-type",o._usingFontIcon()?"font":"svg")("data-mat-icon-name",o._svgName||o.fontIcon)("data-mat-icon-namespace",o._svgNamespace||o.fontSet)("fontIcon",o._usingFontIcon()?o.fontIcon:null),Zd(o.color?"mat-"+o.color:""),pa("mat-icon-inline",o.inline)("mat-icon-no-color",o.color!=="primary"&&o.color!=="accent"&&o.color!=="warn"))},inputs:{color:"color",inline:[2,"inline","inline",Ar],svgIcon:"svgIcon",fontSet:"fontSet",fontIcon:"fontIcon"},exportAs:["matIcon"],ngContentSelectors:MA,decls:1,vars:0,template:function(r,o){r&1&&(Wd(),qd(0))},styles:[`mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}
`],encapsulation:2,changeDetection:0})}return e})(),IG=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ye({type:e});static \u0275inj=pe({imports:[Hf,Hf]})}return e})();export{Z as a,$D as b,R as c,ec as d,tc as e,H as f,un as g,ln as h,je as i,be as j,rc as k,e_ as l,fn as m,W as n,oc as o,pn as p,zr as q,Yn as r,u_ as s,ic as t,$r as u,WD as v,l_ as w,Ne as x,d_ as y,xi as z,sc as A,hn as B,Gr as C,gn as D,Wr as E,h_ as F,cc as G,g_ as H,m_ as I,y_ as J,v_ as K,Zr as L,lc as M,dc as N,Yr as O,E_ as P,_t as Q,_ as R,Qi as S,E as T,pe as U,S_ as V,v as W,C as X,h as Y,it as Z,ge as _,nr as $,eh as aa,th as ba,gh as ca,mh as da,Y as ea,O as fa,He as ga,qe as ha,X_ as ia,sr as ja,pt as ka,Zt as la,Rl as ma,$s as na,TI as oa,Fl as pa,he as qa,Ln as ra,Ll as sa,At as ta,zs as ua,Qt as va,PI as wa,Ir as xa,Cr as ya,$l as za,Fg as Aa,pC as Ba,Xg as Ca,gC as Da,jC as Ea,gr as Fa,Nt as Ga,ca as Ha,ze as Ia,sb as Ja,Nr as Ka,ke as La,Bn as Ma,wy as Na,Ty as Oa,Lo as Pa,ye as Qa,yt as Ra,Ny as Sa,Ry as Ta,xy as Ua,Ce as Va,F as Wa,Sd as Xa,Hy as Ya,xd as Za,$y as _a,da as $a,fa as ab,Qe as bb,jo as cb,hT as db,gT as eb,mT as fb,yT as gb,vT as hb,ET as ib,qy as jb,Ud as kb,Hd as lb,Zy as mb,$d as nb,zd as ob,Yy as pb,bT as qb,Jy as rb,ev as sb,tv as tb,AT as ub,Wd as vb,qd as wb,OT as xb,FT as yb,kT as zb,PT as Ab,LT as Bb,jT as Cb,BT as Db,VT as Eb,iv as Fb,pa as Gb,Zd as Hb,aM as Ib,hv as Jb,Yd as Kb,gv as Lb,yv as Mb,lM as Nb,vv as Ob,dM as Pb,Dv as Qb,vM as Rb,DM as Sb,IM as Tb,TM as Ub,MM as Vb,NM as Wb,Vo as Xb,wv as Yb,bv as Zb,Jd as _b,ma as $b,JM as ac,KU as bc,QU as cc,JU as dc,rS as ec,$v as fc,zv as gc,Zv as hc,dS as ic,XU as jc,Ar as kc,CS as lc,eH as mc,tH as nc,nH as oc,sE as pc,rH as qc,oH as rc,iH as sc,sH as tc,xt as uc,rf as vc,of as wc,Ea as xc,fE as yc,hE as zc,US as Ac,HS as Bc,GS as Cc,AE as Dc,Un as Ec,M2 as Fc,RE as Gc,S2 as Hc,lf as Ic,ei as Jc,kr as Kc,Tf as Lc,ka as Mc,LN as Nc,yf as Oc,hN as Pc,gN as Qc,G$ as Rc,W$ as Sc,q$ as Tc,Et as Uc,pD as Vc,n8 as Wc,Pr as Xc,Sf as Yc,Nf as Zc,hD as _c,Lr as $c,DD as ad,XN as bd,ii as cd,Ua as dd,Of as ed,eA as fd,CD as gd,Q8 as hd,TD as id,oA as jd,gA as kd,yA as ld,vA as md,EA as nd,FD as od,Lf as pd,jf as qd,_A as rd,IA as sd,qz as td,TA as ud,Uf as vd,Hf as wd,_G as xd,IG as yd};
